import React, { useState, useEffect, useMemo } from 'react';
import type { Packages, CartItem, PackageItem, BillingType, BillingOption } from '@/types';
import PackageCard from '@/components/PackageCard';
import SkeletonCard from '@/components/skeletons/SkeletonCard';
import { useAdminActions, useStripeProducts } from '@/hooks';
import { useQueryClient } from '@tanstack/react-query';
import { transformPackageToDatabasePackage } from '@/lib/dataTransformers';
import { useToastStore } from '@/stores/useToastStore';
import { ConfirmationModal } from '@/components/ui';
import { supabase } from '@/lib/supabaseClient';

interface PackageTabProps {
  pageTitle: string;
  description: string;
  packages: PackageItem[];
  category: string;
  cart: CartItem[];
  addToCart: (packageItem: PackageItem, category: string, finalPrice: number, finalBilling: string) => void;
  isLoading: boolean;
  billingType?: BillingType;
  selectedBillingOption?: BillingOption;
  onBillingOptionChange: (billingTypeId: string, optionId: string) => void;
  isAdmin?: boolean;
}

const isItemVisible = (item: { isEnabled?: boolean, validFrom?: string | null, validTo?: string | null }) => {
    if (item.isEnabled === false) return false;
    const now = new Date();
    const from = item.validFrom ? new Date(item.validFrom) : null;
    const to = item.validTo ? new Date(item.validTo) : null;
    if (from && now < from) return false;
    if (to && now > to) return false;
    return true;
};

const DynamicBillingToggle: React.FC<Pick<PackageTabProps, 'billingType' | 'selectedBillingOption' | 'onBillingOptionChange' | 'isAdmin'>> = ({ billingType, selectedBillingOption, onBillingOptionChange, isAdmin }) => {
    if (!billingType || !billingType.options.length) {
        return null;
    }
    
    const visibleOptions = isAdmin ? billingType.options : billingType.options.filter(isItemVisible);

    if (visibleOptions.length === 0) return null;

    const benefitDisplay = React.useMemo(() => {
        if (!selectedBillingOption) return null;

        const parts = [];
        if (selectedBillingOption.discount > 0) {
            parts.push(`Save ${selectedBillingOption.discount}%`);
        }
        if (selectedBillingOption.benefitText) {
            parts.push(selectedBillingOption.benefitText);
        }
        
        if (parts.length === 0) return null;
        
        return `✓ ${parts.join(' ✓ ')}`;
    }, [selectedBillingOption]);

    return (
        <div className="bg-white border border-gray-300 p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                    <h3 className="text-lg font-semibold mb-1 text-black">{billingType.publicTitle || `Choose Your ${billingType.name}`}</h3>
                    <p className="text-gray-600 text-sm">{billingType.description || 'Select a plan that works for you.'}</p>
                </div>
                <div className="flex flex-wrap gap-2">
                    {visibleOptions.map((option) => (
                        <button
                            key={option.id}
                            onClick={() => onBillingOptionChange(billingType.id, option.id)}
                            className={`px-3 sm:px-4 py-2 text-sm font-medium border transition-colors whitespace-nowrap ${selectedBillingOption?.id === option.id ? 'bg-black text-white border-black' : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'}`}
                        >
                            {option.label}
                            {option.discount > 0 && (
                                <span className={`ml-2 text-xs font-semibold ${selectedBillingOption?.id === option.id ? 'text-gray-300' : 'text-green-600'}`}>
                                    {option.discount}% OFF
                                </span>
                            )}
                        </button>
                    ))}
                </div>
            </div>
            {benefitDisplay && (
                <div className="mt-4 inline-block text-sm font-medium text-green-700 bg-green-50 p-2 border border-green-200">
                    {benefitDisplay}
                </div>
            )}
        </div>
    );
};


const PackageTab: React.FC<PackageTabProps> = ({ 
  pageTitle, 
  description, 
  packages: packageList, 
  category, 
  cart, 
  addToCart, 
  isLoading, 
  billingType, 
  selectedBillingOption, 
  onBillingOptionChange,
  isAdmin
}) => {
  const [orderedPackages, setOrderedPackages] = useState<PackageItem[]>([]);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null);
  const [deleteCandidate, setDeleteCandidate] = useState<string | null>(null);

  const adminActions = useAdminActions();
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();
  const { data: stripeProducts = [] } = useStripeProducts();
  const stripeProductMap = useMemo(() => new Map(stripeProducts.map(p => [p.package_id, p])), [stripeProducts]);
  const [syncingPackageId, setSyncingPackageId] = useState<string | null>(null);


  useEffect(() => {
    const sorted = [...packageList].sort((a, b) => (a.orderIndex ?? 0) - (b.orderIndex ?? 0));
    setOrderedPackages(sorted);
  }, [packageList]);

  const handleDragStart = (index: number) => {
    if (!isAdmin) return;
    setDraggedIndex(index);
  };

  const handleDragEnter = (index: number) => {
    if (draggedIndex !== null && draggedIndex !== index) {
      setDropTargetIndex(index);
    }
  };

  const handleDeletePackage = (id: string) => {
    setDeleteCandidate(id);
  };

  const confirmDeletePackage = async () => {
    if (!deleteCandidate) return;
    try {
        await adminActions.deleteRecord('packages', deleteCandidate);
        queryClient.invalidateQueries({ queryKey: ['packages'] });
        queryClient.invalidateQueries({ queryKey: ['stripe-products'] });
        showToast('Package deleted.', 'success');
    } catch(e) {
        const message = e instanceof Error ? e.message : String(e);
        showToast(`Failed to delete package: ${message}`, 'error');
    } finally {
        setDeleteCandidate(null);
    }
  };


  const handleDrop = async (dropIndex: number) => {
    if (!isAdmin || draggedIndex === null || draggedIndex === dropIndex) {
      handleDragEnd();
      return;
    }
  
    const newPackages = [...orderedPackages];
    const [draggedItem] = newPackages.splice(draggedIndex, 1);
    newPackages.splice(dropIndex, 0, draggedItem);
  
    setOrderedPackages(newPackages); // Optimistic update
    handleDragEnd();
  
    try {
      const updatePromises = newPackages.map((pkg, index) => {
        const updatedPackageData = { ...pkg, orderIndex: index, categoryId: category };
        return adminActions.updateRecord('packages', updatedPackageData, pkg.id);
      });
      await Promise.all(updatePromises);
      await queryClient.invalidateQueries({ queryKey: ['packages'] });
    } catch (error) {
      console.error("Failed to reorder packages:", error);
      showToast("Error saving new package order.", 'error');
      // Revert to original order on failure
      const sorted = [...packageList].sort((a, b) => (a.orderIndex ?? 0) - (b.orderIndex ?? 0));
      setOrderedPackages(sorted);
    }
  };
  
  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDropTargetIndex(null);
  };

  const handleSyncStripe = async (packageId: string) => {
    setSyncingPackageId(packageId);
    try {
        const { data, error } = await supabase.functions.invoke('stripe-product-sync', {
            body: { packageId },
        });
        if (error) throw error;
        if (data.error) throw new Error(data.error);
        showToast(data.message || 'Successfully synced with Stripe!', 'success');
        await queryClient.invalidateQueries({ queryKey: ['stripe-products'] });
    } catch (err: any) {
        console.error('Failed to sync with Stripe', err);
        showToast(`Stripe Sync Failed: ${err.message}`, 'error');
    } finally {
        setSyncingPackageId(null);
    }
  };

  const packageToDelete = deleteCandidate ? packageList.find(p => p.id === deleteCandidate) : null;

  return (
    <>
    <div className="max-w-7xl mx-auto space-y-12">
      <DynamicBillingToggle 
        billingType={billingType}
        selectedBillingOption={selectedBillingOption}
        onBillingOptionChange={onBillingOptionChange}
        isAdmin={isAdmin}
      />

      <div onDragEnd={handleDragEnd}>
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2 text-black">{pageTitle}</h1>
          {description && <p className="text-gray-600 max-w-3xl">{description}</p>}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {isLoading ? (
            Array.from({ length: packageList.length || 3 }).map((_, index) => <SkeletonCard key={index} />)
          ) : (
            orderedPackages.map((pkg, index) => (
              <PackageCard
                key={pkg.id}
                packageItem={pkg}
                category={category}
                cart={cart}
                addToCart={addToCart}
                selectedBillingOption={selectedBillingOption}
                isAdmin={isAdmin}
                index={index}
                isDragging={draggedIndex === index}
                isDropTarget={dropTargetIndex === index}
                onDragStart={handleDragStart}
                onDrop={handleDrop}
                onDragEnter={handleDragEnter}
                onEdit={() => {}}
                onDelete={handleDeletePackage}
                stripeProductInfo={stripeProductMap.get(pkg.id)}
                onSyncStripe={handleSyncStripe}
                isSyncing={syncingPackageId === pkg.id}
              />
            ))
          )}
        </div>
      </div>
    </div>
    <ConfirmationModal
        isOpen={!!deleteCandidate}
        onClose={() => setDeleteCandidate(null)}
        onConfirm={confirmDeletePackage}
        title="Delete Package"
        message={
            <p>
                Are you sure you want to delete the package <strong>"{packageToDelete?.name}"</strong>? This will also archive the product in Stripe if it exists.
            </p>
        }
        confirmText="Delete Package"
    />
    </>
  );
};

export default PackageTab;