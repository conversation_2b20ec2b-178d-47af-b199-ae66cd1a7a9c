import React from 'react';

interface PasswordStrengthMeterProps {
  score: number; // Score from 0 to 4
}

const strengthLevels = [
  { text: '', barColor: 'bg-gray-200', textColor: 'text-gray-500' }, // score 0
  { text: 'Weak', barColor: 'bg-red-500', textColor: 'text-red-500' }, // score 1
  { text: 'Medium', barColor: 'bg-orange-500', textColor: 'text-orange-500' }, // score 2
  { text: 'Good', barColor: 'bg-yellow-500', textColor: 'text-yellow-500' }, // score 3
  { text: 'Strong', barColor: 'bg-green-500', textColor: 'text-green-500' }, // score 4
];

const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({ score }) => {
  const currentStrength = strengthLevels[score] || strengthLevels[0];

  return (
    <div>
      <div className="flex gap-1 h-1.5 mt-2">
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className={`flex-1 rounded-full transition-colors ${score > index + 1 ? currentStrength.barColor : (score > index ? currentStrength.barColor : 'bg-gray-200')}`}
          />
        ))}
      </div>
      {score > 0 && (
         <p className={`text-xs mt-1 font-medium ${currentStrength.textColor}`}>
            Strength: {currentStrength.text}
         </p>
      )}
    </div>
  );
};

export default PasswordStrengthMeter;
