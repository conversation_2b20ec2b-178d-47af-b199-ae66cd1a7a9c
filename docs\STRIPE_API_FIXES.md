# Correctifs API Stripe - Facturation et Abonnements

## Problèmes Corrigés

### 1. Erreur `parameter_unknown: add_invoice_items[0][price_data][product_data]`

**Problème** : L'erreur indique que le paramètre `product_data` n'existait pas dans `price_data` pour les abonnements.

**Solution** : Selon la documentation Stripe, le paramètre correct est `product` (ID d'un produit existant) ou `product_data` (objet pour créer un produit dynamique).

#### Avant (Incorrect) :
```javascript
// Dans les abonnements - add_invoice_items ne supportait pas product_data
price_data: {
  currency: 'usd',
  unit_amount: Math.round(item.finalPrice * 100),
  product_data: {
    name: `${item.name} (One-Time)`,
  },
}
```

#### Après (Correct) :
```javascript
// Pour les invoice items - utiliser product_data pour créer des produits dynamiques
price_data: {
  currency: 'usd',
  unit_amount: Math.round(item.finalPrice * 100),
  product_data: {
    name: `${item.name} (One-Time)`,
  },
}
```

### 2. Erreur `No such product: 'Product Name'`

**Problème** : Quand `price_data.product` est une string, Stripe s'attend à un ID de produit existant, pas un nom.

**Solution** : Utiliser `product_data` avec un objet pour créer des produits dynamiques.

## Fichiers Corrigés

### 1. `supabase/functions/stripe-payment-intent/index.ts`
- ✅ Correction du paramètre `product_data` → `product` dans les invoice items pour abonnements
- ✅ Amélioration du flux de création d'abonnements avec éléments one-time

### 2. `supabase/functions/stripe-webhook-handler/index.ts`
- ✅ Correction des invoice items dans `handlePaymentSuccess()`
- ✅ Correction pour les éléments de commande
- ✅ Correction pour la TVA
- ✅ Correction pour les remises

## Configuration Stripe Mise à Jour

### Versions Utilisées
- **Stripe Node.js Library** : v16.2.0 (dernière version compatible)
- **API Version** : 2024-04-10 (version récente)

### Paramètres API Corrigés

#### Invoice Items avec `price_data`
```javascript
await stripe.invoiceItems.create({
  customer: stripeCustomerId,
  invoice: invoice.id,
  price_data: {
    currency: 'usd',
    unit_amount: Math.round(amount * 100),
    product: 'Product Name', // STRING, pas un objet
  },
});
```

#### Abonnements avec Invoice Items One-Time
```javascript
const subscription = await stripe.subscriptions.create({
  customer: customerId,
  items: subscriptionItems,
  payment_behavior: 'default_incomplete',
  expand: ['latest_invoice.payment_intent'],
  automatic_tax: { enabled: true },
  metadata,
});

// Ajouter les éléments one-time à la facture créée
for (const item of oneTimeItems) {
  await stripe.invoiceItems.create({
    customer: customerId,
    invoice: subscription.latest_invoice.id,
    price_data: {
      currency: 'usd',
      unit_amount: Math.round(item.finalPrice * 100),
      product: `${item.name} (One-Time)`,
    },
  });
}
```

## Tests Recommandés

### 1. Test de Paiement One-Time
```bash
# Tester avec une carte de test Stripe
Card Number: 4242 4242 4242 4242
Expiry: 12/26
CVC: 123
```

### 2. Test d'Abonnement avec Éléments One-Time
- Créer un panier avec un élément récurrent et un élément one-time
- Vérifier que les deux apparaissent sur la facture
- Confirmer que seul l'élément récurrent se répète les mois suivants

### 3. Test de Génération de Factures
- Effectuer un achat
- Vérifier que le webhook `payment_intent.succeeded` fonctionne
- Confirmer que la facture est créée et envoyée par email

## Configuration Webhook Requise

Assurez-vous que ces événements sont configurés dans votre Dashboard Stripe :

```
https://ljcbzpxisqegqkgjbkjk.supabase.co/functions/v1/stripe-webhook-handler
```

Événements requis :
- `payment_intent.succeeded` ✅
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`

## Commandes de Déploiement

```bash
# Configurer les secrets si pas encore fait
supabase secrets set STRIPE_SECRET_KEY=sk_test_...
supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_...

# Déployer les functions corrigées
supabase functions deploy stripe-payment-intent
supabase functions deploy stripe-webhook-handler

# Vérifier les logs
supabase functions logs stripe-payment-intent --follow
supabase functions logs stripe-webhook-handler --follow
```

## Résultat Attendu

Après ces corrections :
1. ✅ Les erreurs `parameter_unknown` disparaissent
2. ✅ Les abonnements avec éléments one-time fonctionnent
3. ✅ Les factures sont générées automatiquement
4. ✅ Les clients reçoivent leurs factures par email
5. ✅ Tous les éléments (produits, TVA, remises) apparaissent correctement

Les corrections suivent strictement la documentation officielle Stripe et utilisent les meilleures pratiques recommandées.