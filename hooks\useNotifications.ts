import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { NotificationService } from '../lib/supabaseService';

export const useNotifications = () => {
  return useQuery({
    queryKey: ['notifications'],
    queryFn: NotificationService.getAllUnreadAdmin,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 1 * 60 * 1000,
  });
};

export const useMarkNotificationAsRead = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => NotificationService.markAsRead(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
        },
    });
};
