{"mcpServers": {"context7": {"type": "stdio", "command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp", "--api-key", "ctx7sk-6d485648-d55c-4f42-ad4b-ca6ce637cfe2"], "env": {"NODE_EXTRA_CA_CERTS": "C:/Program Files (x86)/Microsoft SDKs/Azure/CLI2/Lib/site-packages/certifi/bundleNS.pem"}}, "supabase": {"type": "stdio", "command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase", "--read-only", "--project-ref=oxcuslfuxhqkejxkxrml"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "stripe": {"type": "stdio", "command": "cmd", "args": ["/c", "npx", "-y", "@stripe/mcp", "--tools=all"], "env": {"STRIPE_SECRET_KEY": "sk_test_51S9mHMPnfmQanShPWTlAhftgoAHiAUhy6ME2t1wnPnjpBvaRL6udNwpbuGY26XrqnZ22zKG4H2qgFYnspNczJRiW00R5d8paCR"}}}}