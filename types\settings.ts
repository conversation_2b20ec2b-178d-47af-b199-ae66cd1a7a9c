export interface PaymentSettings {
    stripe: {
        enabled: boolean;
        publicKey: string;
        secretKey: string;
        methods: {
            creditCard: boolean;
            applePay: boolean;
            googlePay: boolean;
        }
    }
}

export interface SmtpSettings {
    testMode: boolean;
    server: string;
    port: number;
    username: string;
    password: string;
    encryption: 'none' | 'ssl' | 'starttls';
    fromAddress: string;
    fromName: string;
    testEmailRecipient: string;
}

export interface AnnouncementSettings {
    isEnabled: boolean;
    message: string;
    startDate: string | null;
    endDate: string | null;
}

export interface EmailTemplate {
    id: string;
    subject: string;
    body: string;
    description?: string;
    variables: string[];
}
