<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Voice AI Space - Service Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <style>
    body {
        background-color: #f9fafb;
        background-image: 
            linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
        background-size: 20px 20px;
        background-attachment: fixed;
    }
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
  </style>
<script type="importmap">
{
  "imports": {
    "@/": "/",
    "react": "https://aistudiocdn.com/react@^19.1.1",
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
    "react/": "https://aistudiocdn.com/react@^19.1.1/",
    "path": "https://aistudiocdn.com/path@^0.12.7",
    "vite": "https://aistudiocdn.com/vite@^7.1.6",
    "@stripe/stripe-js": "https://aistudiocdn.com/@stripe/stripe-js@^7.9.0",
    "@stripe/react-stripe-js": "https://aistudiocdn.com/@stripe/react-stripe-js@^4.0.2",
    "@supabase/supabase-js": "https://aistudiocdn.com/@supabase/supabase-js@^2.45.0",
    "react-router-dom": "https://aistudiocdn.com/react-router-dom@^7.9.1",
    "@tanstack/react-query": "https://aistudiocdn.com/@tanstack/react-query@^5.89.0",
    "@tanstack/react-query-devtools": "https://aistudiocdn.com/@tanstack/react-query-devtools@^5.89.0",
    "react-error-boundary": "https://aistudiocdn.com/react-error-boundary@^4.0.13",
    "zustand": "https://aistudiocdn.com/zustand@^5.0.8",
    "@vitejs/plugin-react": "https://aistudiocdn.com/@vitejs/plugin-react@^5.0.3"
  }
}
</script>
<script>
  // IMPORTANT: Replace these placeholder values with your actual Supabase project URL and anon key.
  // You can find these in your Supabase project settings under "API".
  window.process = {
    env: {
      SUPABASE_URL: 'https://kygmggmhbddqkurcsqjp.supabase.co',
      SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt5Z21nZ21oYmRkcWt1cmNzcWpwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg5Mjc5NjQsImV4cCI6MjA3NDUwMzk2NH0.gFo0W2zHR4CwA5xe9vgqg-i2sQD22gmHuoLNlfgpeAQ'
    }
  };
</script>
</head>
  <body>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>