import type { CartItem } from './cart';
import type { Order, Invoice } from './order';
import type { Conversation } from './messaging';

export type UserType = 'client' | 'admin' | null;

export interface ProfileData {
  companyName: string;
  industry: string;
  companySize: string;
  website: string;
  description: string;
  primaryContact: string;
  title: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  billingEmail: string;
  billingContact: string;
  billingPhone: string;
  accountNumber: string;
  paymentMethod: string;
  paymentMethodLast4: string;
  billingAddress: string;
  accountStatus: 'Active' | 'Inactive' | 'Pending Setup';
  subscriptionTier: string;
  memberSince: string;
  autoRenewal: boolean;
  notifications: {
    productUpdates: boolean;
    eventUpdates: boolean;
    newsletter: boolean;
    accountAlerts: boolean;
  };
}

export type SignupData = Partial<Omit<ProfileData, 'notifications'>>;

export interface ContactSubmission {
  id: string;
  name: string;
  companyName?: string;
  email: string;
  phone?: string;
  subject: 'Sales Inquiry' | 'Support Request' | 'Partnership' | 'General Question';
  message: string;
  submittedAt: Date;
  isRead: boolean;
  isArchived: boolean;
}

export interface Client {
    id: string;
    user_id: string;
    role: string;
    email: string;
    profile: ProfileData;
    orders?: Order[];
    cart?: CartItem[];
    cancelledPackageIds?: string[];
    conversations?: Conversation[];
    stripeCustomerId?: string;
    invoices?: Invoice[];
}