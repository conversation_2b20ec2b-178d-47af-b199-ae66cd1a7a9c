import React, { useState, useEffect, useMemo } from 'react';
import type { Packages, CategoriesMetadata, BillingType, PackageItem, CategoryMetadata } from '../../types';
import PackageCard from '../../components/PackageCard';
import { useAdminActions, useStripeProducts } from '../../hooks';
import { useQueryClient } from '@tanstack/react-query';
import { useToastStore } from '../../stores/useToastStore';
import { ConfirmationModal, Button } from '../../components/ui';
import PackageEditorDrawer from './PackageEditorDrawer';
import CategoryEditorDrawer from './CategoryEditorDrawer';
import { supabase } from '../../lib/supabaseClient';

const sanitizeKey = (name: string): string => {
    if (!name) return '';
    const cleanedName = name.trim().replace(/[^a-zA-Z0-9\s-]/g, '');
    return cleanedName.replace(/\s+/g, '-').toLowerCase();
};

interface AdminPackagesTabProps {
    packages: Packages;
    categoriesMetadata: CategoriesMetadata;
    billingTypes: BillingType[];
}

const PackagesTab: React.FC<AdminPackagesTabProps> = ({ packages, categoriesMetadata, billingTypes }) => {
    const sortedCategoryKeys = useMemo(() => Object.keys(categoriesMetadata)
        .filter(key => key !== 'about' && key !== 'annual') // Filter out non-package tabs
        .sort((a, b) => (categoriesMetadata[a].orderIndex ?? 0) - (categoriesMetadata[b].orderIndex ?? 0)), 
        [categoriesMetadata]
    );

    const [activeCategory, setActiveCategory] = useState(sortedCategoryKeys[0] || '');
    const [orderedPackages, setOrderedPackages] = useState<PackageItem[]>([]);
    const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
    const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null);
    const [deleteCandidate, setDeleteCandidate] = useState<string | null>(null);
    const [editorState, setEditorState] = useState<{ isOpen: boolean; data: Partial<PackageItem> | null }>({ isOpen: false, data: null });
    const [syncingPackageId, setSyncingPackageId] = useState<string | null>(null);
    const [categoryEditorState, setCategoryEditorState] = useState<{ isOpen: boolean; data: Partial<CategoryMetadata> & { id?: string } | null }>({ isOpen: false, data: null });
    
    // State for category DND
    const [orderedCategories, setOrderedCategories] = useState<string[]>([]);
    const [draggedId, setDraggedId] = useState<string | null>(null);
    const [dropTargetId, setDropTargetId] = useState<string | null>(null);

    const adminActions = useAdminActions();
    const queryClient = useQueryClient();
    const { showToast } = useToastStore();
    const { data: stripeProducts = [] } = useStripeProducts();
    const stripeProductMap = useMemo(() => new Map(stripeProducts.map(p => [p.package_id, p])), [stripeProducts]);

    const packageList = useMemo(() => packages[activeCategory] || [], [packages, activeCategory]);
    
    useEffect(() => {
        setOrderedCategories(sortedCategoryKeys);
    }, [sortedCategoryKeys]);

    useEffect(() => {
        if (sortedCategoryKeys.length > 0 && !activeCategory) {
            setActiveCategory(sortedCategoryKeys[0]);
        }
    }, [sortedCategoryKeys, activeCategory]);

    useEffect(() => {
        const sorted = [...packageList].sort((a, b) => (a.orderIndex ?? 0) - (b.orderIndex ?? 0));
        setOrderedPackages(sorted);
    }, [packageList]);

    // --- CATEGORY DND HANDLERS ---
    const handleCatDragStart = (id: string) => {
        setDraggedId(id);
    };
    const handleCatDragEnter = (id: string) => {
        if (draggedId && draggedId !== id) {
            setDropTargetId(id);
        }
    };
    const handleCatDragEnd = () => {
        setDraggedId(null);
        setDropTargetId(null);
    };
    const handleCatDrop = async (droppedOnId: string) => {
        if (!draggedId || draggedId === droppedOnId) {
            handleCatDragEnd();
            return;
        }

        const items = [...orderedCategories];
        const draggedIdx = items.findIndex(item => item === draggedId);
        const targetIdx = items.findIndex(item => item === droppedOnId);

        if (draggedIdx === -1 || targetIdx === -1) {
            handleCatDragEnd();
            return;
        }

        const [draggedItem] = items.splice(draggedIdx, 1);
        items.splice(targetIdx, 0, draggedItem);
        
        setOrderedCategories(items); // Optimistic UI update
        handleCatDragEnd();

        try {
            const updatePromises = items.map((categoryId, index) => 
                adminActions.updateRecord('categories_metadata', { orderIndex: index }, categoryId)
            );
            await Promise.all(updatePromises);
            await queryClient.invalidateQueries({ queryKey: ['categories-metadata'] });
            showToast('Category order saved!', 'success');
        } catch (e) {
            console.error("Failed to save category order", e);
            showToast('Failed to save category order.', 'error');
            setOrderedCategories(sortedCategoryKeys); // Revert on error
        }
    };


    // --- PACKAGE DND & CRUD HANDLERS ---
    const handleDragStart = (index: number) => {
        setDraggedIndex(index);
    };
    const handleDragEnter = (index: number) => {
        if (draggedIndex !== null && draggedIndex !== index) {
            setDropTargetIndex(index);
        }
    };
    const handleDeletePackage = (id: string) => {
        setDeleteCandidate(id);
    };
    const confirmDeletePackage = async () => {
        if (!deleteCandidate) return;
        try {
            await adminActions.deleteRecord('packages', deleteCandidate);
            queryClient.invalidateQueries({ queryKey: ['packages'] });
            queryClient.invalidateQueries({ queryKey: ['stripe-products'] });
            showToast('Package deleted.', 'success');
        } catch(e) {
            const message = e instanceof Error ? e.message : String(e);
            showToast(`Failed to delete package: ${message}`, 'error');
        } finally {
            setDeleteCandidate(null);
        }
    };
    const handleDrop = async (dropIndex: number) => {
        if (draggedIndex === null || draggedIndex === dropIndex) {
            handleDragEnd();
            return;
        }

        const newPackages = [...orderedPackages];
        const [draggedItem] = newPackages.splice(draggedIndex, 1);
        newPackages.splice(dropIndex, 0, draggedItem);

        setOrderedPackages(newPackages);
        handleDragEnd();

        try {
            const updatePromises = newPackages.map((pkg, index) => {
                const updatedPackageData = { ...pkg, orderIndex: index, categoryId: activeCategory };
                return adminActions.updateRecord('packages', updatedPackageData, pkg.id);
            });
            await Promise.all(updatePromises);
            await queryClient.invalidateQueries({ queryKey: ['packages'] });
        } catch (error) {
            console.error("Failed to reorder packages:", error);
            showToast("Error saving new package order.", 'error');
            const sorted = [...packageList].sort((a, b) => (a.orderIndex ?? 0) - (b.orderIndex ?? 0));
            setOrderedPackages(sorted);
        }
    };
    const handleDragEnd = () => {
        setDraggedIndex(null);
        setDropTargetIndex(null);
    };
    const handleAddNewPackage = () => {
        const newPackage: Partial<PackageItem> = {
            name: 'New Package',
            price: 0,
            billing: 'one-time',
            features: ['New Feature'],
            orderIndex: orderedPackages.length,
            isAddon: false,
            isEnabled: true,
        };
        setEditorState({ isOpen: true, data: newPackage });
    };
    const handleEditPackage = (pkg: PackageItem) => {
        setEditorState({ isOpen: true, data: pkg });
    };
    const handleSyncStripe = async (packageId: string) => {
        setSyncingPackageId(packageId);
        try {
            const { data, error } = await supabase.functions.invoke('stripe-product-sync', {
                body: { packageId },
            });
            if (error) throw error;
            if (data.error) throw new Error(data.error);
            showToast(data.message || 'Successfully synced with Stripe!', 'success');
            await queryClient.invalidateQueries({ queryKey: ['stripe-products'] });
        } catch (err: any) {
            console.error('Failed to sync with Stripe', err);
            showToast(`Stripe Sync Failed: ${err.message}`, 'error');
        } finally {
            setSyncingPackageId(null);
        }
    };
    const handleSavePackage = async (pkgData: PackageItem, isNew: boolean) => {
        if (isNew) {
            const newId = sanitizeKey(`${pkgData.categoryId}-${pkgData.name}`);
            if (!pkgData.name.trim()) {
                throw new Error('Package name is required.');
            }
            if (packageList.some(p => p.id === newId)) {
                throw new Error(`A package with ID "${newId}" already exists. Please choose a different name.`);
            }

            const { id, ...createData } = pkgData;
            const finalData = { ...createData, id: newId };
            
            const createdPackage = await adminActions.createRecord('packages', finalData) as PackageItem;
            showToast('New package created!', 'success');

            if (createdPackage && createdPackage.id && supabase) {
                const { error: syncError } = await supabase.functions.invoke('stripe-product-sync', { body: { packageId: createdPackage.id } });
                if (syncError) throw new Error(`Package created, but Stripe sync failed: ${syncError.message}`);
                await queryClient.invalidateQueries({ queryKey: ['stripe-products'] });
                showToast('Package synced with Stripe!', 'success');
            }
        } else {
            await adminActions.updateRecord('packages', pkgData, pkgData.id);
            showToast('Package details saved successfully.', 'success');

            const { data, error } = await supabase.functions.invoke('stripe-product-sync', { body: { packageId: pkgData.id } });
            if (error) throw error;
            if (data.error) throw new Error(data.error);
            showToast(data.message || 'Successfully synced with Stripe!', 'success');
            await queryClient.invalidateQueries({ queryKey: ['stripe-products'] });
        }
        await queryClient.invalidateQueries({ queryKey: ['packages'] });
    };
    const handleEditCategory = () => {
        const metadata = categoriesMetadata[activeCategory];
        if (metadata) {
            setCategoryEditorState({ isOpen: true, data: { ...metadata, id: activeCategory } });
        }
    };
    const handleAddCategory = () => {
        setCategoryEditorState({ isOpen: true, data: { orderIndex: sortedCategoryKeys.length } });
    };
    const handleSaveCategory = async (data: CategoryMetadata & { id: string }) => {
        const isNew = !categoriesMetadata[data.id];
        try {
            if (isNew) {
                await adminActions.createRecord('categories_metadata', data);
            } else {
                await adminActions.updateRecord('categories_metadata', data, data.id);
            }
            showToast('Category saved!', 'success');
            await queryClient.invalidateQueries({ queryKey: ['categories-metadata'] });
            setCategoryEditorState({ isOpen: false, data: null });
            if (isNew) {
                setActiveCategory(data.id);
            }
            return { success: true };
        } catch (e: any) {
            showToast(`Error: ${e.message}`, 'error');
            return { success: false, error: e.message };
        }
    };


    const packageToDelete = deleteCandidate ? packageList.find(p => p.id === deleteCandidate) : null;
    const metadata = categoriesMetadata[activeCategory];

    return (
        <div>
            <div className="bg-white border border-gray-300 p-2">
                <div className="flex items-center justify-between">
                    <div className="flex overflow-x-auto no-scrollbar" onDragEnd={handleCatDragEnd}>
                         {orderedCategories.map(categoryKey => (
                            <React.Fragment key={categoryKey}>
                                {dropTargetId === categoryKey && draggedId !== categoryKey && (
                                    <div className="w-1 h-6 bg-black self-center transition-all" />
                                )}
                                <button
                                    onClick={() => setActiveCategory(categoryKey)}
                                    draggable
                                    onDragStart={() => handleCatDragStart(categoryKey)}
                                    onDrop={() => handleCatDrop(categoryKey)}
                                    onDragEnter={() => handleCatDragEnter(categoryKey)}
                                    onDragOver={(e) => e.preventDefault()}
                                    className={`flex items-center gap-2 px-4 py-2 text-sm font-medium whitespace-nowrap cursor-grab transition-all duration-200
                                        ${activeCategory === categoryKey ? 'bg-gray-100 text-black' : 'text-gray-600 hover:bg-gray-50'}
                                        ${draggedId === categoryKey ? 'opacity-40 scale-95 shadow-lg -rotate-1' : ''}
                                    `}
                                >
                                    <i className="fa-solid fa-tags"></i>
                                    {categoriesMetadata[categoryKey]?.tabTitle || categoryKey}
                                </button>
                            </React.Fragment>
                        ))}
                    </div>
                    <div className="flex-shrink-0 flex items-center gap-2 pl-4">
                        <Button onClick={handleAddCategory} size="sm" title="Add New Category"><i className="fa-solid fa-plus"></i></Button>
                        <Button onClick={handleEditCategory} variant="secondary" size="sm" disabled={!activeCategory} title="Edit Current Category"><i className="fa-solid fa-pencil"></i></Button>
                    </div>
                </div>
            </div>

            <div className="mt-6" onDragEnd={handleDragEnd}>
                 {metadata ? (
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold mb-2 text-black">{metadata.pageTitle}</h1>
                        {metadata.description && <p className="text-gray-600 max-w-3xl">{metadata.description}</p>}
                    </div>
                 ) : (
                    <div className="text-center py-20 text-gray-500">
                        <p>No category selected or categories have not been configured yet.</p>
                        <p>Click the '+' button to add a new category.</p>
                    </div>
                 )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {orderedPackages.map((pkg, index) => (
                        <PackageCard
                            key={pkg.id}
                            packageItem={pkg}
                            category={activeCategory}
                            cart={[]} // Admin doesn't need a cart
                            addToCart={() => {}}
                            isAdmin={true}
                            index={index}
                            isDragging={draggedIndex === index}
                            isDropTarget={dropTargetIndex === index}
                            onDragStart={() => handleDragStart(index)}
                            onDrop={() => handleDrop(index)}
                            onDragEnter={() => handleDragEnter(index)}
                            onEdit={handleEditPackage}
                            onDelete={handleDeletePackage}
                            stripeProductInfo={stripeProductMap.get(pkg.id)}
                            onSyncStripe={handleSyncStripe}
                            isSyncing={syncingPackageId === pkg.id}
                        />
                    ))}
                     <button 
                        onClick={handleAddNewPackage}
                        className="border-2 border-dashed border-gray-300 bg-gray-50 text-gray-500 hover:border-black hover:text-black transition-colors duration-300 flex flex-col items-center justify-center min-h-[200px] p-6"
                    >
                        <i className="fa-solid fa-plus text-3xl mb-2"></i>
                        <span className="font-semibold">Add New Package</span>
                    </button>
                </div>
            </div>

            <ConfirmationModal
                isOpen={!!deleteCandidate}
                onClose={() => setDeleteCandidate(null)}
                onConfirm={confirmDeletePackage}
                title="Delete Package"
                message={<p>Are you sure you want to delete the package <strong>"{packageToDelete?.name}"</strong>? This will also archive the product in Stripe if it exists.</p>}
                confirmText="Delete Package"
            />
            {editorState.isOpen && (
                <PackageEditorDrawer
                    isOpen={editorState.isOpen}
                    onClose={() => setEditorState({ isOpen: false, data: null })}
                    onSave={handleSavePackage}
                    packageToEdit={editorState.data}
                    category={activeCategory}
                    allPackagesInCategory={packageList}
                    stripeProductInfo={editorState.data?.id ? stripeProductMap.get(editorState.data.id) : undefined}
                />
            )}
             {categoryEditorState.isOpen && (
                <CategoryEditorDrawer
                    onClose={() => setCategoryEditorState({ isOpen: false, data: null })}
                    onSave={handleSaveCategory}
                    categoryToEdit={categoryEditorState.data}
                    billingTypes={billingTypes}
                    existingCategoryIds={sortedCategoryKeys}
                />
            )}
        </div>
    );
};

export default PackagesTab;
