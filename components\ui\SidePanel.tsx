import React, { useEffect, useRef } from 'react';
import { Button } from './Button';

interface SidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'md' | 'lg' | 'xl' | '2xl' | '3xl';
}

const sizeClasses: Record<string, string> = {
  md: 'sm:max-w-md',
  lg: 'sm:max-w-lg',
  xl: 'sm:max-w-xl',
  '2xl': 'sm:max-w-2xl',
  '3xl': 'sm:max-w-3xl',
};

const SidePanel: React.FC<SidePanelProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
}) => {
  const panelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = 'hidden';
      document.addEventListener('keydown', handleKeyDown);
      // For full accessibility, focus trapping could be added here.
      // A simple approach is to focus the panel itself on open.
      panelRef.current?.focus();
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  const panelSizeClass = sizeClasses[size] || sizeClasses['md'];

  return (
    <>
      {/* Overlay */}
      <div
        onClick={onClose}
        className={`fixed inset-0 bg-black z-40 transition-opacity duration-300 ${isOpen ? 'bg-opacity-60' : 'bg-opacity-0 pointer-events-none'}`}
        aria-hidden="true"
      />
      {/* Side Panel */}
      <div
        ref={panelRef}
        className={`fixed right-0 top-0 h-full w-full bg-white z-50 transform transition-transform duration-300 ease-in-out shadow-2xl flex flex-col ${panelSizeClass} ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="side-panel-title"
        tabIndex={-1} // Make the panel focusable
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <h2 id="side-panel-title" className="text-xl font-semibold text-black">{title}</h2>
          <Button onClick={onClose} variant="ghost" size="sm" className="p-2">
            <i className="fa-solid fa-xmark text-lg"></i>
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div className="border-t border-gray-200 p-6 bg-gray-50 flex-shrink-0">
            {footer}
          </div>
        )}
      </div>
    </>
  );
};

export default SidePanel;
