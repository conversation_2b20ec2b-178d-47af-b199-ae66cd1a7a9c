export interface ChartDataPoint {
    label: string;
    oneTime: number;
    recurring: number;
    subscriptions?: number;
    projection?: number;
}

export interface DashboardMetrics {
    totalRevenue: number;
    mrr: number;
    totalClients: number;
    activeSubscriptions: number;
    projectedRevenue: number;
    daily: ChartDataPoint[];
    monthly: ChartDataPoint[];
    yearly: ChartDataPoint[];
}