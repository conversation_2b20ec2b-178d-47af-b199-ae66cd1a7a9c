import React, { useState } from 'react';
import { Button, FormField, SidePanel } from '../../components/ui';
import type { BillingType, CategoryMetadata } from '../../types';

interface CategoryEditorDrawerProps {
    onClose: () => void;
    onSave: (data: CategoryMetadata & { id: string }) => Promise<{ success: boolean; error?: string }>;
    categoryToEdit: Partial<CategoryMetadata> & { id?: string } | null;
    billingTypes: BillingType[];
    existingCategoryIds: string[];
}

const sanitizeKey = (name: string): string => {
    if (!name) return '';
    const cleanedName = name.trim().replace(/[^a-zA-Z0-9\s-]/g, '');
    return cleanedName.replace(/\s+/g, '-').toLowerCase();
};

const CategoryEditorDrawer: React.FC<CategoryEditorDrawerProps> = ({ onClose, onSave, categoryToEdit, billingTypes, existingCategoryIds }) => {
    const [formData, setFormData] = useState(categoryToEdit);
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const isNew = !formData?.id;

    const handleSave = async () => {
        setError('');
        if (!formData?.pageTitle?.trim()) {
            setError('Page Title is required.');
            return;
        }

        setIsLoading(true);
        let id = formData.id;
        if (isNew) {
            id = sanitizeKey(formData.pageTitle);
            if (!id) {
                setError('Could not generate a valid ID from the Page Title.');
                setIsLoading(false);
                return;
            }
            if (existingCategoryIds.includes(id)) {
                setError(`A category with ID "${id}" already exists. Please choose a different Page Title.`);
                setIsLoading(false);
                return;
            }
        }
        
        const finalData = {
            id: id!,
            pageTitle: formData.pageTitle!,
            tabTitle: formData.tabTitle || formData.pageTitle!,
            description: formData.description || '',
            billingType: formData.billingType || '',
            // Preserve existing values not in the form
            isEnabled: formData.isEnabled !== undefined ? formData.isEnabled : true,
            orderIndex: formData.orderIndex !== undefined ? formData.orderIndex : existingCategoryIds.length,
            validFrom: formData.validFrom,
            validTo: formData.validTo,
        };

        const result = await onSave(finalData as CategoryMetadata & { id: string });
        if (!result.success) {
            setError(result.error || 'Failed to save category.');
        }
        setIsLoading(false);
    };
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => prev ? { ...prev, [name]: value } : { [name]: value });
    };

    const renderFooter = () => (
        <div className="flex gap-2">
            <Button onClick={handleSave} isLoading={isLoading}>Save Category</Button>
            <Button variant="secondary" onClick={onClose} disabled={isLoading}>Cancel</Button>
        </div>
    );

    return (
        <SidePanel isOpen={true} onClose={onClose} title={isNew ? "Create New Category" : `Edit: ${categoryToEdit?.pageTitle}`} footer={renderFooter()} size="lg">
            <div className="space-y-4">
                {error && <p className="text-red-600 text-sm bg-red-50 p-3 border border-red-200">{error}</p>}
                {isNew && <p className="text-sm text-gray-500">The Category ID will be automatically generated from the Page Title. This ID must be unique.</p>}
                
                <FormField label="Page Title" name="pageTitle" value={formData?.pageTitle || ''} onChange={handleChange} required />
                <FormField label="Tab Title" name="tabTitle" value={formData?.tabTitle || ''} onChange={handleChange} placeholder="Defaults to Page Title" />
                <FormField as="textarea" label="Description" name="description" value={formData?.description || ''} onChange={handleChange} />
                <FormField as="select" label="Billing Type" name="billingType" value={formData?.billingType || ''} onChange={handleChange}>
                    <option value="">None</option>
                    {billingTypes.map(bt => <option key={bt.id} value={bt.id}>{bt.name}</option>)}
                </FormField>
            </div>
        </SidePanel>
    );
};

export default CategoryEditorDrawer;
