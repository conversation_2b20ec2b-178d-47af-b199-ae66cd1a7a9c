import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/useAuthStore';
import { supabase } from '@/lib/supabaseClient';
import { ClientService } from '@/lib/supabaseService';
import { <PERSON><PERSON>, FormField } from '@/components/ui';
import PasswordStrengthMeter from '@/components/PasswordStrengthMeter';
import { useQueryClient } from '@tanstack/react-query';
import { useToastStore } from '@/stores/useToastStore';


const calculatePasswordStrength = (password: string): number => {
    if (!password) return 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[^A-Za-z0-9]/.test(password),
    };
    if (!checks.length) return 1;
    let conditionsMet = Object.values(checks).filter(Boolean).length - 1;
    if (password.length < 8) return 1;
    if (conditionsMet <= 1) return 1;
    if (conditionsMet === 2) return 2;
    if (conditionsMet === 3) return 3;
    if (conditionsMet >= 4) return 4;
    return 0;
};

// FIX: Complete the component, add a return statement with JSX, and include a default export.
const CompleteSetupPage: React.FC = () => {
    const navigate = useNavigate();
    const queryClient = useQueryClient();
    const { user, isLoading: isAuthLoading, fetchUserData } = useAuthStore();
    const { showToast } = useToastStore();

    const [password, setPassword] = useState('');
    const [passwordStrength, setPasswordStrength] = useState(0);
    const [confirmPassword, setConfirmPassword] = useState('');
    const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);

    useEffect(() => {
        // If there's no user session and it's not loading, they shouldn't be here.
        if (!isAuthLoading && !user.data) {
            showToast('Invalid setup link. Please request a new one.', 'error');
            navigate('/login');
        }
    }, [user, isAuthLoading, navigate, showToast]);
    
    const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newPassword = e.target.value;
        setPassword(newPassword);
        setPasswordStrength(calculatePasswordStrength(newPassword));
    };

    const validateForm = () => {
        const errors: { [key: string]: string } = {};
        if (!password) {
            errors.password = 'Password is required.';
        } else if (passwordStrength < 4) {
            errors.password = 'Password must be strong. Include uppercase, lowercase, numbers, and special characters.';
        }
        if (password !== confirmPassword) {
            errors.confirmPassword = 'Passwords do not match.';
        }
        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (!validateForm() || !user.data) return;

        setIsSubmitting(true);
        try {
            // 1. Update user password in Supabase Auth
            const { error: updateError } = await supabase.auth.updateUser({ password });
            if (updateError) throw updateError;
            
            // 2. Update client account status
            await ClientService.updateProfile(user.data.id, { accountStatus: 'Active' });

            // 3. Invalidate queries and refetch user data to reflect the change
            await queryClient.invalidateQueries({ queryKey: ['clients', user.data.id] });
            const session = (await supabase.auth.getSession()).data.session;
            await fetchUserData(session);

            showToast('Account setup complete! You can now log in.', 'success');
            setIsSuccess(true);
            setTimeout(() => navigate('/login'), 3000);

        } catch (error: any) {
            console.error('Failed to complete setup:', error);
            setFormErrors({ form: error.message || 'An unexpected error occurred.' });
        } finally {
            setIsSubmitting(false);
        }
    };
    
    if (isAuthLoading) {
        return <div className="text-center p-20"><i className="fa-solid fa-spinner fa-spin text-4xl text-gray-400"></i></div>;
    }
    
    if (isSuccess) {
        return (
            <div className="min-h-[60vh] flex items-center justify-center">
                <div className="max-w-md w-full mx-auto text-center bg-white p-8 border border-gray-300">
                    <h2 className="text-2xl font-bold mb-2 text-black">Setup Complete!</h2>
                    <p className="text-gray-600">Your account is now active. You will be redirected to the login page shortly.</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="min-h-[60vh] flex items-center justify-center">
            <div className="max-w-md w-full mx-auto">
                <div className="bg-white p-8 border border-gray-300">
                    <h2 className="text-2xl font-bold text-center mb-1 text-black">Complete Account Setup</h2>
                    <p className="text-center text-gray-600 mb-6">Welcome, {user.data?.profile?.primaryContact}! Please set a new password for your account.</p>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        {formErrors.form && <p className="text-red-600 text-sm text-center">{formErrors.form}</p>}
                        <div>
                            <FormField label="New Password" name="password" type="password" value={password} onChange={handlePasswordChange} error={formErrors.password} />
                            <PasswordStrengthMeter score={passwordStrength} />
                            {password.length > 0 && passwordStrength < 4 && (
                                <p className="text-xs text-gray-500 mt-1">
                                    Password must contain uppercase, lowercase, numbers, and special characters.
                                </p>
                            )}
                        </div>
                        <FormField label="Confirm New Password" name="confirmPassword" type="password" value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} error={formErrors.confirmPassword} />
                        
                        <div>
                            <Button type="submit" size="lg" className="w-full" isLoading={isSubmitting} disabled={isSubmitting}>
                                Set Password & Finish
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default CompleteSetupPage;
