import React, { useState } from 'react';
import { <PERSON><PERSON>, FormField } from '@/components/ui';
import type { ContactSubmission } from '@/types';
import { useCreateContactSubmission } from '@/hooks/useContactSubmissions';

type ContactFormData = Omit<ContactSubmission, 'id' | 'submittedAt' | 'isRead' | 'isArchived'>;

const ContactPage: React.FC = () => {
  const createSubmission = useCreateContactSubmission();
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    companyName: '',
    email: '',
    phone: '',
    subject: 'General Question',
    message: '',
  });
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (!formData.name.trim()) errors.name = 'Your name is required.';
    if (!formData.email.trim()) {
      errors.email = 'Email is required.';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid.';
    }
    if (!formData.message.trim()) errors.message = 'A message is required.';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value as any }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        await createSubmission.mutateAsync(formData);
        setIsSubmitted(true);
      } catch (error) {
        setFormErrors({ form: 'There was an error submitting your message. Please try again.' });
      }
    }
  };
  
  if (isSubmitted) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="max-w-lg w-full mx-auto text-center bg-white p-8 border border-gray-300">
          <div className="w-16 h-16 bg-black text-white flex items-center justify-center mx-auto mb-6">
            <i className="fa-solid fa-check text-3xl"></i>
          </div>
          <h2 className="text-3xl font-bold mb-2 text-black">Thank You!</h2>
          <p className="text-gray-600">Your message has been sent successfully. Our team will get back to you shortly.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="max-w-2xl w-full mx-auto">
        <div className="bg-white p-8 border border-gray-300">
          <h2 className="text-3xl font-bold text-center mb-1 text-black">Contact Us</h2>
          <p className="text-center text-gray-600 mb-8">Have a question or need support? Fill out the form below.</p>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Full Name" name="name" value={formData.name} onChange={handleChange} error={formErrors.name} required />
              <FormField label="Company Name" name="companyName" value={formData.companyName || ''} onChange={handleChange} required={false} />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Email Address" name="email" value={formData.email} onChange={handleChange} type="email" error={formErrors.email} required />
              <FormField label="Phone Number" name="phone" value={formData.phone || ''} onChange={handleChange} type="tel" required={false} />
            </div>
            <div>
              <FormField
                label="Subject"
                name="subject"
                as="select"
                value={formData.subject}
                onChange={handleChange}
                options={['General Question', 'Sales Inquiry', 'Support Request', 'Partnership']}
                required
              />
            </div>
            <div>
              <FormField label="Message" name="message" as="textarea" value={formData.message} onChange={handleChange} error={formErrors.message} required />
            </div>
             {formErrors.form && <p className="text-red-600 text-sm">{formErrors.form}</p>}
            <div>
              <Button
                type="submit"
                isLoading={createSubmission.isPending}
                disabled={createSubmission.isPending}
                size="lg"
                className="w-full"
              >
                Submit Message
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;