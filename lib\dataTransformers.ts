// Data transformation functions to ensure consistency between database and application types

import type {
  DatabaseClient,
  DatabaseOrder,
  DatabaseConversation,
  DatabaseMessage,
  DatabaseContactSubmission,
  DatabasePackage,
  DatabaseBillingType,
  DatabaseBillingOption,
  DatabaseCategoryMetadata,
  DatabaseServiceCategory,
  DatabasePaymentSettings,
  DatabaseSmtpSettings,
  DatabaseAnnouncementSettings,
  DatabaseDiscountCode,
  DatabaseSubscription,
  DatabaseInvoice,
  DatabaseEmailTemplate,
  DatabaseCartItem,
  DatabaseOrderItem,
  DatabaseOrderDiscountCode,
} from '@/types/database';

import type {
  Client,
  Order,
  Conversation,
  Message,
  ContactSubmission,
  PackageItem,
  BillingType,
  BillingOption,
  CategoryMetadata,
  ServiceCategoryItem,
  PaymentSettings,
  SmtpSettings,
  AnnouncementSettings,
  DiscountCode,
  Subscription,
  Invoice,
  EmailTemplate,
  CartItem,
  ProfileData,
} from '@/types';

// --- START: GENERIC TYPE UTILITIES ---

// Type to convert a single snake_case string to camelCase
type SnakeToCamelCase<S extends string> = S extends `${infer T}_${infer U}`
  ? `${T}${Capitalize<SnakeToCamelCase<U>>}`
  : S;

// Type to recursively convert all keys of an object from snake_case to camelCase
type SnakeToCamel<T> = T extends (infer U)[]
  ? SnakeToCamel<U>[]
  : T extends Date | null | undefined // Keep Date, null, and undefined as is
  ? T
  : T extends object
  ? { [K in keyof T as SnakeToCamelCase<string & K>]: SnakeToCamel<T[K]> }
  : T;

// Type to convert a single camelCase string to snake_case
type CamelToSnakeCase<S extends string> = S extends `${infer T}${infer U}`
  ? `${T extends Capitalize<T> ? '_' : ''}${Lowercase<T>}${CamelToSnakeCase<U>}`
  : S;

// Type to recursively convert all keys of an object from camelCase to snake_case
type CamelToSnake<T> = T extends (infer U)[]
  ? CamelToSnake<U>[]
  : T extends Date | null | undefined // Keep Date, null, and undefined as is
  ? T
  : T extends object
  ? { [K in keyof T as CamelToSnakeCase<string & K>]: CamelToSnake<T[K]> }
  : T;

// --- END: GENERIC TYPE UTILITIES ---


// --- START: GENERIC TRANSFORMER FUNCTIONS ---

/**
 * Recursively transforms keys of an object from snake_case to camelCase.
 * Now fully type-safe with mapped types.
 */
export function snakeToCamel<T>(obj: T): SnakeToCamel<T> {
  if (Array.isArray(obj)) {
    return obj.map(v => snakeToCamel(v)) as SnakeToCamel<T>;
  } else if (obj !== null && typeof obj === 'object' && !(obj instanceof Date)) {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = key.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
      (acc as any)[camelKey] = snakeToCamel((obj as any)[key]);
      return acc;
    }, {} as SnakeToCamel<T>);
  }
  return obj as SnakeToCamel<T>;
}

/**
 * Recursively transforms keys of an object from camelCase to snake_case.
 * Now fully type-safe with mapped types.
 */
export function camelToSnake<T>(obj: T): CamelToSnake<T> {
  if (Array.isArray(obj)) {
    return obj.map(v => camelToSnake(v)) as CamelToSnake<T>;
  } else if (obj !== null && typeof obj === 'object' && !(obj instanceof Date)) {
    return Object.keys(obj).reduce((acc, key) => {
      const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
      (acc as any)[snakeKey] = camelToSnake((obj as any)[key]);
      return acc;
    }, {} as CamelToSnake<T>);
  }
  return obj as CamelToSnake<T>;
}

// --- END: GENERIC TRANSFORMER FUNCTIONS ---


// --- START: TYPE-SPECIFIC TRANSFORMERS ---

// Define types for DB results with relations to avoid 'any'
type DbClientWithRelations = DatabaseClient & {
  orders: DbOrderWithRelations[];
  conversations: DbConversationWithRelations[];
  cart_items: DatabaseCartItem[];
  invoices: DatabaseInvoice[];
};
type DbOrderWithRelations = DatabaseOrder & { order_items: DatabaseOrderItem[], order_discount_codes: DatabaseOrderDiscountCode[] };
type DbConversationWithRelations = DatabaseConversation & { messages: DatabaseMessage[] };
type DbBillingTypeWithRelations = DatabaseBillingType & { billing_options: DatabaseBillingOption[] };
type DbSubscriptionWithRelations = DatabaseSubscription & { clients: Partial<DatabaseClient> | null, packages: Partial<DatabasePackage> | null, invoices: DatabaseInvoice[] };


/**
 * Transform database client to application client
 */
export function transformDatabaseClientToClient(dbClient: DbClientWithRelations): Client {
  const {
    id, user_id, role, email, cancelled_package_ids, stripe_customer_id,
    orders, conversations, cart_items, invoices,
    ...dbProfileData
  } = dbClient;

  // snakeToCamel will now return a strongly-typed object
  const camelProfileData = snakeToCamel(dbProfileData);
  
  // This custom logic for nested 'notifications' object is still necessary
  const notifications = {
    productUpdates: camelProfileData.notificationsProductUpdates,
    eventUpdates: camelProfileData.notificationsEventUpdates,
    newsletter: camelProfileData.notificationsNewsletter,
    accountAlerts: camelProfileData.notificationsAccountAlerts,
  };

  // Remove the flattened notification properties from the main profile object
  delete (camelProfileData as any).notificationsProductUpdates;
  delete (camelProfileData as any).notificationsEventUpdates;
  delete (camelProfileData as any).notificationsNewsletter;
  delete (camelProfileData as any).notificationsAccountAlerts;
  
  return {
    id,
    user_id, // Keep snake_case for Supabase Auth reference
    role,
    email,
    // FIX: Property 'email' is missing in type '{...}' but required in type 'ProfileData'.
    profile: { ...camelProfileData, notifications, email } as ProfileData,
    orders: (orders || []).map(transformDatabaseOrderToOrder),
    cart: (cart_items || []).map(transformDatabaseCartItemToCartItem),
    cancelledPackageIds: cancelled_package_ids || [],
    conversations: (conversations || []).map(transformDatabaseConversationToConversation),
    invoices: (invoices || []).map(transformDatabaseInvoiceToInvoice),
    stripeCustomerId: stripe_customer_id,
  };
}


/**
 * Transform application client to database client (for inserts/updates)
 */
export function transformClientToDatabaseClient(client: Partial<Client>): Partial<DatabaseClient> {
    const { profile, orders, cart, conversations, invoices, ...topLevelClientData } = client;

    // Use the generic camelToSnake for top-level properties
    const dbClient = camelToSnake(topLevelClientData);

    if (profile) {
        // Flatten notifications object for DB storage
        const { notifications, ...restOfProfile } = profile;
        const flattenedProfile = { ...restOfProfile, ...notifications };
        const dbProfile = camelToSnake(flattenedProfile);
        Object.assign(dbClient, dbProfile);
    }
    
    return dbClient;
}


/**
 * Transform database cart item to application cart item.
 * This involves custom key renaming (id -> cartId, package_id -> id)
 */
export function transformDatabaseCartItemToCartItem(dbItem: DatabaseCartItem): CartItem {
  const { id, package_id, ...rest } = dbItem;
  const camelRest = snakeToCamel(rest);
  return {
    ...camelRest,
    cartId: id,
    id: package_id,
    // FIX: Property 'categoryId' is missing in type '{...}' but required in type 'CartItem'.
    categoryId: camelRest.category,
  } as CartItem;
}


/**
 * Transform database order to application order
 */
export function transformDatabaseOrderToOrder(dbOrder: DbOrderWithRelations): Order {
  const { order_items, order_discount_codes, ...restOfOrder } = dbOrder;
  const camelOrder = snakeToCamel(restOfOrder);

  return {
    ...camelOrder,
    items: (order_items || []).map(transformDatabaseOrderItemToCartItem),
    discountCodes: (order_discount_codes || []).map((d) => ({
        // FIX: Type 'string' is not assignable to type '"fixed" | "percentage"'.
        code: d.code, value: d.value, type: d.type as 'fixed' | 'percentage' 
    })),
    discountAmount: camelOrder.discountAmount || 0
  };
}

/**
 * Transform database order item to application cart item.
 * This involves custom key renaming.
 */
export function transformDatabaseOrderItemToCartItem(dbItem: DatabaseOrderItem): CartItem {
  const { id, package_id, ...rest } = dbItem;
  const camelRest = snakeToCamel(rest);
  return {
    ...camelRest,
    cartId: id, // Use order item ID as unique cartId for display
    id: package_id || `pkg-deleted-${dbItem.name}`,
    // FIX: Property 'categoryId' is missing in type '{...}' but required in type 'CartItem'.
    categoryId: camelRest.category,
  } as CartItem;
}

/**
 * Transform application order to database order (for inserts)
 */
export function transformOrderToDatabaseOrder(order: Partial<Order>): Partial<DatabaseOrder> {
  const { items, discountCodes, ...restOfOrder } = order;
  return camelToSnake(restOfOrder);
}

/**
 * Transform database conversation to application conversation
 */
export function transformDatabaseConversationToConversation(dbConvo: DbConversationWithRelations): Conversation {
  const { messages, ...restOfConvo } = dbConvo;
  const camelConvo = snakeToCamel(restOfConvo);
  return {
    ...camelConvo,
    messages: (messages || []).map(transformDatabaseMessageToMessage),
  };
}

/**
 * Transform database message to application message
 */
export function transformDatabaseMessageToMessage(dbMessage: DatabaseMessage): Message {
  return snakeToCamel(dbMessage);
}

/**
 * Transform database contact submission to application contact submission
 */
export function transformDatabaseContactSubmissionToContactSubmission(dbSubmission: DatabaseContactSubmission): ContactSubmission {
  // FIX: Type 'string' is not assignable to type 'Date'.
  const camelSubmission = snakeToCamel(dbSubmission);
  return {
    ...camelSubmission,
    submittedAt: new Date(camelSubmission.submittedAt),
  };
}

/**
 * Transform application contact submission to database contact submission (for inserts)
 */
export function transformContactSubmissionToDatabaseContactSubmission(
  submission: Omit<ContactSubmission, 'id' | 'submittedAt' | 'isRead' | 'isArchived'>
): Omit<DatabaseContactSubmission, 'id' | 'submitted_at' | 'is_read' | 'is_archived'> {
  return camelToSnake(submission);
}

/**
 * Transform database package to application package
 */
export function transformDatabasePackageToPackage(dbPackage: DatabasePackage): PackageItem {
  // FIX: Type 'string' is not assignable to type '"/month" | "/year" | "one-time"'.
  const camelPackage = snakeToCamel(dbPackage);
  return {
    ...camelPackage,
    billing: camelPackage.billing as '/month' | '/year' | 'one-time',
  };
}

/**
 * Transform application package to database package (for inserts/updates)
 */
export function transformPackageToDatabasePackage(pkg: Partial<PackageItem>): Partial<DatabasePackage> {
  return camelToSnake(pkg);
}

/**
 * Transform database billing type to application billing type
 */
export function transformDatabaseBillingTypeToBillingType(dbBillingType: DbBillingTypeWithRelations): BillingType {
  const { billing_options, ...rest } = dbBillingType;
  const camelBillingType = snakeToCamel(rest);
  return {
    ...camelBillingType,
    options: (billing_options || []).map(transformDatabaseBillingOptionToBillingOption),
  };
}

export function transformDatabaseBillingOptionToBillingOption(dbOption: DatabaseBillingOption): BillingOption {
    return snakeToCamel(dbOption);
}


/**
 * Transform application billing type to database billing type (for inserts/updates)
 */
export function transformBillingTypeToDatabaseBillingType(billingType: Partial<BillingType>): Partial<Omit<DatabaseBillingType, 'id'>> {
  const { options, ...rest } = billingType;
  return camelToSnake(rest);
}

/**
 * Transform database category metadata to application category metadata
 * This involves custom key renaming.
 */
export function transformDatabaseCategoryMetadataToCategoryMetadata(dbMetadata: DatabaseCategoryMetadata): CategoryMetadata {
  const { billing_type_id, ...rest } = dbMetadata;
  const camelRest = snakeToCamel(rest);
  return {
    ...camelRest,
    // FIX: Property 'description' is optional in type '{...}' but required in type 'CategoryMetadata'.
    description: camelRest.description || '',
    billingType: billing_type_id || ''
  };
}

/**
 * Transform application category metadata to database category metadata (for inserts/updates)
 * This involves custom key renaming.
 */
export function transformCategoryMetadataToDatabaseCategoryMetadata(metadata: Partial<CategoryMetadata> & { id: string }): Partial<DatabaseCategoryMetadata> {
  const { billingType, ...rest } = metadata;
  const snakeRest = camelToSnake(rest);
  return {
    ...snakeRest,
    billing_type_id: billingType
  };
}

/**
 * Transform database service category to application service category
 */
export function transformDatabaseServiceCategoryToServiceCategory(dbServiceCategory: DatabaseServiceCategory): ServiceCategoryItem {
    return snakeToCamel(dbServiceCategory);
}

/**
 * Transform application service category to database service category
 */
export function transformServiceCategoryToDatabaseServiceCategory(serviceCategory: Partial<ServiceCategoryItem>): Partial<DatabaseServiceCategory> {
    return camelToSnake(serviceCategory);
}


/**
 * Transform database payment settings to application payment settings
 */
export function transformDatabasePaymentSettingsToPaymentSettings(dbSettings: DatabasePaymentSettings): PaymentSettings {
  const camelSettings = snakeToCamel(dbSettings);
  return {
    stripe: {
      enabled: camelSettings.stripeEnabled,
      publicKey: camelSettings.stripePublicKey || '',
      secretKey: camelSettings.stripeSecretKey || '',
      methods: {
        creditCard: !!camelSettings.methodCreditCard,
        applePay: !!camelSettings.methodApplePay,
        googlePay: !!camelSettings.methodGooglePay
      }
    }
  };
}

/**
 * Transform application payment settings to database payment settings
 */
export function transformPaymentSettingsToDatabasePaymentSettings(settings: PaymentSettings): Omit<DatabasePaymentSettings, 'id'> {
  return {
    stripe_enabled: settings.stripe.enabled,
    stripe_public_key: settings.stripe.publicKey,
    stripe_secret_key: settings.stripe.secretKey,
    method_credit_card: settings.stripe.methods.creditCard,
    method_apple_pay: settings.stripe.methods.applePay,
    method_google_pay: settings.stripe.methods.googlePay
  };
}

/**
 * Transform database SMTP settings to application SMTP settings
 */
export function transformDatabaseSmtpSettingsToSmtpSettings(dbSettings: DatabaseSmtpSettings): SmtpSettings {
  // FIX: Property 'server' is optional in type '{...}' but required in type 'SmtpSettings'.
  const camelSettings = snakeToCamel(dbSettings);
  return {
    testMode: camelSettings.testMode,
    server: camelSettings.server || '',
    port: camelSettings.port || 587,
    username: camelSettings.username || '',
    password: camelSettings.password || '',
    encryption: (camelSettings.encryption as SmtpSettings['encryption']) || 'none',
    fromAddress: camelSettings.fromAddress || '',
    fromName: camelSettings.fromName || '',
    testEmailRecipient: camelSettings.testEmailRecipient || '',
  };
}

/**
 * Transform application SMTP settings to database SMTP settings
 */
export function transformSmtpSettingsToDatabaseSmtpSettings(settings: SmtpSettings): Omit<DatabaseSmtpSettings, 'id'> {
  return camelToSnake(settings);
}

/**
 * Transform database announcement settings to application announcement settings
 */
export function transformDatabaseAnnouncementSettingsToAnnouncementSettings(dbSettings: DatabaseAnnouncementSettings): AnnouncementSettings {
  // FIX: Property 'message' is optional in type '{...}' but required in type 'AnnouncementSettings'.
  const camelSettings = snakeToCamel(dbSettings);
  return {
    isEnabled: camelSettings.isEnabled,
    message: camelSettings.message || '',
    startDate: camelSettings.startDate || null,
    endDate: camelSettings.endDate || null,
  };
}

/**
 * Transform application announcement settings to database announcement settings
 */
export function transformAnnouncementSettingsToDatabaseAnnouncementSettings(settings: AnnouncementSettings): Omit<DatabaseAnnouncementSettings, 'id'> {
  return camelToSnake(settings);
}

/**
 * Transform database discount code to application discount code
 */
export function transformDatabaseDiscountCodeToDiscountCode(dbCode: DatabaseDiscountCode): DiscountCode {
    // FIX: Property 'validFrom' is optional in type '{...}' but required in type 'DiscountCode'.
    const camelCode = snakeToCamel(dbCode);
    return {
        ...camelCode,
        validFrom: camelCode.validFrom || null,
        validTo: camelCode.validTo || null,
    };
}
  
/**
 * Transform application discount code to database discount code
 */
export function transformDiscountCodeToDatabaseDiscountCode(code: Partial<DiscountCode>): Partial<Omit<DatabaseDiscountCode, 'id' | 'created_at'>> {
    return camelToSnake(code);
}

/**
 * Transform database invoice to application invoice
 */
export function transformDatabaseInvoiceToInvoice(dbInvoice: DatabaseInvoice): Invoice {
    // FIX: Type 'string' is not assignable to type '"paid" | "open" | "draft" | "void" | "uncollectible"'.
    const camelInvoice = snakeToCamel(dbInvoice);
    return {
        ...camelInvoice,
        status: camelInvoice.status as Invoice['status'],
    };
}

/**
 * Transform database subscription to application subscription
 */
export function transformDatabaseSubscriptionToSubscription(dbSub: DbSubscriptionWithRelations): Subscription {
    const { clients, packages, invoices, ...rest } = dbSub;
    const camelSub = snakeToCamel(rest);

    return {
      ...camelSub,
      // FIX: Type 'string' is not assignable to type '"active" | "canceled" | "past_due" | "trialing" | "unpaid"'.
      status: camelSub.status as Subscription['status'],
      clientName: clients?.company_name || 'Unknown Client',
      packageName: packages?.name || 'Unknown Package',
      price: 0,
      invoices: (invoices || []).map(transformDatabaseInvoiceToInvoice),
    };
}

/**
 * Transform database email template to application email template
 */
export function transformDatabaseEmailTemplateToEmailTemplate(dbTemplate: DatabaseEmailTemplate): EmailTemplate {
    return snakeToCamel(dbTemplate);
}

/**
 * Transform application email template to database email template
 */
export function transformEmailTemplateToDatabaseEmailTemplate(template: Partial<EmailTemplate>): Partial<DatabaseEmailTemplate> {
    return camelToSnake(template);
}