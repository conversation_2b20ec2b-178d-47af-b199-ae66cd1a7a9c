# Guide de Débogage - Factures Non Reçues

## Problème Identifié

Après l'achat de packages, les clients ne reçoivent pas de factures. Le système est configuré pour générer automatiquement les factures via les webhooks Stripe.

## Architecture de Génération de Factures

1. **CheckoutForm.tsx** → Traite le paiement avec Stripe
2. **onSuccessfulCheckout** → Crée la commande en base de données
3. **Webhook Stripe** → Reçoit `payment_intent.succeeded` et génère la facture
4. **handlePaymentSuccess** → Crée et envoie la facture automatiquement

## Étapes de Diagnostic

### 1. Vérifier la Configuration des Webhooks Stripe

Dans votre Dashboard Stripe:

1. Allez à **Developers > Webhooks**
2. Vérifiez que l'endpoint existe:
   ```
   https://ljcbzpxisqegqkgjbkjk.supabase.co/functions/v1/stripe-webhook-handler
   ```
3. Vérifiez que ces événements sont configurés:
   - `payment_intent.succeeded` ✅ (REQUIS)
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.created`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

### 2. Vérifier les Secrets Supabase

Exécutez ces commandes pour configurer les secrets:

```bash
# Se connecter à Supabase
supabase login
supabase link --project-ref ljcbzpxisqegqkgjbkjk

# Configurer les secrets requis
supabase secrets set STRIPE_SECRET_KEY=sk_test_...
supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_...

# Vérifier les secrets
supabase secrets list
```

### 3. Redéployer les Edge Functions

```bash
# Redéployer la function webhook
supabase functions deploy stripe-webhook-handler

# Vérifier les logs
supabase functions logs stripe-webhook-handler
```

### 4. Tester le Webhook

Dans Stripe Dashboard:
1. Allez à **Developers > Webhooks**
2. Cliquez sur votre endpoint
3. Cliquez sur **"Send test webhook"**
4. Sélectionnez `payment_intent.succeeded`
5. Vérifiez les logs de réponse

## Test de Paiement

1. Effectuez un achat test avec ces cartes Stripe:
   - **Succès**: `4242 4242 4242 4242`
   - **Échec**: `4000 0000 0000 0002`

2. Vérifiez dans les logs Supabase:
   ```bash
   supabase functions logs stripe-webhook-handler --follow
   ```

## Logs à Surveiller

Recherchez ces messages dans les logs:

### ✅ Succès
```
Received webhook: payment_intent.succeeded
Handling successful payment for PI: pi_xxx
Successfully created, sent, and processed invoice inv_xxx for order ORD-xxx
```

### ❌ Erreurs Communes
```
Signature or secret missing
Webhook Error: Could not find order for payment_intent
Webhook Error: Client for order xxx is missing a stripe_customer_id
Webhook Error: Failed to create invoice for PI xxx
```

## Solutions par Type d'Erreur

### Erreur: "Signature or secret missing"
- Le `STRIPE_WEBHOOK_SECRET` n'est pas configuré
- Solution: `supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_...`

### Erreur: "Could not find order"
- Race condition entre création de commande et webhook
- Vérifiez que `createOrder()` est appelé avant le webhook

### Erreur: "Missing stripe_customer_id"
- Le client n'a pas de customer Stripe associé dans la base de données.
- **Vérifiez la fonction `stripe-customer-create`** : Assurez-vous qu'elle est appelée et réussit avant l'intention de paiement.
- **Vérifiez le webhook `customer.created`** : Si le client a été créé directement dans Stripe (ex: via un lien de paiement), ce webhook doit s'exécuter pour lier le `stripe_customer_id` au profil client dans Supabase. Vérifiez les logs de ce webhook dans le tableau de bord Stripe pour déceler d'éventuelles erreurs.

## Vérification Base de Données

Vérifiez ces tables après un achat:

```sql
-- Vérifier la commande
SELECT * FROM orders WHERE stripe_payment_intent_id = 'pi_xxx';

-- Vérifier la facture générée
SELECT * FROM invoices WHERE stripe_invoice_id IS NOT NULL;

-- Vérifier le client Stripe
SELECT stripe_customer_id FROM clients WHERE id = 'client_id';
```

## Action Immédiate Recommandée

1. **Configurer le secret webhook**:
   ```bash
   supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_[VOTRE_SECRET_DEPUIS_STRIPE]
   ```

2. **Redéployer la function**:
   ```bash
   supabase functions deploy stripe-webhook-handler
   ```

3. **Tester avec un achat**

Le système devrait alors générer et envoyer automatiquement les factures après chaque paiement réussi.