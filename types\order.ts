import type { CartItem } from './cart';

export type OrderStatus = 'Completed' | 'Processing' | 'Cancelled' | 'Refunded';

export interface Order {
    id: string;
    clientId: string;
    createdAt: string;
    items: CartItem[];
    subtotal: number;
    vatAmount: number;
    total: number;
    status: OrderStatus;
    stripePaymentIntentId?: string;
    stripeInvoiceId?: string;
    discountCodes?: { code: string; value: number; type: 'fixed' | 'percentage' }[];
    discountAmount: number;
}

export interface Subscription {
    id: string;
    clientId: string;
    stripeSubscriptionId: string;
    status: 'active' | 'canceled' | 'past_due' | 'trialing' | 'unpaid';
    currentPeriodStart: string;
    currentPeriodEnd: string;
    trialStart?: string;
    trialEnd?: string;
    canceledAt?: string;
    cancelAtPeriodEnd: boolean;
    lastPaymentError?: { message?: string; [key: string]: any } | null;
    orderId?: string;
    clientName?: string;
    packageId?: string;
    packageName?: string;
    startDate?: Date;
    nextBillingDate?: Date;
    billingCycle?: 'Monthly' | 'Yearly';
    price?: number;
    invoices?: Invoice[];
}

export interface Invoice {
    id: string;
    clientId: string;
    stripeInvoiceId: string;
    stripeSubscriptionId?: string;
    amountTotal: number;
    currency: string;
    status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
    dueDate?: string;
    paidAt?: string;
    invoicePdf?: string;
    hostedInvoiceUrl?: string;
    clientName?: string;
}
