import React, { useState, useMemo } from 'react';
import { useInvoices } from '../../hooks';
import type { Invoice } from '../../types';
import { Button, SidePanel } from '../../components/ui';
import { Input } from '../../components/ui/Input';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '../../components/ui/Table';

type InvoiceSortKey = 'paidAt' | 'amountTotal' | 'clientName' | 'status';

const InvoicesTab: React.FC = () => {
    const { data: invoices = [], isLoading } = useInvoices();
    const [searchTerm, setSearchTerm] = useState('');
    const [sort, setSort] = useState<{ key: InvoiceSortKey; direction: 'asc' | 'desc' }>({ key: 'paidAt', direction: 'desc' });
    const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

    const statusColor: { [key: string]: string } = {
        paid: 'bg-green-100 text-green-800',
        open: 'bg-yellow-100 text-yellow-800',
        draft: 'bg-gray-100 text-gray-800',
        uncollectible: 'bg-red-100 text-red-800',
        void: 'bg-gray-100 text-gray-800',
    };

    const filteredAndSortedInvoices = useMemo(() => {
        return invoices
            .filter(invoice => {
                const lowercasedFilter = searchTerm.toLowerCase();
                return (
                    invoice.stripeInvoiceId.toLowerCase().includes(lowercasedFilter) ||
                    invoice.clientName?.toLowerCase().includes(lowercasedFilter)
                );
            })
            .sort((a, b) => {
                const key = sort.key;
                const direction = sort.direction === 'asc' ? 1 : -1;
                
                let valA: any = a[key] || '';
                let valB: any = b[key] || '';

                if (key === 'paidAt') {
                    valA = a.paidAt ? new Date(a.paidAt).getTime() : 0;
                    valB = b.paidAt ? new Date(b.paidAt).getTime() : 0;
                }

                if (typeof valA === 'string' && typeof valB === 'string') {
                    return valA.localeCompare(valB) * direction;
                }
                if (valA < valB) return -1 * direction;
                if (valA > valB) return 1 * direction;
                return 0;
            });
    }, [invoices, searchTerm, sort]);

    const handleSort = (key: InvoiceSortKey) => {
        setSort(prev => ({ key, direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc' }));
    };

    const DetailItem: React.FC<{ label: string, value: React.ReactNode }> = ({ label, value }) => (
        <div>
            <p className="text-xs text-gray-500 font-medium uppercase tracking-wider">{label}</p>
            <div className="text-sm text-black mt-1 break-all">{value || 'N/A'}</div>
        </div>
    );

    if (isLoading) {
        return <div className="text-center p-20"><i className="fa-solid fa-spinner fa-spin text-4xl text-gray-400"></i></div>;
    }

    return (
        <>
            <div className="bg-white border border-gray-300">
                <div className="p-4 border-b border-gray-200">
                    <Input 
                        type="text"
                        placeholder="Search by Invoice ID or Company..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full sm:w-80"
                    />
                </div>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Invoice ID</TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort('clientName')}>Client</TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort('paidAt')}>Date</TableHead>
                            <TableHead className="cursor-pointer text-right" onClick={() => handleSort('amountTotal')}>Amount</TableHead>
                            <TableHead className="cursor-pointer text-center" onClick={() => handleSort('status')}>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredAndSortedInvoices.map(invoice => (
                            <TableRow key={invoice.id} onClick={() => setSelectedInvoice(invoice)} className="cursor-pointer">
                                <TableCell className="font-mono text-xs text-gray-800">{invoice.stripeInvoiceId}</TableCell>
                                <TableCell className="font-semibold text-black">{invoice.clientName}</TableCell>
                                <TableCell className="text-gray-600">{invoice.paidAt ? new Date(invoice.paidAt).toLocaleDateString() : 'N/A'}</TableCell>
                                <TableCell className="text-right font-medium text-black">${(invoice.amountTotal / 100).toFixed(2)}</TableCell>
                                <TableCell className="text-center">
                                    <span className={`px-2 py-1 text-xs font-medium capitalize ${statusColor[invoice.status]}`}>{invoice.status}</span>
                                </TableCell>
                                <TableCell className="text-right">
                                    <Button variant="secondary" size="sm" onClick={(e) => { e.stopPropagation(); setSelectedInvoice(invoice); }}>Details</Button>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            <SidePanel
                isOpen={!!selectedInvoice}
                onClose={() => setSelectedInvoice(null)}
                title={`Invoice Details`}
                size="lg"
            >
                {selectedInvoice && (
                    <div className="space-y-6">
                        <div>
                           <p className="text-lg font-bold text-black">{selectedInvoice.clientName}</p>
                           <p className="text-sm text-gray-500 font-mono">ID: {selectedInvoice.stripeInvoiceId}</p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-y-4 gap-x-6 p-4 bg-gray-50 border border-gray-200">
                             <DetailItem label="Status" value={
                                 <span className={`px-2 py-1 text-xs font-medium capitalize ${statusColor[selectedInvoice.status] || 'bg-gray-100 text-gray-800'}`}>
                                     {selectedInvoice.status}
                                 </span>
                             } />
                             <DetailItem label="Total Amount" value={`$${(selectedInvoice.amountTotal / 100).toFixed(2)} ${selectedInvoice.currency.toUpperCase()}`} />
                             <DetailItem label="Date Paid" value={selectedInvoice.paidAt ? new Date(selectedInvoice.paidAt).toLocaleString() : 'Not Paid'} />
                             <DetailItem label="Due Date" value={selectedInvoice.dueDate ? new Date(selectedInvoice.dueDate).toLocaleDateString() : 'N/A'} />
                        </div>

                        <div className="space-y-4">
                             <DetailItem label="Stripe Subscription ID" value={selectedInvoice.stripeSubscriptionId || 'N/A'} />
                        </div>
                        
                        <div className="pt-4 border-t border-gray-200 flex flex-col sm:flex-row gap-2">
                             <a href={selectedInvoice.hostedInvoiceUrl} target="_blank" rel="noopener noreferrer" className="flex-1">
                                <Button variant="secondary" className="w-full">View on Stripe</Button>
                             </a>
                             <a href={selectedInvoice.invoicePdf} target="_blank" rel="noopener noreferrer" className="flex-1">
                                <Button className="w-full">Download PDF</Button>
                             </a>
                        </div>
                    </div>
                )}
            </SidePanel>
        </>
    );
};

export default InvoicesTab;