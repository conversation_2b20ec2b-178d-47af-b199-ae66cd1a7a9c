// Database schema types for Supabase
// These types represent the exact structure of database tables

export interface DatabaseClient {
  id: string; // uuid
  user_id: string; // uuid
  role: string;
  email: string;
  created_at?: string;
  stripe_customer_id?: string;
  // Profile data
  company_name?: string;
  industry?: string;
  company_size?: string;
  website?: string;
  description?: string;
  primary_contact?: string;
  title?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  billing_email?: string;
  billing_contact?: string;
  billing_phone?: string;
  account_number?: string;
  payment_method?: string;
  payment_method_last4?: string;
  billing_address?: string;
  account_status?: string;
  subscription_tier?: string;
  member_since?: string;
  auto_renewal?: boolean;
  notifications_product_updates?: boolean;
  notifications_event_updates?: boolean;
  notifications_newsletter?: boolean;
  notifications_account_alerts?: boolean;
  cancelled_package_ids?: string[];
}

export interface DatabaseCartItem {
  id: string; // uuid
  client_id: string; // uuid
  package_id: string;
  name: string;
  price: number;
  billing: string;
  features: string[];
  is_addon?: boolean;
  category: string;
  final_price: number;
  final_billing: string;
  added_at: string;
}

export interface DatabaseOrder {
  id: string;
  client_id: string;
  created_at: string;
  subtotal: number;
  vat_amount: number;
  total: number;
  status: 'Completed' | 'Processing' | 'Cancelled' | 'Refunded';
  stripe_payment_intent_id?: string;
  stripe_invoice_id?: string;
  paid_at?: string;
  discount_amount?: number;
}

export interface DatabaseOrderItem {
  id: string; // uuid
  order_id: string;
  package_id?: string;
  name: string;
  price: number;
  billing: string;
  features: string[];
  is_addon?: boolean;
  final_price: number;
  final_billing: string;
  category: string;
}

export interface DatabaseOrderDiscountCode {
  id: string; // uuid
  order_id: string;
  discount_code_id?: string; // uuid
  code: string;
  type: string;
  value: number;
}

export interface DatabaseConversation {
  id: string;
  client_id: string;
  subject: string;
  last_updated: string;
  status: 'open' | 'closed';
  client_has_unread: boolean;
  admin_has_unread: boolean;
  created_at?: string;
}

export interface DatabaseMessage {
  id: string;
  conversation_id: string;
  sender: 'client' | 'admin';
  content: string;
  created_at: string;
}

export interface DatabaseContactSubmission {
  id: string;
  name: string;
  company_name?: string;
  email: string;
  phone?: string;
  subject: 'Sales Inquiry' | 'Support Request' | 'Partnership' | 'General Question';
  message: string;
  submitted_at: string;
  is_read: boolean;
  is_archived: boolean;
}

export interface DatabasePackage {
  id: string;
  category_id: string;
  name: string;
  price: number;
  billing: string; // '/month' | '/year' | 'one-time'
  features: string[]; // text[] array
  is_addon?: boolean;
  cta_text?: string;
  cta_link?: string;
  order_index?: number;
  is_enabled?: boolean;
  valid_from?: string;
  valid_to?: string;
}

export interface DatabaseBillingType {
  id: string;
  name: string;
  public_title?: string;
  description?: string;
  is_system_type?: boolean;
}

export interface DatabaseBillingOption {
  id: string;
  billing_type_id: string;
  label: string;
  multiplier: number;
  discount: number;
  is_partner_tier?: boolean;
  benefit_text?: string;
  is_enabled?: boolean;
  valid_from?: string;
  valid_to?: string;
  order_index?: number;
}

export interface DatabaseCategoryMetadata {
  id: string;
  tab_title: string;
  page_title: string;
  description?: string;
  billing_type_id?: string;
  is_enabled?: boolean;
  valid_from?: string;
  valid_to?: string;
  order_index?: number;
}

export interface DatabaseServiceCategory {
    id: string;
    title: string;
    description?: string;
    items: string[];
    link_to_tab?: string;
    order_index?: number;
    is_enabled?: boolean;
}

export interface DatabasePaymentSettings {
  id: number;
  stripe_enabled: boolean;
  stripe_public_key?: string;
  stripe_secret_key?: string;
  method_credit_card?: boolean;
  method_apple_pay?: boolean;
  method_google_pay?: boolean;
}

export interface DatabaseSmtpSettings {
  id: number;
  test_mode: boolean;
  server?: string;
  port?: number;
  username?: string;
  password?: string;
  encryption?: string; // 'none' | 'ssl' | 'starttls'
  from_address?: string;
  from_name?: string;
  test_email_recipient?: string;
}

// New tables for Stripe integration
export interface DatabaseSubscription {
    id: string;
    client_id: string;
    package_id?: string;
    stripe_subscription_id: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
    trial_start?: string;
    trial_end?: string;
    canceled_at?: string;
    cancel_at_period_end: boolean;
    created_at: string;
    updated_at: string;
    last_payment_error_message?: string;
}

export interface DatabaseStripeProduct {
    id: string;
    package_id: string;
    stripe_product_id: string;
    stripe_price_id?: string;
    price_type?: string;
    billing_interval?: string;
    created_at: string;
}

export interface DatabaseInvoice {
    id: string;
    client_id: string;
    stripe_invoice_id: string;
    stripe_subscription_id?: string;
    amount_total: number;
    currency: string;
    status: string;
    due_date?: string;
    paid_at?: string;
    invoice_pdf?: string;
    hosted_invoice_url?: string;
    created_at: string;
}

export interface DatabasePaymentLog {
    id: string;
    action: string;
    stripe_id: string;
    amount?: number;
    currency?: string;
    customer_id?: string;
    metadata?: string;
    created_at: string;
}

export interface DatabaseNotification {
    id: string;
    client_id: string;
    type: string;
    message: string;
    stripe_subscription_id?: string;
    stripe_invoice_id?: string;
    read_at?: string;
    created_at: string;
}

export interface DatabaseAnnouncementSettings {
  id: number;
  is_enabled: boolean;
  message?: string;
  start_date?: string | null;
  end_date?: string | null;
}

export interface DatabaseDiscountCode {
  id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  valid_from?: string | null;
  valid_to?: string | null;
  is_stackable: boolean;
  is_active: boolean;
  created_at?: string;
}

export interface DatabaseEmailTemplate {
  id: string;
  subject: string;
  body: string;
  description?: string;
  variables: string[];
}


// Database table names (for type safety in queries)
export type DatabaseTableName =
  | 'clients'
  | 'orders'
  | 'conversations'
  | 'messages'
  | 'contact_submissions'
  | 'packages'
  | 'billing_types'
  | 'billing_options'
  | 'cart_items'
  | 'order_items'
  | 'order_discount_codes'
  | 'categories_metadata'
  | 'payment_settings'
  | 'smtp_settings'
  | 'service_categories'
  | 'subscriptions'
  | 'stripe_products'
  | 'invoices'
  | 'payment_logs'
  | 'notifications'
  | 'announcement_settings'
  | 'discount_codes'
  | 'email_templates';

// Union type for all database schemas
export type Database = {
  public: {
    Tables: {
      clients: {
        Row: DatabaseClient;
        Insert: any;
        Update: any;
      };
      cart_items: {
        Row: DatabaseCartItem;
        Insert: any;
        Update: any;
      };
      orders: {
        Row: DatabaseOrder;
        Insert: any;
        Update: any;
      };
      order_items: {
        Row: DatabaseOrderItem;
        Insert: any;
        Update: any;
      };
      order_discount_codes: {
        Row: DatabaseOrderDiscountCode;
        Insert: any;
        Update: any;
      };
      conversations: {
        Row: DatabaseConversation;
        Insert: any;
        Update: any;
      };
      messages: {
        Row: DatabaseMessage;
        Insert: any;
        Update: any;
      };
      contact_submissions: {
        Row: DatabaseContactSubmission;
        Insert: any;
        Update: any;
      };
      packages: {
        Row: DatabasePackage;
        Insert: any;
        Update: any;
      };
      billing_types: {
        Row: DatabaseBillingType;
        Insert: any;
        Update: any;
      };
       billing_options: {
        Row: DatabaseBillingOption;
        Insert: any;
        Update: any;
      };
      categories_metadata: {
        Row: DatabaseCategoryMetadata;
        Insert: any;
        Update: any;
      };
      service_categories: {
        Row: DatabaseServiceCategory;
        Insert: any;
        Update: any;
      };
      payment_settings: {
        Row: DatabasePaymentSettings;
        Insert: any;
        Update: any;
      };
      smtp_settings: {
        Row: DatabaseSmtpSettings;
        Insert: any;
        Update: any;
      };
      subscriptions: {
          Row: DatabaseSubscription;
          Insert: any;
          Update: any;
      };
      stripe_products: {
          Row: DatabaseStripeProduct;
          Insert: any;
          Update: any;
      };
      invoices: {
          Row: DatabaseInvoice;
          Insert: any;
          Update: any;
      };
      payment_logs: {
          Row: DatabasePaymentLog;
          Insert: any;
          Update: any;
      };
      notifications: {
          Row: DatabaseNotification;
          Insert: any;
          Update: any;
      };
      announcement_settings: {
        Row: DatabaseAnnouncementSettings;
        Insert: any;
        Update: any;
      };
      discount_codes: {
        Row: DatabaseDiscountCode;
        Insert: any;
        Update: any;
      };
      email_templates: {
        Row: DatabaseEmailTemplate;
        Insert: any;
        Update: any;
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};
