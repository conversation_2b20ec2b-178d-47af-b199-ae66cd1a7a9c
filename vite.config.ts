import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    return {
      plugins: [react()],
      resolve: {
        alias: {
          '@': fileURLToPath(new URL('./', import.meta.url))
        }
      },
      build: {
        rollupOptions: {
          external: []
        }
      }
    };
});
