import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/useAuthStore';
import { Button } from '@/components/ui';
import { supabase } from '@/lib/supabaseClient';
import { useToastStore } from '@/stores/useToastStore';

const ImpersonationBanner: React.FC = () => {
  const navigate = useNavigate();
  const { isImpersonating, impersonatedClient, endImpersonation } = useAuthStore();
  const [isSending, setIsSending] = useState(false);
  const { showToast } = useToastStore();

  const handleEndImpersonation = async () => {
    endImpersonation();
    navigate('/admin');
  };

  if (!isImpersonating || !impersonatedClient) {
    return null;
  }

  const isPendingSetup = impersonatedClient.profile?.accountStatus === 'Pending Setup';

  const handleSendSetupLink = async () => {
    if (!impersonatedClient) return;
    setIsSending(true);
    try {
        const { error } = await supabase.functions.invoke('send-setup-link', {
            body: { clientId: impersonatedClient.id }
        });
        if (error) throw error;
        showToast('Setup and payment link has been sent to the client.', 'success');
    } catch(err: any) {
        console.error("Failed to send setup link:", err);
        showToast(`Error: ${err.message}`, 'error');
    } finally {
        setIsSending(false);
    }
  };
  
  return (
    <div className="bg-yellow-400 text-black h-12 flex items-center justify-center px-4 z-40 border-b-2 border-yellow-500">
      <div className="container mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
              <i className="fa-solid fa-user-secret text-xl"></i>
              <p className="text-sm font-semibold">
                  You are currently shopping for: <span className="font-bold">{impersonatedClient.profile.companyName}</span>
              </p>
          </div>
          <div className="flex items-center gap-2">
            {isPendingSetup ? (
                <Button onClick={handleSendSetupLink} size="sm" isLoading={isSending} disabled={isSending}>
                    Send Setup & Payment Link
                </Button>
            ) : null}
             <Button onClick={handleEndImpersonation} size="sm" variant="secondary">
              Exit Session
            </Button>
          </div>
      </div>
    </div>
  );
};

export default ImpersonationBanner;