import React, { useEffect } from 'react';
import { useToastStore } from '../stores/useToastStore';

const Toast: React.FC = () => {
  const { isVisible, message, type, hideToast } = useToastStore();

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        hideToast();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, hideToast]);

  if (!isVisible) return null;

  const baseClasses = 'fixed top-20 left-1/2 -translate-x-1/2 z-[200] px-6 py-4 text-sm font-semibold flex items-center gap-4 border shadow-lg animate-slide-down';
  const typeClasses = {
    success: 'bg-green-50 border-green-300 text-green-800',
    error: 'bg-red-50 border-red-300 text-red-800',
  };
  const iconClasses = {
    success: 'fa-solid fa-check-circle',
    error: 'fa-solid fa-exclamation-circle',
  };

  return (
    <div className={`${baseClasses} ${typeClasses[type]}`} role="alert">
      <i className={iconClasses[type]}></i>
      <span>{message}</span>
      <button onClick={hideToast} className="ml-4 opacity-70 hover:opacity-100">
        <i className="fa-solid fa-xmark"></i>
      </button>
      <style>{`
        @keyframes slide-down {
            from { top: 0; opacity: 0; }
            to { top: 80px; opacity: 1; }
        }
        .animate-slide-down { animation: slide-down 0.5s ease-out forwards; }
      `}</style>
    </div>
  );
};

export default Toast;
