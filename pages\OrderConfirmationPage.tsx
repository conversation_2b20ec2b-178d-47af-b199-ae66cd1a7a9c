import React from 'react';
import { useLocation, Link, Navigate } from 'react-router-dom';
import type { Order } from '../types';
import { Button } from '../components/ui/Button';

const OrderConfirmationPage: React.FC = () => {
  const location = useLocation();
  const order = location.state?.order as Order | undefined;

  if (!order) {
    // If there's no order in the state, the user probably navigated here directly.
    // Redirect them to their profile page.
    return <Navigate to="/profile" replace />;
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="bg-white p-8 sm:p-12 border border-gray-300 text-center">
        <div className="w-16 h-16 bg-black text-white flex items-center justify-center mx-auto mb-6">
          <i className="fa-solid fa-check text-3xl"></i>
        </div>
        <h1 className="text-3xl font-bold text-black mb-2">Thank You For Your Order!</h1>
        <p className="text-gray-600 mb-2">
          Your order has been confirmed. You can view your order history and manage your services in your profile.
        </p>
        <p className="text-sm text-gray-500 mb-8">
          A confirmation email with your invoice attached has been sent to you. You can also access all your invoices from your profile at any time.
        </p>

        <div className="bg-gray-50 border border-gray-200 p-6 text-left my-8">
          <h2 className="text-xl font-semibold mb-6 border-b border-gray-200 pb-4 text-black">Order Summary</h2>
          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-sm">
                <span className="text-gray-600">Order ID</span>
                <span className="font-mono text-xs text-gray-800">{order.id}</span>
            </div>
            <div className="flex justify-between text-sm">
                <span className="text-gray-600">Order Date</span>
                <span className="font-medium text-black">{new Date(order.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
          
          <div className="space-y-4 mb-6 border-t border-gray-200 pt-4">
            {order.items.map(item => (
              <div key={item.cartId} className="flex justify-between items-start">
                <div>
                  <p className="font-semibold text-black">{item.name}</p>
                  <p className="text-sm text-gray-500">{item.finalBilling}</p>
                </div>
                <p className="font-semibold text-black">${item.finalPrice.toFixed(2)}</p>
              </div>
            ))}
          </div>

          <div className="pt-4 border-t border-gray-200 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal</span>
              <span className="font-medium text-black">${order.subtotal.toFixed(2)}</span>
            </div>
             {order.discountAmount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                    <span>Discounts</span>
                    <span>-${order.discountAmount.toFixed(2)}</span>
                </div>
            )}
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">VAT (20%)</span>
              <span className="font-medium text-black">${order.vatAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-lg font-bold border-t border-gray-300 pt-2 mt-2">
              <span className="text-black">Total Paid</span>
              <span className="text-black">${order.total.toFixed(2)}</span>
            </div>
          </div>
        </div>

        <Link to="/profile">
          <Button size="lg" className="w-full">
            Go to My Profile & Orders
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
