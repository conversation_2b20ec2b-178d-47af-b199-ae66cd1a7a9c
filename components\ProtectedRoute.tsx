import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/useAuthStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requireAdmin = false }) => {
  const navigate = useNavigate();
  const { session, user, isLoading } = useAuthStore();

  useEffect(() => {
    if (isLoading) return;

    if (!session) {
      navigate('/login');
    } else if (requireAdmin && user.type !== 'admin') {
      navigate('/');
    }
  }, [session, user.type, isLoading, requireAdmin, navigate]);

  if (isLoading || !session || (requireAdmin && user.type !== 'admin')) {
    return null; // Render nothing while loading or if redirecting
  }
  
  return <>{children}</>;
};

export default ProtectedRoute;
