import React from 'react';

const SkeletonCard: React.FC = () => {
  return (
    <div className="bg-white border border-gray-200 p-6 animate-pulse">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 space-y-2">
          <div className="h-6 bg-gray-200 w-3/4"></div>
        </div>
        <div className="w-10 h-10 bg-gray-200"></div>
      </div>
      
      <div className="flex items-baseline gap-2 mb-4">
        <div className="h-10 bg-gray-200 w-1/3"></div>
      </div>

      <div className="mb-6 flex-grow border-t border-gray-200 pt-4 space-y-3">
        <div className="flex items-start">
          <div className="w-4 h-4 bg-gray-200 mr-2 mt-0.5 shrink-0"></div>
          <div className="h-4 bg-gray-200 w-full"></div>
        </div>
        <div className="flex items-start">
          <div className="w-4 h-4 bg-gray-200 mr-2 mt-0.5 shrink-0"></div>
          <div className="h-4 bg-gray-200 w-5/6"></div>
        </div>
        <div className="flex items-start">
          <div className="w-4 h-4 bg-gray-200 mr-2 mt-0.5 shrink-0"></div>
          <div className="h-4 bg-gray-200 w-full"></div>
        </div>
      </div>

      <div className="w-full mt-auto h-11 bg-gray-200"></div>
    </div>
  );
};

export default SkeletonCard;