import React from 'react';
import { Link } from 'react-router-dom';

interface EmailVerificationPageProps {}

const EmailVerificationPage: React.FC<EmailVerificationPageProps> = () => {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="max-w-lg w-full mx-auto text-center bg-white p-8 border border-gray-300">
        <div className="w-16 h-16 bg-black text-white flex items-center justify-center mx-auto mb-6">
          <i className="fa-solid fa-envelope-open-text text-3xl"></i>
        </div>
        <h2 className="text-3xl font-bold mb-2 text-black">Check Your Inbox</h2>
        <p className="text-gray-600 mb-8">
          We've sent a verification link to your email address. Please click the link to activate your account.
        </p>
        <Link
          to="/login"
          className="block w-full bg-black text-white py-3 px-6 hover:bg-gray-800 font-semibold transition-colors"
        >
          Back to Login
        </Link>
      </div>
    </div>
  );
};

export default EmailVerificationPage;
