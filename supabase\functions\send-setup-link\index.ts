import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../shared/cors.ts';

// FIX: Declare <PERSON>o global to satisfy TypeScript compiler in this environment.
declare const Deno: any;

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabaseAdmin = createClient(
            Deno.env.get('SUPABASE_URL')!,
            Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
        );

        const authorization = req.headers.get('Authorization')!;
        const { data: { user: adminUser } } = await supabaseAdmin.auth.getUser(authorization.replace('Bearer ', ''));
        
        const { data: clientProfile, error: profileError } = await supabaseAdmin
            .from('clients')
            .select('role')
            .eq('user_id', adminUser.id)
            .single();

        if (profileError || clientProfile?.role !== 'admin') {
             throw new Error("Unauthorized");
        }

        const { clientId } = await req.json();
        if (!clientId) {
            return new Response(
                JSON.stringify({ error: "Client ID is required." }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }

        // 1. Get Client data and Email Template
        const { data: clientData, error: clientError } = await supabaseAdmin
            .from('clients')
            .select('*, cart_items(*)')
            .eq('id', clientId)
            .single();
        if (clientError || !clientData) throw clientError || new Error("Client not found.");

        const { data: emailTemplate, error: templateError } = await supabaseAdmin
            .from('email_templates')
            .select('subject, body')
            .eq('id', 'client_setup_link')
            .single();
        if (templateError || !emailTemplate) throw templateError || new Error("Email template 'client_setup_link' not found.");

        // 2. Generate Magic Link
        const clientEmail = clientData.email;
        const { data: linkData, error: linkError } = await supabaseAdmin.auth.admin.generateLink({
            type: 'magiclink',
            email: clientEmail,
            options: {
                redirectTo: '/complete-setup'
            }
        });
        if (linkError) throw linkError;
        const magicLink = linkData.properties.action_link;

        // 3. Prepare Template Variables
        const cartItems = clientData.cart_items || [];
        const subtotal = cartItems.reduce((acc, item) => acc + item.final_price, 0);
        const vat = subtotal * 0.20;
        const total = subtotal + vat;
        const cartItemsHtml = cartItems.map(item => `<li>${item.name} - $${item.final_price.toFixed(2)}</li>`).join('');

        // 4. Compose Email by replacing variables
        const emailSubject = emailTemplate.subject.replace(/{{companyName}}/g, clientData.company_name);
        const emailHtml = emailTemplate.body
            .replace(/{{companyName}}/g, clientData.company_name)
            .replace(/{{cartItemsList}}/g, cartItemsHtml)
            .replace(/{{cartTotal}}/g, total.toFixed(2))
            .replace(/{{setupLink}}/g, magicLink);

        // 5. Invoke the centralized 'send-email' function
        const { error: emailError } = await supabaseAdmin.functions.invoke('send-email', {
            body: {
                to: clientEmail,
                subject: emailSubject,
                html: emailHtml
            },
            // Pass the original authorization header to the invoked function
            headers: {
                Authorization: authorization
            }
        });

        if (emailError) {
            // Check if the error is from the function itself
            if (emailError.context && emailError.context.error) {
                 throw new Error(`Failed to send email: ${emailError.context.error.message}`);
            }
            throw emailError;
        }

        return new Response(JSON.stringify({ success: true, message: 'Setup link sent.' }), { headers: { ...corsHeaders, 'Content-Type': 'application/json' } });

    } catch (error) {
        console.error('Error in send-setup-link function:', error);
        return new Response(JSON.stringify({ error: error.message }), { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } });
    }
});