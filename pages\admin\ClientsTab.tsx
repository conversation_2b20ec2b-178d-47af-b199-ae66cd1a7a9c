import React, { useState, useMemo, useEffect } from 'react';
import type { Client, ProfileData } from '../../types';
import { useAuthStore } from '../../stores/useAuthStore';
import { useAdminActions } from '../../hooks';
import { Button, ConfirmationModal, SidePanel, FormField } from '../../components/ui';
import CreateClientDrawer from './CreateClientDrawer';
import { useQueryClient } from '@tanstack/react-query';
import { useToastStore } from '../../stores/useToastStore';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '../../components/ui/Table';
import { Input } from '../../components/ui/Input';
import { supabase } from '../../lib/supabaseClient';

interface ClientsTabProps {
    clients: Client[];
    activeClient: Client | null;
    setActiveClient: (client: Client | null) => void;
}

type ClientSortKey = 'companyName' | 'primaryContact' | 'email' | 'accountStatus';

// Section component for the detail view
const DetailSection: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
    <div className="border border-gray-200">
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
            <h4 className="font-semibold text-black">{title}</h4>
        </div>
        <div className="p-4 grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-6">
            {children}
        </div>
    </div>
);

// Read-only detail item
const DetailItem: React.FC<{ label: string, value: React.ReactNode }> = ({ label, value }) => (
    <div>
        <p className="text-xs text-gray-500 font-medium uppercase tracking-wider">{label}</p>
        <div className="text-sm text-black mt-1 break-words">{value || <span className="text-gray-400">N/A</span>}</div>
    </div>
);

const ClientsTab: React.FC<ClientsTabProps> = ({ clients, activeClient, setActiveClient }) => {
    const { startImpersonation } = useAuthStore();
    const adminActions = useAdminActions();
    const { showToast } = useToastStore();
    const [searchTerm, setSearchTerm] = useState('');
    const [sort, setSort] = useState<{ key: ClientSortKey; direction: 'asc' | 'desc' }>({ key: 'companyName', direction: 'asc' });
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const queryClient = useQueryClient();

    // State for editing a client profile
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [editableProfile, setEditableProfile] = useState<ProfileData | null>(null);
    const [isSyncing, setIsSyncing] = useState(false);

    // When a new client is selected, reset the editing state
    useEffect(() => {
        if (activeClient) {
            setEditableProfile(activeClient.profile);
            setIsEditing(false);
        }
    }, [activeClient]);


    const filteredAndSortedClients = useMemo(() => {
        if (!clients || clients.length === 0) return [];
        return clients
            .filter(client => {
                if (!client.profile) return false; // Guard against missing profile
                const lowercasedFilter = searchTerm.toLowerCase();
                // Defensive check for each property, defaulting to empty string
                const companyName = client.profile.companyName || '';
                const primaryContact = client.profile.primaryContact || '';
                const email = client.profile.email || '';
                return (
                    companyName.toLowerCase().includes(lowercasedFilter) ||
                    primaryContact.toLowerCase().includes(lowercasedFilter) ||
                    email.toLowerCase().includes(lowercasedFilter)
                );
            })
            .sort((a, b) => {
                const key = sort.key;
                const direction = sort.direction === 'asc' ? 1 : -1;
                // Defensive check for profile in sort as well, using optional chaining.
                const valA = a.profile?.[key] || '';
                const valB = b.profile?.[key] || '';

                if (typeof valA === 'string' && typeof valB === 'string') {
                    return valA.localeCompare(valB) * direction;
                }
                if (valA < valB) return -1 * direction;
                if (valA > valB) return 1 * direction;
                return 0;
            });
    }, [searchTerm, clients, sort]);

    const handleCreationSuccess = (newClient: Client) => {
        queryClient.invalidateQueries({ queryKey: ['clients'] });
        setIsCreateModalOpen(false);
        setActiveClient(newClient); // Open the new client in the drawer
    };

    const handleDeleteClient = async () => {
        if (!activeClient) return;
        setIsDeleting(true);
        try {
            await adminActions.deleteRecord('clients', activeClient.id);
            setActiveClient(null);
            showToast('Client deleted successfully.', 'success');
        } catch (error) {
            console.error("Failed to delete client:", error);
            const message = error instanceof Error ? error.message : String(error);
            showToast(`Failed to delete client: ${message}`, 'error');
        } finally {
            setIsDeleting(false);
            setIsDeleteModalOpen(false);
        }
    };
    
    const handleSort = (key: ClientSortKey) => {
        setSort(prevSort => ({
            key,
            direction: prevSort.key === key && prevSort.direction === 'asc' ? 'desc' : 'asc',
        }));
    };
    
    const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        if (!editableProfile) return;
        const { name, value } = e.target;
        setEditableProfile({ ...editableProfile, [name]: value });
    };

    const handleSaveChanges = async () => {
        if (!activeClient || !editableProfile) return;
        setIsSaving(true);
        try {
            await adminActions.updateRecord('clients', { profile: editableProfile }, activeClient.id);
            setIsEditing(false);
            showToast('Client profile updated successfully!', 'success');
        } catch (error) {
            console.error("Failed to update profile", error);
            showToast(`Error: ${error instanceof Error ? error.message : 'Failed to update profile'}`, 'error');
        } finally {
            setIsSaving(false);
        }
    };

    const handleSyncWithStripe = async () => {
        if (!activeClient) return;
        setIsSyncing(true);
        try {
            const { data, error } = await supabase.functions.invoke('stripe-customer-sync', {
                body: { clientId: activeClient.id },
            });
    
            if (error) throw error;
            if (data.error) throw new Error(data.error);
            
            showToast(data.message || 'Client synced with Stripe successfully!', 'success');
            queryClient.invalidateQueries({ queryKey: ['clients'] });
        } catch (err: any) {
            console.error('Failed to sync client with Stripe:', err);
            showToast(`Stripe Sync Failed: ${err.message}`, 'error');
        } finally {
            setIsSyncing(false);
        }
    };

    const statusColor: { [key: string]: string } = {
        'Active': 'bg-green-100 text-green-800',
        'Inactive': 'bg-gray-100 text-gray-800',
        'Pending Setup': 'bg-yellow-100 text-yellow-800',
    };

    const renderSidePanelFooter = () => (
        <div className="flex justify-between items-center">
            <div>
                {isEditing ? (
                    <div className="flex gap-2">
                        <Button onClick={handleSaveChanges} isLoading={isSaving} disabled={isSaving}>Save Changes</Button>
                        <Button variant="secondary" onClick={() => setIsEditing(false)} disabled={isSaving}>Cancel</Button>
                    </div>
                ) : (
                    <Button onClick={() => setIsEditing(true)}>Edit Profile</Button>
                )}
            </div>
            <div className="flex gap-2">
                <Button onClick={() => activeClient && startImpersonation(activeClient)} variant="secondary" size="sm" className="flex items-center gap-2">
                    <i className="fa-solid fa-user-secret"></i> Impersonate
                </Button>
                <Button onClick={() => setIsDeleteModalOpen(true)} variant="danger" size="sm" className="flex items-center gap-2">
                    <i className="fa-solid fa-trash-can"></i>
                </Button>
            </div>
        </div>
    );
    

    return (
        <>
            <div className="bg-white border border-gray-300">
                <div className="p-4 border-b border-gray-200 flex items-center justify-between gap-4 flex-wrap">
                    <Input 
                      type="text" 
                      placeholder="Search by company, contact, or email..." 
                      value={searchTerm} 
                      onChange={(e) => setSearchTerm(e.target.value)} 
                      className="w-full sm:w-80"
                    />
                    <Button onClick={() => setIsCreateModalOpen(true)}>
                        <i className="fa-solid fa-plus mr-2"></i> Create New Client
                    </Button>
                </div>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="cursor-pointer" onClick={() => handleSort('companyName')}>Company Name <i className="fa-solid fa-sort"></i></TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort('primaryContact')}>Primary Contact <i className="fa-solid fa-sort"></i></TableHead>
                            <TableHead className="cursor-pointer" onClick={() => handleSort('email')}>Email <i className="fa-solid fa-sort"></i></TableHead>
                            <TableHead className="text-center cursor-pointer" onClick={() => handleSort('accountStatus')}>Status <i className="fa-solid fa-sort"></i></TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredAndSortedClients.map(client => (
                            <TableRow key={client.id} onClick={() => setActiveClient(client)} className="cursor-pointer">
                                <TableCell className="font-semibold text-black">{client.profile.companyName}</TableCell>
                                <TableCell className="text-black">{client.profile.primaryContact}</TableCell>
                                <TableCell className="text-gray-600">{client.profile.email}</TableCell>
                                <TableCell className="text-center">
                                    <span className={`px-2 py-1 text-xs font-medium ${statusColor[client.profile.accountStatus] || 'bg-gray-100'}`}>
                                        {client.profile.accountStatus}
                                    </span>
                                </TableCell>
                                <TableCell className="text-right">
                                    <Button onClick={(e) => { e.stopPropagation(); setActiveClient(client); }} variant="secondary" size="sm">Details</Button>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            <SidePanel
                isOpen={!!activeClient}
                onClose={() => setActiveClient(null)}
                title={activeClient?.profile.companyName || 'Client Details'}
                size="3xl"
                footer={activeClient ? renderSidePanelFooter() : undefined}
            >
                {activeClient && editableProfile && (
                    <div className="space-y-6">
                        <DetailSection title="Company Information">
                            {isEditing ? <>
                                <FormField label="Company Name" name="companyName" value={editableProfile.companyName} onChange={handleProfileChange} />
                                <FormField label="Industry" name="industry" value={editableProfile.industry} onChange={handleProfileChange} />
                                <FormField label="Company Size" name="companySize" as="select" value={editableProfile.companySize} onChange={handleProfileChange}>
                                    {['1-10 employees', '11-50 employees', '50-100 employees', '101-500 employees', '500+ employees'].map(s => <option key={s} value={s}>{s}</option>)}
                                </FormField>
                                <FormField label="Website" name="website" value={editableProfile.website} onChange={handleProfileChange} />
                                <FormField className="sm:col-span-2" as="textarea" label="Description" name="description" value={editableProfile.description} onChange={handleProfileChange} />
                            </> : <>
                                <DetailItem label="Company Name" value={editableProfile.companyName} />
                                <DetailItem label="Industry" value={editableProfile.industry} />
                                <DetailItem label="Company Size" value={editableProfile.companySize} />
                                <DetailItem label="Website" value={<a href={editableProfile.website} target="_blank" rel="noopener noreferrer" className="text-black hover:underline">{editableProfile.website}</a>} />
                                <div className="sm:col-span-2"><DetailItem label="Description" value={editableProfile.description} /></div>
                            </>}
                        </DetailSection>

                        <DetailSection title="Primary Contact">
                           {isEditing ? <>
                                <FormField label="Contact Name" name="primaryContact" value={editableProfile.primaryContact} onChange={handleProfileChange} />
                                <FormField label="Title" name="title" value={editableProfile.title} onChange={handleProfileChange} />
                                <FormField label="Email" name="email" value={editableProfile.email} onChange={handleProfileChange} type="email" />
                                <FormField label="Phone" name="phone" value={editableProfile.phone} onChange={handleProfileChange} type="tel" />
                            </> : <>
                                <DetailItem label="Contact Name" value={editableProfile.primaryContact} />
                                <DetailItem label="Title" value={editableProfile.title} />
                                <DetailItem label="Email" value={<a href={`mailto:${editableProfile.email}`} className="text-black hover:underline">{editableProfile.email}</a>} />
                                <DetailItem label="Phone" value={editableProfile.phone} />
                           </>}
                        </DetailSection>

                         <DetailSection title="Billing Information">
                           {isEditing ? <>
                                <FormField label="Billing Email" name="billingEmail" value={editableProfile.billingEmail} onChange={handleProfileChange} type="email" />
                                <FormField label="Billing Contact" name="billingContact" value={editableProfile.billingContact} onChange={handleProfileChange} />
                                <FormField label="Billing Phone" name="billingPhone" value={editableProfile.billingPhone} onChange={handleProfileChange} type="tel" />
                                <FormField label="Billing Address" name="billingAddress" value={editableProfile.billingAddress} onChange={handleProfileChange} />
                            </> : <>
                                <DetailItem label="Billing Email" value={editableProfile.billingEmail} />
                                <DetailItem label="Billing Contact" value={editableProfile.billingContact} />
                                <DetailItem label="Billing Phone" value={editableProfile.billingPhone} />
                                <DetailItem label="Billing Address" value={editableProfile.billingAddress} />
                           </>}
                        </DetailSection>

                        <DetailSection title="Integrations">
                            <div className="sm:col-span-2">
                                <p className="text-xs text-gray-500 font-medium uppercase tracking-wider">Stripe Customer</p>
                                <div className="text-sm text-black mt-1 flex items-center gap-2">
                                    {activeClient.stripeCustomerId ? (
                                        <>
                                            <i className="fa-brands fa-stripe text-green-600"></i>
                                            <a href={`https://dashboard.stripe.com/customers/${activeClient.stripeCustomerId}`} target="_blank" rel="noopener noreferrer" className="text-black hover:underline font-mono text-xs">{activeClient.stripeCustomerId}</a>
                                        </>
                                    ) : (
                                        <span className="text-gray-400 flex items-center gap-2 text-xs"><i className="fa-solid fa-triangle-exclamation text-yellow-500"></i> Not Synced</span>
                                    )}
                                    <Button
                                        variant="secondary"
                                        size="sm"
                                        className="ml-auto px-2 py-1 text-xs"
                                        onClick={handleSyncWithStripe}
                                        isLoading={isSyncing}
                                        disabled={isSyncing || isEditing}
                                        title={isEditing ? 'Save profile changes before syncing' : 'Sync client data with Stripe'}
                                    >
                                        {activeClient.stripeCustomerId ? 'Re-Sync' : 'Sync Now'}
                                    </Button>
                                </div>
                            </div>
                        </DetailSection>

                        <DetailSection title="Account Status">
                           {isEditing ? <>
                                <FormField label="Account Status" as="select" name="accountStatus" value={editableProfile.accountStatus} onChange={handleProfileChange}>
                                    {['Active', 'Inactive', 'Pending Setup'].map(s => <option key={s} value={s}>{s}</option>)}
                                </FormField>
                                <FormField label="Subscription Tier" name="subscriptionTier" value={editableProfile.subscriptionTier} onChange={handleProfileChange} />
                            </> : <>
                                <DetailItem label="Account Status" value={<span className={`px-2 py-1 text-xs font-medium ${statusColor[editableProfile.accountStatus] || 'bg-gray-100'}`}>{editableProfile.accountStatus}</span>} />
                                <DetailItem label="Subscription Tier" value={editableProfile.subscriptionTier} />
                           </>}
                            <DetailItem label="Member Since" value={new Date(editableProfile.memberSince).toLocaleDateString()} />
                            <DetailItem label="Account Number" value={editableProfile.accountNumber} />
                        </DetailSection>

                    </div>
                )}
            </SidePanel>

            {isCreateModalOpen && <CreateClientDrawer onClose={() => setIsCreateModalOpen(false)} onSuccess={handleCreationSuccess} />}
            
            <ConfirmationModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleDeleteClient}
                title="Delete Client"
                message={<p>Are you sure you want to delete client <strong>"{activeClient?.profile.companyName}"</strong>? This will remove all their associated data and cannot be undone.</p>}
                confirmText="Delete Client"
                isLoading={isDeleting}
            />
        </>
    );
};

export default ClientsTab;