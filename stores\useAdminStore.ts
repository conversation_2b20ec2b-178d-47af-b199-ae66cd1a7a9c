import { create } from 'zustand';
import {
  ClientService,
  ContactSubmissionService,
  ConversationService,
  MessageService,
  AdminService
} from '@/lib/supabaseService';
import type {
  Client,
  ContactSubmission,
  PaymentSettings,
  SmtpSettings,
  Message,
  Conversation
} from '@/types';

interface AdminState {
  clients: Client[];
  contactSubmissions: ContactSubmission[];
  paymentSettings: PaymentSettings | null;
  smtpSettings: SmtpSettings | null;
}

interface AdminActions {
  setClients: (clients: Client[]) => void;
  setContactSubmissions: (submissions: ContactSubmission[]) => void;
  setPaymentSettings: (settings: PaymentSettings) => void;
  setSmtpSettings: (settings: SmtpSettings) => void;
  loadAdminData: () => Promise<void>;
  updateRecord: (tableName: string, data: any, id: string) => Promise<boolean>;
  createRecord: (tableName: string, data: any) => Promise<boolean>;
  deleteRecord: (tableName: string, id: string) => Promise<boolean>;
  sendMessage: (clientId: string, conversationId: string, content: string) => Promise<void>;
  markConversationAsRead: (clientId: string, conversationId: string) => Promise<void>;
  updateClientInStore: (updatedClient: Client) => void;
}

type AdminStore = AdminState & AdminActions;

export const useAdminStore = create<AdminStore>((set, get) => ({
  // State
  clients: [],
  contactSubmissions: [],
  paymentSettings: null,
  smtpSettings: null,

  // Actions
  setClients: (clients) => set({ clients }),
  setContactSubmissions: (submissions) => set({ contactSubmissions: submissions }),
  setPaymentSettings: (settings) => set({ paymentSettings: settings }),
  setSmtpSettings: (settings) => set({ smtpSettings: settings }),

  loadAdminData: async () => {
    try {
      const [allClientsData, submissionsData, paymentData, smtpData] = await Promise.all([
        ClientService.getAll(),
        ContactSubmissionService.getAll(),
        AdminService.getPaymentSettings(),
        AdminService.getSmtpSettings()
      ]);

      set({
        clients: allClientsData,
        contactSubmissions: submissionsData,
        paymentSettings: paymentData,
        smtpSettings: smtpData
      });
    } catch (error) {
      console.error('Error fetching admin data:', error);
    }
  },

  updateRecord: async (tableName: string, data: any, id: string) => {
    const success = await AdminService.updateRecord(tableName, data, id);
    if (!success) {
      console.error(`Error updating ${tableName}`);
    }
    return success;
  },

  createRecord: async (tableName: string, data: any) => {
    const success = await AdminService.createRecord(tableName, data);
    if (!success) {
      console.error(`Error creating in ${tableName}`);
    }
    return success;
  },

  deleteRecord: async (tableName: string, id: string) => {
    const success = await AdminService.deleteRecord(tableName, id);
    if (!success) {
      console.error(`Error deleting from ${tableName}`);
    }
    return success;
  },

  sendMessage: async (clientId: string, conversationId: string, content: string) => {
    const { clients, setClients } = get();

    const createdMessage = await MessageService.sendFromAdmin(conversationId, content);

    const conversationUpdated = await ConversationService.update(conversationId, {
      lastUpdated: new Date().toISOString(),
      clientHasUnread: true,
      adminHasUnread: false
    });

    if (!createdMessage || !conversationUpdated) {
      console.error('Could not send message');
      return;
    }

    const updatedClients = clients.map(c =>
      c.id === clientId
        ? {
            ...c,
            conversations: c.conversations?.map(convo =>
              convo.id === conversationId
                ? {
                    ...convo,
                    messages: [...convo.messages, createdMessage],
                    lastUpdated: createdMessage.createdAt,
                    adminHasUnread: false,
                    clientHasUnread: true
                  }
                : convo
            )
          }
        : c
    );

    setClients(updatedClients);
  },

  markConversationAsRead: async (clientId: string, conversationId: string) => {
    const { clients, setClients } = get();
    const success = await ConversationService.markAsRead(conversationId, false);
    if (!success) return;

    const updatedClients = clients.map(c =>
      c.id === clientId
        ? {
            ...c,
            conversations: c.conversations?.map(convo =>
              convo.id === conversationId
                ? { ...convo, adminHasUnread: false }
                : convo
            )
          }
        : c
    );

    setClients(updatedClients);
  },

  updateClientInStore: (updatedClient: Client) => {
    const { clients, setClients } = get();
    const updatedClients = clients.map(c =>
      c.id === updatedClient.id ? updatedClient : c
    );
    setClients(updatedClients);
  },
}));