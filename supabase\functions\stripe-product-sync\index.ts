import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';
// FIX: Align Stripe version with other functions.
import Stripe from 'https://esm.sh/stripe@16.2.0';

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabaseAdmin = getSupabaseAdminClient();
        
        // 1. Check for admin privileges
        const { data: { user } } = await supabaseAdmin.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
        
        const { data: clientProfile, error: profileError } = await supabaseAdmin
            .from('clients')
            .select('role')
            .eq('user_id', user.id)
            .single();

        if (profileError || clientProfile?.role !== 'admin') {
            throw new Error("Unauthorized: Only admins can sync products.");
        }

        // 2. Get packageId from request and validate
        const { packageId } = await req.json();
        if (!packageId) {
            return new Response(
                JSON.stringify({ error: "packageId is required." }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }

        // 3. Fetch package details from Supabase
        const { data: packageData, error: packageError } = await supabaseAdmin
            .from('packages')
            .select('*')
            .eq('id', packageId)
            .single();
        
        if (packageError) throw packageError;
        
        const stripe = await getStripeInstance();
        
        // 4. Check if a Stripe product already exists for this package
        const { data: existingMapping } = await supabaseAdmin
            .from('stripe_products')
            .select('*')
            .eq('package_id', packageId)
            .single();

        if (existingMapping) {
            // --- UPDATE EXISTING PRODUCT ---
            const stripeProductId = existingMapping.stripe_product_id;
            const oldStripePriceId = existingMapping.stripe_price_id;

            // Update product details
            await stripe.products.update(stripeProductId, {
                name: packageData.name,
                description: `Package: ${packageData.name} (ID: ${packageData.id})`,
            });
            
            // Check if price needs updating. Prices are immutable, so we create a new one.
            const oldPrice = await stripe.prices.retrieve(oldStripePriceId);
            
            const newPriceAmount = Math.round(packageData.price * 100);
            const newPriceIsRecurring = packageData.billing === '/month' || packageData.billing === '/year';
            const oldPriceIsRecurring = !!oldPrice.recurring;
            
            let priceNeedsUpdate = false;
            if (newPriceAmount !== oldPrice.unit_amount) {
                priceNeedsUpdate = true;
            } else if (newPriceIsRecurring !== oldPriceIsRecurring) {
                priceNeedsUpdate = true;
            } else if (newPriceIsRecurring && oldPrice.recurring?.interval !== (packageData.billing === '/month' ? 'month' : 'year')) {
                priceNeedsUpdate = true;
            }

            if (priceNeedsUpdate) {
                // Create a new price
                const newPriceData: Stripe.PriceCreateParams = {
                    product: stripeProductId,
                    unit_amount: newPriceAmount,
                    currency: 'usd',
                };
                if (newPriceIsRecurring) {
                    newPriceData.recurring = {
                        interval: packageData.billing === '/month' ? 'month' : 'year',
                    };
                }
                const newStripePrice = await stripe.prices.create(newPriceData);
                
                // Update our mapping table with the new price ID
                await supabaseAdmin
                    .from('stripe_products')
                    .update({ stripe_price_id: newStripePrice.id })
                    .eq('package_id', packageId);

                // Archive the old price so it can't be used for new subscriptions
                await stripe.prices.update(oldStripePriceId, { active: false });

                return new Response(
                    JSON.stringify({ success: true, message: 'Product and price updated in Stripe.' }),
                    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
                );
            } else {
                 return new Response(
                    JSON.stringify({ success: true, message: 'Product details updated in Stripe. Price was unchanged.' }),
                    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
                );
            }
        } else {
            // --- CREATE NEW PRODUCT ---
            const stripeProduct = await stripe.products.create({
                name: packageData.name,
                description: `Package: ${packageData.name} (ID: ${packageData.id})`,
                metadata: { supabase_package_id: packageId },
            });

            const priceData: Stripe.PriceCreateParams = {
                product: stripeProduct.id,
                unit_amount: Math.round(packageData.price * 100),
                currency: 'usd',
            };

            if (packageData.billing === '/month' || packageData.billing === '/year') {
                priceData.recurring = {
                    interval: packageData.billing === '/month' ? 'month' : 'year',
                };
            }

            const stripePrice = await stripe.prices.create(priceData);

            // 6. Save the mapping in our database
            const { data: newMapping, error: insertError } = await supabaseAdmin
                .from('stripe_products')
                .insert({
                    package_id: packageId,
                    stripe_product_id: stripeProduct.id,
                    stripe_price_id: stripePrice.id,
                })
                .select()
                .single();

            if (insertError) throw insertError;

            return new Response(
                JSON.stringify({ success: true, message: 'Stripe product and price created successfully.', mapping: newMapping }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }
    } catch (error) {
        console.error('Error syncing Stripe product:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
});
