import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabaseAdmin = getSupabaseAdminClient();
        
        // 1. Check for admin privileges
        const { data: { user } } = await supabaseAdmin.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
        const { data: clientProfile, error: profileError } = await supabaseAdmin
            .from('clients')
            .select('role')
            .eq('user_id', user.id)
            .single();

        if (profileError || clientProfile?.role !== 'admin') {
             throw new Error("Unauthorized: Only admins can issue refunds.");
        }

        // 2. Get data from request and validate
        const { paymentIntentId, orderId } = await req.json();
        if (!paymentIntentId) {
            return new Response(
                JSON.stringify({ error: "paymentIntentId is required." }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }

        // 3. Create the refund on Stripe
        const stripe = await getStripeInstance();
        await stripe.refunds.create({
            payment_intent: paymentIntentId,
        });

        console.log(`Refund initiated for Payment Intent ${paymentIntentId} (Order ID: ${orderId}).`);
        
        // The webhook 'charge.refunded' will handle updating the order status in our DB.

        return new Response(
            JSON.stringify({ success: true, message: `Refund for order ${orderId} initiated.` }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error processing refund:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
});