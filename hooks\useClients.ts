import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ClientService } from '@/lib/supabaseService';
import type { Client } from '@/types';

export const useClients = () => {
  return useQuery({
    queryKey: ['clients'],
    queryFn: ClientService.getAll,
    staleTime: 2 * 60 * 1000, // Client data changes more frequently
  });
};

export const useClient = (id: string) => {
  return useQuery({
    queryKey: ['clients', id],
    queryFn: () => ClientService.getById(id),
    enabled: !!id,
  });
};

export const useClientByUserId = (userId: string) => {
  return useQuery({
    queryKey: ['clients', 'user', userId],
    queryFn: () => ClientService.getByUserId(userId),
    enabled: !!userId,
  });
};

export const useUpdateClient = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, profile }: { id: string; profile: any }) =>
      ClientService.updateProfile(id, profile),
    onSuccess: (_, { id }) => {
      // Invalidate related queries to refetch fresh data
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      queryClient.invalidateQueries({ queryKey: ['clients', id] });
    },
  });
};

export const useCreateClient = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ClientService.create,
    onSuccess: () => {
      // Invalidate clients list to include new client
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    },
  });
};