import React, { ReactNode } from "react";
import { ErrorBoundary as ReactErrorBoundary, FallbackProps } from "react-error-boundary";
import { useQueryClient } from '@tanstack/react-query';

// Log the error to an error reporting service
const logError = (error: Error, info: { componentStack: string }) => {
  console.error("Uncaught error:", error, info.componentStack);
};

// Fallback component to display when an error is caught
function ErrorFallback({ resetErrorBoundary }: FallbackProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100" role="alert">
      <div className="text-center bg-white p-10 border border-red-300 shadow-lg max-w-lg mx-auto">
        <i className="fa-solid fa-triangle-exclamation text-5xl text-red-500 mb-6"></i>
        <h1 className="text-2xl font-bold text-red-600 mb-4">Oops! Something Went Wrong.</h1>
        <p className="text-gray-700 mb-6">
          We've encountered an unexpected error. Our team has been notified. Please try resetting the application state to continue.
        </p>
        <button
          onClick={resetErrorBoundary}
          className="inline-flex items-center justify-center whitespace-nowrap transition-colors focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 bg-black text-white hover:bg-gray-800 px-6 py-3 text-base font-semibold"
        >
          <i className="fa-solid fa-arrows-rotate mr-2"></i>
          Reset Application
        </button>
      </div>
    </div>
  );
}

interface ErrorBoundaryProps {
  children: ReactNode;
}

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ children }) => {
  const queryClient = useQueryClient();

  return (
    <ReactErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={logError}
      onReset={() => {
        queryClient.invalidateQueries();
      }}
    >
      {children}
    </ReactErrorBoundary>
  );
};

export default ErrorBoundary;