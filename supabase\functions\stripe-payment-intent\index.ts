import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@16.2.0';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { amount, customerId, metadata, cartItems, email } = await req.json();
    if (!customerId || !cartItems || !email) {
        return new Response(
            JSON.stringify({ error: "Invalid input. A customerId, cartItems, and email are required." }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }

    const stripe = await getStripeInstance();
    const supabase = getSupabaseAdminClient();

    const recurringItems = cartItems.filter((item: any) => item.billing === '/month' || item.billing === '/year');

    if (recurringItems.length > 0) {
      // --- SUBSCRIPTION WITH OPTIONAL ONE-TIME ITEMS FLOW ---
      const oneTimeItems = cartItems.filter((item: any) => item.billing === 'one-time');
      
      const recurringPackageIds = recurringItems.map((item: any) => item.id);
      const { data: stripeProducts, error: productsError } = await supabase
        .from('stripe_products')
        .select('package_id, stripe_price_id')
        .in('package_id', recurringPackageIds);
      
      if (productsError) throw productsError;

      const foundIds = stripeProducts.map(p => p.package_id);
      const missingIds = recurringPackageIds.filter(id => !foundIds.includes(id));

      if (missingIds.length > 0) {
        throw new Error(`The following products are not synced with Stripe and cannot be purchased as a subscription: ${missingIds.join(', ')}`);
      }

      const subscriptionItems = stripeProducts.map(p => ({ price: p.stripe_price_id }));
      
      // Step 1: Create pending invoice items for all one-time purchases.
      for (const item of oneTimeItems) {
        await stripe.invoiceItems.create({
            customer: customerId,
            amount: Math.round(item.finalPrice * 100),
            currency: 'usd',
            description: `${item.name} (One-Time)`,
        });
      }
      
      // Step 2: Create the subscription. Stripe will automatically sweep up
      // the pending invoice items and add them to the first invoice.
      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: subscriptionItems,
        payment_behavior: 'default_incomplete',
        payment_settings: {
            save_default_payment_method: 'on_subscription',
        },
        expand: ['latest_invoice.payment_intent'],
        metadata,
      });

      const latestInvoice = subscription.latest_invoice as Stripe.Invoice;
      if (!latestInvoice || !latestInvoice.payment_intent) {
          throw new Error('Could not retrieve payment intent from subscription invoice.');
      }
      
      const paymentIntent = latestInvoice.payment_intent as Stripe.PaymentIntent;

      if (!paymentIntent || !paymentIntent.client_secret) {
        throw new Error('Could not create payment intent for subscription.');
      }

      return new Response(
        JSON.stringify({ clientSecret: paymentIntent.client_secret }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
      );

    } else {
      // --- ONE-TIME PAYMENT FLOW ---
       const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount),
        currency: 'usd',
        customer: customerId,
        metadata,
        receipt_email: email,
        automatic_payment_methods: { enabled: true },
        setup_future_usage: 'on_session',
      });
      
      return new Response(
        JSON.stringify({ clientSecret: paymentIntent.client_secret, paymentIntentId: paymentIntent.id }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
      );
    }

  } catch (error) {
    console.error('Error creating payment intent/subscription:', error);
    return new Response(
      JSON.stringify({ error: error.message || 'Failed to create payment' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    );
  }
});
