import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../shared/cors.ts';

// FIX: Declare <PERSON> global to satisfy TypeScript compiler in this environment.
declare const Deno: any;

// Helper to generate a random password
const generatePassword = (length = 16) => {
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ01223456789!@#$%^&*()_+~`|}{[]:;?><,./-=";
    let password = "";
    for (let i = 0, n = charset.length; i < n; ++i) {
        password += charset.charAt(Math.floor(Math.random() * n));
    }
    return password;
};

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabaseAdmin = createClient(
            Deno.env.get('SUPABASE_URL')!,
            Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
        );
        
        // This is a protected function, check if the caller is an admin
        const { data: { user } } = await supabaseAdmin.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
        
        // Use a role check instead of hardcoded email
        const { data: clientProfile, error: profileError } = await supabaseAdmin
            .from('clients')
            .select('role')
            .eq('user_id', user.id)
            .single();

        if (profileError || clientProfile?.role !== 'admin') {
             throw new Error("Unauthorized: Only admins can create clients.");
        }

        const { companyName, contactName, email } = await req.json();
        if (!companyName || !contactName || !email || !/\S+@\S+\.\S+/.test(email)) {
            return new Response(
                JSON.stringify({ error: "Invalid input. Company name, contact name, and a valid email are required." }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }

        // 1. Create the user in Supabase Auth
        const tempPassword = generatePassword();
        const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
            email: email,
            password: tempPassword,
            email_confirm: true, // Auto-confirm the email
        });

        if (authError) throw authError;
        const newUserId = authData.user.id;

        // 2. Create the client profile with flattened columns
        const { data: clientData, error: clientError } = await supabaseAdmin
            .from('clients')
            .insert({
                user_id: newUserId,
                email: email,
                company_name: companyName,
                primary_contact: contactName,
                account_status: 'Pending Setup',
                account_number: `ACC-${Date.now()}`,
                member_since: new Date().toISOString(),
                billing_email: email,
                billing_contact: contactName,
                // Add defaults for other non-nullable or important fields
                industry: '',
                company_size: '1-10 employees',
                website: '',
                description: '',
                title: '',
                phone: '',
                address: '',
                city: '',
                state: '',
                zip_code: '',
                country: 'United States',
                billing_phone: '',
                payment_method: 'Not on file',
                payment_method_last4: '',
                billing_address: '',
                auto_renewal: false,
                notifications_product_updates: true,
                notifications_event_updates: true,
                notifications_newsletter: true,
                notifications_account_alerts: true,
            })
            .select()
            .single();

        if (clientError) throw clientError;

        return new Response(
            JSON.stringify({ client: clientData }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
});
