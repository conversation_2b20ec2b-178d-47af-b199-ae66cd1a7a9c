import React from 'react';

interface ToggleSwitchProps {
  label: string;
  enabled: boolean;
  onChange: () => void;
  description?: string;
  className?: string;
}

export const ToggleSwitch: React.FC<ToggleSwitchProps> = ({ label, enabled, onChange, description, className }) => (
  <div className={className}>
    <div className="flex justify-between items-center">
      <span className="text-sm text-black font-medium">{label}</span>
      <button type="button" role="switch" aria-checked={enabled} onClick={onChange} className={`w-12 h-6 transition-colors relative ${enabled ? 'bg-black' : 'bg-gray-300'}`}>
        <span className="sr-only">{label}</span>
        <div className={`w-4 h-4 bg-white absolute top-1 transition-transform ${enabled ? 'translate-x-7' : 'translate-x-1'}`} />
      </button>
    </div>
    {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
  </div>
);