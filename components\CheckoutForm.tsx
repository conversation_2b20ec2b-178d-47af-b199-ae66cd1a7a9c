import React, { useState } from 'react';
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui';

interface CheckoutFormProps {
  onPaymentSuccess: (paymentIntentId: string) => void;
  onPaymentError: (errorMessage: string) => void;
  isSubmitting: boolean;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({ onPaymentSuccess, onPaymentError, isSubmitting }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not yet loaded.
      return;
    }

    setIsProcessing(true);
    setErrorMessage(null);

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Return to the current page (CartPage) after payment completion or authentication.
        return_url: `${window.location.origin}${window.location.pathname}${window.location.hash}`,
      },
      redirect: 'if_required',
    });

    if (error) {
        let message = error.message || 'An unexpected error occurred.';
        if (error.type === "card_error" || error.type === "validation_error") {
            setErrorMessage(message);
        } else {
            onPaymentError(message);
        }
    } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        onPaymentSuccess(paymentIntent.id);
    } else if (!error && !paymentIntent) {
        // This case can happen if the user is redirected for authentication.
        // The page will reload and handle the result in a different useEffect.
    } else {
        onPaymentError('Payment was not successful. Please try again.');
    }

    setIsProcessing(false);
  };

  const isButtonDisabled = !stripe || !elements || isProcessing || isSubmitting;

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      {errorMessage && <div className="text-red-600 text-sm mt-4">{errorMessage}</div>}
      <Button
        type="submit"
        disabled={isButtonDisabled}
        isLoading={isProcessing || isSubmitting}
        className="w-full mt-6"
        size="lg"
      >
        {isSubmitting ? 'Processing Order...' : 'Pay Now'}
      </Button>
    </form>
  );
};

export default CheckoutForm;