import React, { useState } from 'react';
import { SidePanel, Button } from '../../components/ui';
import type { EmailTemplate } from '../../types';

interface EmailPreviewDrawerProps {
    template: EmailTemplate | null;
    onClose: () => void;
}

// Mock data to replace variables in the templates
const mockData = {
  companyName: 'Innovate Corp',
  cartItemsList: `<li>WHISPER Media Package - $300.00</li><li>Online Demo Event - $1000.00</li>`,
  cartTotal: '1560.00', // (1300 + 20% VAT)
  setupLink: '#',
};

// Function to replace placeholder variables with mock data
const replaceVariables = (content: string) => {
    let replacedContent = content;
    for (const key in mockData) {
        const variable = `{{${key}}}`;
        replacedContent = replacedContent.replace(new RegExp(variable, 'g'), mockData[key as keyof typeof mockData]);
    }
    return replacedContent;
};

type ViewMode = 'desktop' | 'tablet' | 'mobile';

const viewConfig: Record<ViewMode, { width: string; icon: string }> = {
    desktop: { width: '100%', icon: 'fa-desktop' },
    tablet: { width: '768px', icon: 'fa-tablet-screen-button' },
    mobile: { width: '375px', icon: 'fa-mobile-screen-button' },
};

const EmailPreviewDrawer: React.FC<EmailPreviewDrawerProps> = ({ template, onClose }) => {
    const [viewMode, setViewMode] = useState<ViewMode>('desktop');
    if (!template) return null;

    const subject = replaceVariables(template.subject);
    const body = replaceVariables(template.body);

    return (
        <SidePanel
            isOpen={!!template}
            onClose={onClose}
            title={`Preview: ${template.id}`}
            size="3xl"
            footer={<Button onClick={onClose}>Close</Button>}
        >
            <div className="space-y-4 flex flex-col h-full">
                <div>
                    <h3 className="text-sm font-medium text-gray-700">Subject</h3>
                    <p className="p-3 bg-gray-50 border border-gray-200 mt-1">{subject}</p>
                </div>
                <div className="flex-grow flex flex-col">
                    <div className="flex justify-center items-center gap-1 mb-2 p-1 bg-gray-100 border border-gray-200">
                        {(Object.keys(viewConfig) as ViewMode[]).map(mode => (
                            <Button
                                key={mode}
                                variant={viewMode === mode ? 'primary' : 'secondary'}
                                size="sm"
                                onClick={() => setViewMode(mode)}
                                className="flex items-center gap-2 text-xs"
                                title={`Switch to ${mode} view`}
                            >
                                <i className={`fa-solid ${viewConfig[mode].icon}`}></i>
                                <span className="hidden sm:inline">{mode.charAt(0).toUpperCase() + mode.slice(1)}</span>
                            </Button>
                        ))}
                    </div>
                    <div className="mt-1 border border-gray-200 w-full flex-grow overflow-auto bg-gray-50 flex justify-center p-4">
                        <iframe
                            srcDoc={body}
                            title="Email Preview"
                            className="border-0 shadow-lg bg-white"
                            style={{ width: viewConfig[viewMode].width, height: '100%', transition: 'width 0.3s ease-in-out' }}
                            sandbox="allow-same-origin"
                        />
                    </div>
                </div>
            </div>
        </SidePanel>
    );
};

export default EmailPreviewDrawer;