import React, { useState, useEffect } from 'react';
import type { PaymentSettings, SmtpSettings, BillingType, BillingOption, CategoryMetadata } from '../../types';
import { useUpdatePaymentSettings, useUpdateSmtpSettings, useAdminActions } from '../../hooks';
import { Button, FormField, ToggleSwitch, SidePanel } from '../../components/ui';
import DiscountSettingsTab from './DiscountSettingsTab';
import AnnouncementSettingsTab from './AnnouncementSettingsTab';
import EmailTemplatesSettings from './EmailTemplatesSettings';

type SettingsTab = 'payment' | 'smtp' | 'billing' | 'announcement' | 'discounts' | 'email';

const sanitizeKey = (name: string): string => {
    if (!name) return '';
    const cleanedName = name.trim().replace(/[^a-zA-Z0-9\s-]/g, ''); // Allow alphanumeric, spaces, dashes
    return cleanedName.replace(/\s+/g, '-').toLowerCase();
};

const formatIsoForInput = (isoString?: string | null): string => {
    if (!isoString) return '';
    try {
      const date = new Date(isoString);
      return date.toISOString().slice(0, 16);
    } catch (e) {
      return '';
    }
};
  
const formatInputForIso = (inputString?: string): string | null => {
    if (!inputString) return null;
    return new Date(inputString).toISOString();
};


// --- Billing Type Editor Drawer ---
interface BillingTypeEditorDrawerProps {
    mode: 'create' | 'edit';
    initialData: Partial<BillingType>;
    existingTypes: BillingType[];
    onSave: (data: BillingType) => Promise<{ success: boolean, error?: string }>;
    onClose: () => void;
}
const BillingTypeEditorDrawer: React.FC<BillingTypeEditorDrawerProps> = ({ mode, initialData, existingTypes, onSave, onClose }) => {
    // Ensure options is always an array
    const sanitizedInitialData = {
        ...initialData,
        options: Array.isArray(initialData.options)
            ? initialData.options
            : (typeof initialData.options === 'string'
                ? JSON.parse(initialData.options || '[]')
                : [])
    };
    const [formData, setFormData] = useState<Partial<BillingType>>({ options: [], ...sanitizedInitialData });
    const [error, setError] = useState<string | null>(null);

    const handleOptionChange = (index: number, field: keyof BillingOption, value: any) => {
        setFormData(prev => {
            if (!prev || !prev.options) return prev;
            const newOptions = [...prev.options];
            let finalValue = value;
            if (field === 'validFrom' || field === 'validTo') {
                finalValue = value ? formatInputForIso(value) : null;
            }
            newOptions[index] = { ...newOptions[index], [field]: finalValue };
            return { ...prev, options: newOptions };
        });
    };
    
    const addOption = () => {
        setFormData(prev => ({
            ...prev,
            options: [...(prev?.options || []), { id: `new-option-${Date.now()}`, label: '', multiplier: 1, discount: 0, isPartnerTier: false, benefitText: '', isEnabled: true, validFrom: null, validTo: null }]
        }));
    };

    const removeOption = (index: number) => {
        setFormData(prev => ({
            ...prev,
            options: prev?.options?.filter((_, i) => i !== index)
        }));
    };

    const handleSave = async () => {
        setError(null);
        if (!formData.name?.trim()) {
            setError('Billing Type Name is required.');
            return;
        }
    
        // Validate options
        for (const option of formData.options || []) {
            if (!option.label.trim() || !option.id.trim()) {
                setError('All billing options must have a Label and a unique ID.');
                return;
            }
            if (isNaN(option.multiplier) || option.multiplier < 1) {
                setError(`Multiplier for "${option.label}" must be a number greater than or equal to 1.`);
                return;
            }
            if (isNaN(option.discount) || option.discount < 0 || option.discount > 100) {
                setError(`Discount for "${option.label}" must be a number between 0 and 100.`);
                return;
            }
        }
    
        let finalId = formData.id;
        if (mode === 'create') {
            finalId = sanitizeKey(formData.name);
            if (!finalId) {
                setError('Could not generate a valid ID. Please provide a name.');
                return;
            }
            if (existingTypes.some(bt => bt.id === finalId)) {
                setError('A Billing Type with this ID already exists.');
                return;
            }
        }
    
        const finalData: BillingType = {
            id: finalId!,
            name: formData.name!,
            publicTitle: formData.publicTitle,
            description: formData.description,
            options: formData.options || [],
            isSystemType: formData.isSystemType || false
        };
    
        const result = await onSave(finalData);
        if (!result.success) {
            setError(result.error || 'An unknown error occurred.');
        }
    };
    
    const renderFooter = () => (
        <div className="flex gap-3">
            <Button onClick={handleSave} className="flex items-center gap-2">
                <i className="fa-solid fa-save"></i> Save
            </Button>
            <Button onClick={onClose} variant="secondary">Cancel</Button>
        </div>
    );

    return (
        <SidePanel
            isOpen
            onClose={onClose}
            title={mode === 'create' ? 'Create Billing Type' : 'Edit Billing Type'}
            size="3xl"
            footer={renderFooter()}
        >
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField label="Internal Name" name="name" value={formData.name || ''} onChange={(e) => setFormData(p => ({...p, name: e.target.value}))} required />
                    {mode === 'edit' ? (
                        <FormField label="Unique ID" name="id" value={formData.id || ''} onChange={() => {}} disabled />
                    ) : (
                        <div className="pt-5">
                            <label className="block text-sm font-medium text-gray-700 mb-1">Unique ID</label>
                            <p className="text-sm text-gray-500 bg-gray-100 p-2 min-h-[38px]">Auto-generated from name</p>
                        </div>
                    )}
                    <FormField className="md:col-span-2" label="Public Display Title" name="publicTitle" value={formData.publicTitle || ''} onChange={(e) => setFormData(p => ({...p, publicTitle: e.target.value}))} placeholder="e.g., Choose Your Plan" />
                    <FormField className="md:col-span-2" as="textarea" label="Description" name="description" value={formData.description || ''} onChange={(e) => setFormData(p => ({...p, description: e.target.value}))} placeholder="e.g., Save more with longer commitments." />
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                    <h3 className="text-md font-semibold text-black mb-4">Billing Options</h3>
                    <div className="space-y-4">
                        {(Array.isArray(formData.options) ? formData.options : []).map((opt, index) => (
                            <div key={index} className="p-3 border border-gray-200 bg-gray-50 space-y-3">
                                <div className="grid grid-cols-12 gap-3 items-start">
                                    <FormField className="col-span-12 sm:col-span-6 md:col-span-2" label="ID" name="id" value={opt.id} onChange={e => handleOptionChange(index, 'id', e.target.value)} disabled={formData.isSystemType} />
                                    <FormField className="col-span-12 sm:col-span-6 md:col-span-3" label="Label" name="label" value={opt.label} onChange={e => handleOptionChange(index, 'label', e.target.value)} />
                                    <FormField className="col-span-6 md:col-span-2" label="Multiplier" name="multiplier" type="number" value={opt.multiplier} onChange={e => handleOptionChange(index, 'multiplier', Number(e.target.value))} />
                                    <FormField className="col-span-6 md:col-span-2" label="Discount %" name="discount" type="number" value={opt.discount} onChange={e => handleOptionChange(index, 'discount', Number(e.target.value))} />
                                    <div className="col-span-12 sm:col-span-6 md:col-span-2 flex items-center pt-5">
                                        <input 
                                            type="checkbox" 
                                            id={`partner-tier-${index}`} 
                                            checked={!!opt.isPartnerTier} 
                                            onChange={e => handleOptionChange(index, 'isPartnerTier', e.target.checked)}
                                            className="h-4 w-4 text-black border-gray-300 focus:ring-black"
                                        />
                                        <label htmlFor={`partner-tier-${index}`} className="ml-2 text-sm text-gray-700">Partner</label>
                                    </div>
                                    <div className="col-span-12 sm:col-span-6 md:col-span-1 flex justify-end items-start pt-5">
                                        <Button variant="danger-outline" size="sm" onClick={() => removeOption(index)} disabled={formData.isSystemType} className="px-3 py-2">
                                            <i className="fa-solid fa-trash-can"></i>
                                        </Button>
                                    </div>
                                </div>
                                <FormField className="w-full" label="Selection Benefit Text" name="benefitText" value={opt.benefitText || ''} onChange={e => handleOptionChange(index, 'benefitText', e.target.value)} placeholder="e.g., 60-day money-back guarantee" />
                                <div className="pt-3 border-t border-gray-200 grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                                    <ToggleSwitch label="Enable Option" enabled={opt.isEnabled !== false} onChange={() => handleOptionChange(index, 'isEnabled', !(opt.isEnabled !== false))} />
                                    <FormField label="Valid From" type="datetime-local" name="validFrom" value={formatIsoForInput(opt.validFrom)} onChange={e => handleOptionChange(index, 'validFrom', e.target.value)} />
                                    <FormField label="Valid To" type="datetime-local" name="validTo" value={formatIsoForInput(opt.validTo)} onChange={e => handleOptionChange(index, 'validTo', e.target.value)} />
                                </div>
                            </div>
                        ))}
                    </div>
                    <Button onClick={addOption} disabled={formData.isSystemType} variant="secondary" size="sm" className="mt-4 flex items-center gap-2">
                       <i className="fa-solid fa-plus"></i> Add Option
                    </Button>
                </div>

                 {error && <p className="text-red-600 text-sm mt-4">{error}</p>}
            </div>
        </SidePanel>
    );
};


interface SettingsTabProps {
    paymentSettings: PaymentSettings | null;
    smtpSettings: SmtpSettings | null;
    billingTypes: BillingType[];
    categoriesMetadata: { [key: string]: CategoryMetadata };
}

const SettingsTab: React.FC<SettingsTabProps> = ({ paymentSettings, smtpSettings, billingTypes, categoriesMetadata }) => {
    const [activeSettingsTab, setActiveSettingsTab] = useState<SettingsTab>('billing');
    
    const adminActions = useAdminActions();
    const updatePaymentSettingsMutation = useUpdatePaymentSettings();
    const updateSmtpSettingsMutation = useUpdateSmtpSettings();

    const [localPaymentSettings, setLocalPaymentSettings] = useState<PaymentSettings>({
        stripe: { enabled: false, publicKey: '', secretKey: '', methods: { creditCard: true, applePay: false, googlePay: false } }
    });
    const [localSmtpSettings, setLocalSmtpSettings] = useState<SmtpSettings>({
        testMode: true, server: '', port: 587, username: '', password: '', encryption: 'none', fromAddress: '', fromName: '', testEmailRecipient: ''
    });

    const [billingTypeEditorState, setBillingTypeEditorState] = useState<{ isOpen: boolean; mode: 'create' | 'edit'; data: Partial<BillingType> | null }>({ isOpen: false, mode: 'create', data: null });

    useEffect(() => {
        if (paymentSettings) setLocalPaymentSettings(paymentSettings);
    }, [paymentSettings]);
    useEffect(() => {
        if (smtpSettings) setLocalSmtpSettings(smtpSettings);
    }, [smtpSettings]);

    const handleSendTestEmail = () => {
        if (!localSmtpSettings.server || !localSmtpSettings.port) {
            alert('Failed to send test email. Please configure and save your SMTP server and port settings first.'); return;
        }
        alert(`Simulating sending a test email to ${localSmtpSettings.testEmailRecipient}...`);
        setTimeout(() => {
            alert(`Test email successfully sent to ${localSmtpSettings.testEmailRecipient} using the specified SMTP server configuration!`);
        }, 1500);
    };

    const handleOpenBillingTypeEditor = (mode: 'create' | 'edit', data: Partial<BillingType> | null) => {
        setBillingTypeEditorState({ isOpen: true, mode, data });
    };
    const handleCloseBillingTypeEditor = () => {
        setBillingTypeEditorState({ isOpen: false, mode: 'create', data: null });
    };
    const handleSaveBillingType = async (data: BillingType) => {
        if (billingTypeEditorState.mode === 'create') {
            await adminActions.createRecord('billing_types', data);
        } else {
            await adminActions.updateRecord('billing_types', data, data.id);
        }
        handleCloseBillingTypeEditor();
        return { success: true };
    };
    const handleDeleteBillingType = async (idToDelete: string) => {
        // FIX: Explicitly type 'meta' to avoid accessing property on 'unknown'.
        const isUsed = Object.values(categoriesMetadata).some((meta: CategoryMetadata) => meta.billingType === idToDelete);
        if (isUsed) {
            alert('This billing type is currently in use by one or more categories. Please reassign them before deleting.');
            return;
        }
        if (window.confirm('Are you sure you want to delete this billing type?')) {
            await adminActions.deleteRecord('billing_types', idToDelete);
        }
    };
    
    const settingsNavItems: { id: SettingsTab; label: string; icon: string }[] = [
        { id: 'billing', label: 'Billing Types', icon: 'fa-file-invoice-dollar' },
        { id: 'discounts', label: 'Discounts', icon: 'fa-tags' },
        { id: 'email', label: 'Email Templates', icon: 'fa-envelope-open-text' },
        { id: 'payment', label: 'Payment Gateway', icon: 'fa-credit-card' },
        { id: 'smtp', label: 'Email (SMTP)', icon: 'fa-paper-plane' },
        { id: 'announcement', label: 'Announcement', icon: 'fa-bullhorn' },
    ];

    return (
        <div className="space-y-6">
          <div className="bg-white border border-gray-300 p-2">
              <div className="flex justify-center overflow-x-auto no-scrollbar">
                  {settingsNavItems.map(item => (
                      <button key={item.id} onClick={() => setActiveSettingsTab(item.id)} className={`flex items-center gap-2 px-4 py-2 text-sm font-medium ${activeSettingsTab === item.id ? 'bg-gray-100 text-black' : 'text-gray-600 hover:bg-gray-50'}`}>
                          <i className={`fa-solid ${item.icon}`}></i> {item.label}
                      </button>
                  ))}
              </div>
          </div>
          
          <div className="mt-6">
              {activeSettingsTab === 'payment' && (
                  <div className="bg-white border border-gray-300 p-6 space-y-6">
                        <h3 className="text-xl font-semibold text-black">Payment Gateway (Stripe)</h3>
                        <ToggleSwitch label="Enable Stripe" enabled={localPaymentSettings.stripe.enabled} onChange={() => setLocalPaymentSettings(s => ({...s, stripe: {...s.stripe, enabled: !s.stripe.enabled}}))} />
                        <FormField label="Public Key" name="publicKey" value={localPaymentSettings.stripe.publicKey} onChange={(e) => setLocalPaymentSettings(s => ({...s, stripe: {...s.stripe, publicKey: e.target.value}}))} />
                        <FormField label="Secret Key" name="secretKey" type="password" value={localPaymentSettings.stripe.secretKey} onChange={(e) => setLocalPaymentSettings(s => ({...s, stripe: {...s.stripe, secretKey: e.target.value}}))} />
                        <div className="space-y-4 pt-4 border-t border-gray-200">
                            <label className="text-sm font-medium text-gray-700">Enabled Methods</label>
                            <ToggleSwitch
                                label="Credit Card"
                                enabled={localPaymentSettings.stripe.methods.creditCard}
                                onChange={() => setLocalPaymentSettings(s => ({...s, stripe: {...s.stripe, methods: {...s.stripe.methods, creditCard: !s.stripe.methods.creditCard }}}))}
                            />
                            <ToggleSwitch
                                label="Apple Pay"
                                enabled={localPaymentSettings.stripe.methods.applePay}
                                onChange={() => setLocalPaymentSettings(s => ({...s, stripe: {...s.stripe, methods: {...s.stripe.methods, applePay: !s.stripe.methods.applePay }}}))}
                            />
                            <ToggleSwitch
                                label="Google Pay"
                                enabled={localPaymentSettings.stripe.methods.googlePay}
                                onChange={() => setLocalPaymentSettings(s => ({...s, stripe: {...s.stripe, methods: {...s.stripe.methods, googlePay: !s.stripe.methods.googlePay }}}))}
                            />
                        </div>
                        <Button 
                            onClick={() => updatePaymentSettingsMutation.mutate(localPaymentSettings)} 
                            isLoading={updatePaymentSettingsMutation.isPending}
                            disabled={updatePaymentSettingsMutation.isPending}
                        >
                            {updatePaymentSettingsMutation.isPending ? 'Saving...' : 'Save Payment Settings'}
                        </Button>
                  </div>
              )}
              {activeSettingsTab === 'smtp' && (
                   <div className="bg-white border border-gray-300 p-6 space-y-6">
                        <h3 className="text-xl font-semibold text-black">Email (SMTP) Settings</h3>
                        <ToggleSwitch label="Test Mode" enabled={localSmtpSettings.testMode} onChange={() => setLocalSmtpSettings(s => ({...s, testMode: !s.testMode}))} description="In test mode, emails are logged to the console instead of being sent." />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField label="Server" name="server" value={localSmtpSettings.server} onChange={e => setLocalSmtpSettings(s => ({...s, server: e.target.value}))} />
                            <FormField label="Port" name="port" type="number" value={localSmtpSettings.port} onChange={e => setLocalSmtpSettings(s => ({...s, port: Number(e.target.value)}))} />
                            <FormField label="Username" name="username" value={localSmtpSettings.username} onChange={e => setLocalSmtpSettings(s => ({...s, username: e.target.value}))} />
                            <FormField label="Password" name="password" type="password" value={localSmtpSettings.password} onChange={e => setLocalSmtpSettings(s => ({...s, password: e.target.value}))} />
                            <FormField as="select" label="Encryption" name="encryption" value={localSmtpSettings.encryption} onChange={e => setLocalSmtpSettings(s => ({...s, encryption: e.target.value as any}))} options={['none', 'ssl', 'starttls']} />
                            <FormField label="From Name" name="fromName" value={localSmtpSettings.fromName} onChange={e => setLocalSmtpSettings(s => ({...s, fromName: e.target.value}))} />
                            <FormField className="md:col-span-2" label="From Address" name="fromAddress" value={localSmtpSettings.fromAddress} onChange={e => setLocalSmtpSettings(s => ({...s, fromAddress: e.target.value}))} />
                        </div>
                        <div className="pt-4 border-t border-gray-200 space-y-4">
                            <FormField label="Test Email Recipient" name="testEmailRecipient" value={localSmtpSettings.testEmailRecipient} onChange={e => setLocalSmtpSettings(s => ({...s, testEmailRecipient: e.target.value}))} />
                            <div className="flex gap-3">
                                <Button 
                                    onClick={() => updateSmtpSettingsMutation.mutate(localSmtpSettings)} 
                                    isLoading={updateSmtpSettingsMutation.isPending} 
                                    disabled={updateSmtpSettingsMutation.isPending}
                                >
                                    Save SMTP Settings
                                </Button>
                                <Button onClick={handleSendTestEmail} variant="secondary">Send Test Email</Button>
                            </div>
                        </div>
                   </div>
              )}
              {activeSettingsTab === 'billing' && (
                   <div className="space-y-6">
                      <div className="p-4 border border-gray-300 bg-white">
                          <Button onClick={() => handleOpenBillingTypeEditor('create', {})} className="w-full flex items-center justify-center gap-2">
                              <i className="fa-solid fa-plus"></i> Add New Billing Type
                          </Button>
                      </div>
                      <div className="bg-white border border-gray-300">
                          <div className="overflow-x-auto">
                               <table className="w-full text-sm">
                                  <thead className="bg-gray-50 border-b border-gray-200">
                                      <tr>
                                          <th className="px-4 py-3 font-medium text-black text-left">Name</th>
                                          <th className="px-4 py-3 font-medium text-black text-left">ID</th>
                                          <th className="px-4 py-3 font-medium text-black text-center">Options</th>
                                          <th className="px-4 py-3 font-medium text-black text-right">Actions</th>
                                      </tr>
                                  </thead>
                                  <tbody>
                                      {billingTypes.map(bt => (
                                          <tr key={bt.id} className="border-b border-gray-200 hover:bg-gray-50 last:border-b-0">
                                              <td className="px-4 py-3 font-semibold text-black">{bt.name}</td>
                                              <td className="px-4 py-3 text-gray-600 font-mono text-xs">{bt.id}</td>
                                              <td className="px-4 py-3 text-center text-gray-600">{bt.options.length}</td>
                                              <td className="px-4 py-3 text-right">
                                                  <Button onClick={() => handleOpenBillingTypeEditor('edit', bt)} variant="secondary" size="sm">Edit</Button>
                                                  {!bt.isSystemType && <Button onClick={() => handleDeleteBillingType(bt.id)} variant="danger-outline" size="sm" className="ml-2">Delete</Button>}
                                              </td>
                                          </tr>
                                      ))}
                                  </tbody>
                              </table>
                          </div>
                      </div>
                   </div>
              )}
              {activeSettingsTab === 'announcement' && <AnnouncementSettingsTab />}
              {activeSettingsTab === 'discounts' && <DiscountSettingsTab />}
              {activeSettingsTab === 'email' && <EmailTemplatesSettings />}
          </div>
  
          {billingTypeEditorState.isOpen && (
              <BillingTypeEditorDrawer 
                  mode={billingTypeEditorState.mode}
                  initialData={billingTypeEditorState.data || {}}
                  existingTypes={billingTypes}
                  onSave={handleSaveBillingType}
                  onClose={handleCloseBillingTypeEditor}
              />
          )}
        </div>
      );
};

export default SettingsTab;