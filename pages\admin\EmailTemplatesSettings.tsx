import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useEmailTemplates, useAdminActions } from '../../hooks';
import type { EmailTemplate } from '../../types';
import { Button, FormField, SidePanel } from '../../components/ui';
import { useToastStore } from '../../stores/useToastStore';
import EmailPreviewDrawer from './EmailPreviewDrawer';

// --- START: Email HTML Validator ---
interface ValidationIssue {
    type: 'error' | 'warning';
    message: string;
}

const validationRules: {
    type: 'error' | 'warning';
    regex: RegExp;
    message: string;
}[] = [
    // Errors: Deprecated or dangerous tags that are almost universally stripped.
    {
        type: 'error',
        regex: /<\s*(script|iframe|video|audio|applet|bgsound|blink|frame|frameset|ilayer|layer|marquee|meta[^>]+refresh)\b/gi,
        message: 'Erreur : La balise <TAG> n\'est pas supportée dans les clients de messagerie et sera probablement supprimée ou provoquera des avertissements de sécurité.'
    },
    // Warnings: Tags with inconsistent or poor support.
    {
        type: 'warning',
        regex: /<\s*style\b/gi,
        message: 'Avertissement : La balise <style> est mal supportée dans certains clients de messagerie majeurs (par ex., l\'application Gmail). Préférez l\'utilisation de styles en ligne sur les éléments.'
    },
    {
        type: 'warning',
        regex: /<\s*link[^>]+rel=["']stylesheet["']/gi,
        message: 'Avertissement : Les feuilles de style externes (<link rel="stylesheet">) ne sont pas supportées. Tout le CSS doit être intégré en ligne.'
    },
    // Warnings: Problematic CSS properties.
    {
        type: 'warning',
        regex: /position\s*:\s*(absolute|fixed)/gi,
        message: 'Avertissement : Les propriétés CSS `position: absolute` et `position: fixed` sont mal supportées. Utilisez des tableaux pour la mise en page.'
    },
    {
        type: 'warning',
        regex: /background-image\s*:/gi,
        message: 'Avertissement : La propriété CSS `background-image` a un support incohérent. Pour une compatibilité totale, utilisez une couleur unie `background-color` avec l\'attribut `background` sur un `<td>`.'
    },
    {
        type: 'warning',
        regex: /\sbackground=["']/gi,
        message: 'Avertissement : L\'attribut HTML `background` a un support limité. Utilisez un style en ligne `background-color` sur une cellule de tableau (`<td>`) pour une meilleure compatibilité.'
    }
];

const validateEmailHtml = (html: string): ValidationIssue[] => {
    const issues: ValidationIssue[] = [];
    validationRules.forEach(rule => {
        const matches = [...html.matchAll(rule.regex)];
        matches.forEach(match => {
            const tagName = match[1] ? match[1].toLowerCase() : 'element';
            issues.push({
                type: rule.type,
                message: rule.message.replace(/<TAG>/g, tagName)
            });
        });
    });
    // Deduplicate messages to avoid spamming the user with the same warning
    const uniqueMessages = new Set(issues.map(issue => issue.message));
    return Array.from(uniqueMessages).map(message => issues.find(issue => issue.message === message)!);
};
// --- END: Email HTML Validator ---


const EmailTemplateEditor: React.FC<{
    template: EmailTemplate;
    onSave: (template: EmailTemplate) => void;
    onClose: () => void;
}> = ({ template, onSave, onClose }) => {
    const [formData, setFormData] = useState<EmailTemplate>(template);
    const [validationIssues, setValidationIssues] = useState<ValidationIssue[]>([]);
    
    useEffect(() => {
        if (formData.body) {
            const issues = validateEmailHtml(formData.body);
            setValidationIssues(issues);
        } else {
            setValidationIssues([]);
        }
    }, [formData.body]);

    const handleSave = () => {
        onSave(formData);
    };

    const iconMap = {
        error: 'fa-circle-xmark',
        warning: 'fa-triangle-exclamation'
    };

    const colorMap = {
        error: 'text-red-700',
        warning: 'text-yellow-800'
    };

    return (
        <SidePanel isOpen={true} onClose={onClose} title={`Edit: ${template.id}`} size="3xl" footer={
            <div className="flex gap-2">
                <Button onClick={handleSave}>Save Template</Button>
                <Button variant="secondary" onClick={onClose}>Cancel</Button>
            </div>
        }>
            <div className="space-y-4">
                <p className="text-sm text-gray-600 bg-gray-50 p-3 border border-gray-200">{template.description}</p>
                <FormField label="Subject" name="subject" value={formData.subject} onChange={(e) => setFormData(p => ({...p, subject: e.target.value}))} />
                <FormField 
                    label="Body (HTML)" 
                    name="body" 
                    as="textarea" 
                    value={formData.body} 
                    onChange={(e) => setFormData(p => ({...p, body: e.target.value}))} 
                    rows={20} 
                />
                {validationIssues.length > 0 && (
                    <div className="mt-2 p-3 bg-gray-50 border border-gray-200">
                        <h4 className="text-sm font-semibold text-gray-800 mb-2 flex items-center gap-2">
                           <i className="fa-solid fa-shield-halved"></i> Rapport de Compatibilité E-mail
                        </h4>
                        <ul className="space-y-2">
                            {validationIssues.map((issue, index) => (
                                <li key={index} className={`flex items-start text-xs ${colorMap[issue.type]}`}>
                                    <i className={`fa-solid ${iconMap[issue.type]} mr-2 mt-0.5 w-4 text-center`}></i>
                                    <span>{issue.message}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
                <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Available Variables</h4>
                    <div className="flex flex-wrap gap-2">
                        {template.variables.map(variable => (
                            <code key={variable} className="text-xs bg-gray-200 text-gray-800 px-2 py-1">{variable}</code>
                        ))}
                    </div>
                </div>
            </div>
        </SidePanel>
    );
};


const EmailTemplatesSettings: React.FC = () => {
    const { data: templates = [], isLoading } = useEmailTemplates();
    const adminActions = useAdminActions();
    const queryClient = useQueryClient();
    const { showToast } = useToastStore();
    const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
    const [previewTemplate, setPreviewTemplate] = useState<EmailTemplate | null>(null);

    const handleSaveTemplate = async (template: EmailTemplate) => {
        try {
            await adminActions.updateRecord('email_templates', template, template.id);
            showToast('Template saved!', 'success');
            setSelectedTemplate(null);
            queryClient.invalidateQueries({ queryKey: ['email-templates'] });
        } catch (error) {
            showToast(`Failed to save template: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
        }
    };
    
    if (isLoading) {
        return <div className="text-center p-10"><i className="fa-solid fa-spinner fa-spin text-2xl text-gray-400"></i></div>;
    }

    return (
        <div className="bg-white border border-gray-300">
            <div className="overflow-x-auto">
                <table className="w-full text-sm">
                    <thead className="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th className="px-4 py-3 font-medium text-black text-left">Template ID</th>
                            <th className="px-4 py-3 font-medium text-black text-left">Description</th>
                            <th className="px-4 py-3 font-medium text-black text-right">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {templates.map(template => (
                            <tr key={template.id} className="border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
                                <td className="px-4 py-3 font-mono text-xs text-black">{template.id}</td>
                                <td className="px-4 py-3 text-gray-600">{template.description}</td>
                                <td className="px-4 py-3 text-right">
                                    <Button variant="secondary" size="sm" onClick={() => setPreviewTemplate(template)}>Preview</Button>
                                    <Button variant="secondary" size="sm" onClick={() => setSelectedTemplate(template)} className="ml-2">Edit</Button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            {selectedTemplate && (
                <EmailTemplateEditor 
                    template={selectedTemplate}
                    onSave={handleSaveTemplate}
                    onClose={() => setSelectedTemplate(null)}
                />
            )}
            {previewTemplate && (
                <EmailPreviewDrawer 
                    template={previewTemplate}
                    onClose={() => setPreviewTemplate(null)}
                />
            )}
        </div>
    );
};

export default EmailTemplatesSettings;