import React, { useState, useMemo } from 'react';
import type { Subscription } from '../../types';
import { SidePanel, Button } from '../../components/ui';

type SubscriptionStatusFilter = 'All' | 'active' | 'trialing' | 'past_due' | 'unpaid' | 'canceled';
type SubscriptionSortKey = 'clientName' | 'status' | 'currentPeriodEnd';

interface SubscriptionsTabProps {
    subscriptions: Subscription[];
}

const SubscriptionsTab: React.FC<SubscriptionsTabProps> = ({ subscriptions }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState<SubscriptionStatusFilter>('All');
    const [sort, setSort] = useState<{ key: SubscriptionSortKey; direction: 'asc' | 'desc' }>({ key: 'currentPeriodEnd', direction: 'asc' });
    const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);

    const statusColor: { [key: string]: string } = {
        active: 'bg-green-100 text-green-800',
        trialing: 'bg-blue-100 text-blue-800',
        past_due: 'bg-yellow-100 text-yellow-800',
        unpaid: 'bg-red-100 text-red-800',
        canceled: 'bg-gray-100 text-gray-800',
    };

    const filteredAndSortedSubscriptions = useMemo(() => {
        return subscriptions
          .filter(sub => {
            if (statusFilter !== 'All' && sub.status !== statusFilter) {
              return false;
            }
            const lowercasedFilter = searchTerm.toLowerCase();
            if (lowercasedFilter && 
                !sub.clientName?.toLowerCase().includes(lowercasedFilter) && 
                !sub.packageName?.toLowerCase().includes(lowercasedFilter)) {
              return false;
            }
            return true;
          })
          .sort((a, b) => {
            const key = sort.key;
            const direction = sort.direction === 'asc' ? 1 : -1;
            
            let valA: any, valB: any;
            if (key === 'currentPeriodEnd') {
                valA = a.status === 'canceled' ? Infinity : new Date(a.currentPeriodEnd).getTime();
                valB = b.status === 'canceled' ? Infinity : new Date(b.currentPeriodEnd).getTime();
            } else {
                valA = a[key] || '';
                valB = b[key] || '';
            }

            if (valA < valB) return -1 * direction;
            if (valA > valB) return 1 * direction;
            return 0;
          });
    }, [subscriptions, searchTerm, statusFilter, sort]);

    const DetailItem: React.FC<{ label: string, value: React.ReactNode }> = ({ label, value }) => (
        <div>
            <p className="text-xs text-gray-500 font-medium uppercase tracking-wider">{label}</p>
            <div className="text-sm text-black mt-1">{value || 'N/A'}</div>
        </div>
    );
    
    return (
        <>
            <div className="bg-white border border-gray-300">
                <div className="p-4 border-b border-gray-200 flex items-center justify-between gap-4 flex-wrap">
                    <h3 className="text-lg font-semibold text-black">Subscription Management</h3>
                    <div className="flex items-center gap-4 flex-wrap">
                        <input type="text" placeholder="Search by Company or Package..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="w-64 px-2 py-1 border border-gray-300 text-sm bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"/>
                        <select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value as SubscriptionStatusFilter)} className="px-2 py-1 border border-gray-300 text-sm bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent">
                            <option value="All">All Statuses</option>
                            <option value="active">Active</option>
                            <option value="trialing">Trialing</option>
                            <option value="past_due">Past Due</option>
                            <option value="unpaid">Unpaid</option>
                            <option value="canceled">Canceled</option>
                        </select>
                    </div>
                </div>
                <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                         <thead className="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th className="px-4 py-3 font-medium text-black text-left">Client</th>
                                <th className="px-4 py-3 font-medium text-black text-left">Package</th>
                                <th className="px-4 py-3 font-medium text-black text-left">Period End</th>
                                <th className="px-4 py-3 font-medium text-black text-center">Status</th>
                                <th className="px-4 py-3 font-medium text-black text-left">Last Payment Error</th>
                                <th className="px-4 py-3 font-medium text-black text-right">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredAndSortedSubscriptions.map(sub => (
                                <tr key={sub.id} className="border-b border-gray-200 hover:bg-gray-50 last:border-b-0 cursor-pointer" onClick={() => setSelectedSubscription(sub)}>
                                    <td className="px-4 py-3 font-semibold text-black">{sub.clientName}</td>
                                    <td className="px-4 py-3 text-black">{sub.packageName}</td>
                                    <td className="px-4 py-3 text-gray-600">{sub.status === 'canceled' ? 'N/A' : new Date(sub.currentPeriodEnd).toLocaleDateString()}</td>
                                    <td className="px-4 py-3 text-center">
                                        <span className={`px-2 py-1 text-xs font-medium capitalize ${statusColor[sub.status] || 'bg-gray-100 text-gray-800'}`}>{sub.status.replace('_', ' ')}</span>
                                    </td>
                                    <td className="px-4 py-3 text-xs text-red-700">{sub.lastPaymentError?.message || '—'}</td>
                                    <td className="px-4 py-3 text-right">
                                        <Button variant="secondary" size="sm" onClick={(e) => { e.stopPropagation(); setSelectedSubscription(sub); }}>
                                            View Details
                                        </Button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            <SidePanel
                isOpen={!!selectedSubscription}
                onClose={() => setSelectedSubscription(null)}
                title={`Subscription Details`}
                size="lg"
            >
                {selectedSubscription && (
                    <div className="space-y-6">
                        <div>
                           <p className="text-2xl font-bold text-black">{selectedSubscription.clientName}</p>
                           <p className="text-sm text-gray-500 break-all">ID: {selectedSubscription.stripeSubscriptionId}</p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-y-4 gap-x-6 p-4 bg-gray-50 border border-gray-200">
                             <DetailItem label="Status" value={
                                 <span className={`px-2 py-1 text-xs font-medium capitalize ${statusColor[selectedSubscription.status] || 'bg-gray-100 text-gray-800'}`}>
                                     {selectedSubscription.status.replace('_', ' ')}
                                 </span>
                             } />
                             <DetailItem label="Package" value={selectedSubscription.packageName} />
                             <DetailItem label="Price" value={`$${selectedSubscription.price?.toFixed(2)} / ${selectedSubscription.billingCycle}`} />
                             <DetailItem label="Current Period End" value={new Date(selectedSubscription.currentPeriodEnd).toLocaleDateString()} />
                        </div>

                        <div className="space-y-4">
                            <h4 className="font-semibold text-black">Timeline</h4>
                            <DetailItem label="Created" value={new Date(selectedSubscription.currentPeriodStart).toLocaleString()} />
                            {selectedSubscription.trialEnd && <DetailItem label="Trial Ends" value={new Date(selectedSubscription.trialEnd).toLocaleString()} />}
                            {selectedSubscription.canceledAt && <DetailItem label="Canceled On" value={new Date(selectedSubscription.canceledAt).toLocaleString()} />}
                        </div>

                        {selectedSubscription.lastPaymentError && (
                            <div className="p-4 bg-red-50 border border-red-200">
                                <h4 className="font-semibold text-red-800">Last Payment Error</h4>
                                <p className="text-sm text-red-700 mt-1">{selectedSubscription.lastPaymentError.message}</p>
                            </div>
                        )}

                        <div className="space-y-4">
                            <h4 className="font-semibold text-black">Payment History</h4>
                            {selectedSubscription.invoices && selectedSubscription.invoices.length > 0 ? (
                                <div className="border border-gray-200">
                                    <table className="w-full text-xs">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="p-2 text-left font-medium">Date</th>
                                                <th className="p-2 text-left font-medium">Amount</th>
                                                <th className="p-2 text-center font-medium">Status</th>
                                                <th className="p-2 text-right font-medium"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {selectedSubscription.invoices.sort((a, b) => new Date(b.paidAt || 0).getTime() - new Date(a.paidAt || 0).getTime()).map(invoice => (
                                                <tr key={invoice.id} className="border-t border-gray-200">
                                                    <td className="p-2 text-gray-800">{invoice.paidAt ? new Date(invoice.paidAt).toLocaleDateString() : 'N/A'}</td>
                                                    <td className="p-2 text-black">${(invoice.amountTotal / 100).toFixed(2)}</td>
                                                    <td className="p-2 text-center capitalize text-black">{invoice.status}</td>
                                                    <td className="p-2 text-right">
                                                        <a href={invoice.hostedInvoiceUrl} target="_blank" rel="noopener noreferrer" className="text-black hover:underline font-medium">
                                                            View Invoice
                                                        </a>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            ) : (
                                <p className="text-sm text-gray-500">No payment history available for this subscription.</p>
                            )}
                        </div>
                        
                        <div className="pt-4 border-t border-gray-200">
                             <a 
                                href={`https://dashboard.stripe.com/subscriptions/${selectedSubscription.stripeSubscriptionId}`} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="inline-flex items-center justify-center whitespace-nowrap transition-colors focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 bg-black text-white hover:bg-gray-800 px-4 py-2 text-sm font-semibold w-full"
                            >
                                <i className="fa-brands fa-stripe mr-2"></i> View in Stripe
                            </a>
                        </div>
                    </div>
                )}
            </SidePanel>
        </>
    );
};

export default SubscriptionsTab;