import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCartStore } from '@/stores/useCartStore';
import { useAuthStore } from '@/stores/useAuthStore';
import { Button, SidePanel, ConfirmationModal } from '@/components/ui';
// FIX: Import CartItem type.
import { CartItem } from '@/types';

const CartDrawer: React.FC = () => {
  const navigate = useNavigate();
  const { cart, removeFromCart, getTotals, isCartOpen, setIsCartOpen } = useCartStore();
  const { user, isImpersonating, impersonatedClient } = useAuthStore();
  const client = isImpersonating ? impersonatedClient : user.data;
  const [itemToRemove, setItemToRemove] = useState<CartItem | null>(null);
  
  const onClose = () => setIsCartOpen(false);

  const handleCheckout = () => {
    onClose();
    navigate('/cart');
  };

  const handleRemoveFromCart = (item: CartItem) => {
    setItemToRemove(item);
  };

  const confirmRemoveFromCart = () => {
    if (itemToRemove) {
      removeFromCart(itemToRemove.cartId, client?.id);
      setItemToRemove(null);
    }
  };
  
  const { total } = getTotals();

  const renderFooter = () => (
    <>
      <div className="flex justify-between items-center mb-4">
        <span className="text-lg font-semibold text-black">Total</span>
        <span className="text-2xl font-bold text-black">${total.toFixed(2)}</span>
      </div>
      <Button
        onClick={handleCheckout}
        className="w-full"
        size="lg"
      >
        Proceed to Checkout
      </Button>
    </>
  );

  return (
    <>
      <SidePanel
        isOpen={isCartOpen}
        onClose={onClose}
        title="Shopping Cart"
        size="md"
        footer={cart.length > 0 ? renderFooter() : undefined}
      >
        {cart.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <p className="text-gray-600">Your cart is empty</p>
            <Button onClick={onClose} className="mt-4">Continue Shopping</Button>
          </div>
        ) : (
          <div className="space-y-4">
            {cart.map((item) => (
              <div key={item.cartId} className="bg-gray-50 border border-gray-200 p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-semibold text-black">{item.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      ${item.finalPrice || item.price} {item.finalBilling || item.billing}
                    </p>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => handleRemoveFromCart(item)} className="text-red-600 hover:text-red-800 p-1">
                    <i className="fa-solid fa-xmark"></i>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </SidePanel>
      <ConfirmationModal
        isOpen={!!itemToRemove}
        onClose={() => setItemToRemove(null)}
        onConfirm={confirmRemoveFromCart}
        title="Remove Item from Cart"
        message={<p>Are you sure you want to remove <strong>{itemToRemove?.name}</strong> from your cart?</p>}
        confirmText="Remove Item"
      />
    </>
  );
};

export default CartDrawer;