import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ContactSubmissionService } from '@/lib/supabaseService';
import type { ContactSubmission } from '@/types';

export const useContactSubmissions = () => {
  return useQuery({
    queryKey: ['contact-submissions'],
    queryFn: ContactSubmissionService.getAll,
    staleTime: 1 * 60 * 1000, // Contact submissions change frequently
  });
};

export const useContactSubmission = (id: string) => {
  return useQuery({
    queryKey: ['contact-submissions', id],
    queryFn: () => ContactSubmissionService.getById(id),
    enabled: !!id,
  });
};

export const useCreateContactSubmission = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ContactSubmissionService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contact-submissions'] });
    },
  });
};

export const useUpdateContactSubmission = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ContactSubmission> }) =>
      ContactSubmissionService.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['contact-submissions'] });
      queryClient.invalidateQueries({ queryKey: ['contact-submissions', id] });
    },
  });
};