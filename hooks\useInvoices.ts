import { useQuery } from '@tanstack/react-query';
import { InvoiceService } from '../lib/supabaseService';

export const useInvoices = () => {
  return useQuery({
    queryKey: ['invoices'],
    queryFn: InvoiceService.getAllWithClientInfo,
    staleTime: 1 * 60 * 1000,
  });
};

export const useInvoicesByClient = (clientId: string) => {
    return useQuery({
        queryKey: ['invoices', 'client', clientId],
        queryFn: () => InvoiceService.getByClientId(clientId),
        enabled: !!clientId,
        staleTime: 1 * 60 * 1000,
    });
};