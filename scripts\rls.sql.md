-- TUTO: This script enables Row Level Security (RLS) and defines access policies for your tables.
-- RLS is a crucial security feature that ensures users can only access data they are permitted to see.
-- To use it, go to the SQL Editor in your Supabase dashboard and run the entire script.
--
-- Note: This script now relies on a 'role' column in the 'clients' table.
-- The admin user should have their role set to 'admin'.

-- Helper function to check for the admin user by role
create or replace function is_admin()
returns boolean
language sql
security definer
set search_path = public
as $$
  select exists (
    select 1 from public.clients where user_id = auth.uid() and role = 'admin'
  );
$$;

-- Helper function to check if the current user owns a specific client profile
create or replace function is_client_owner(p_client_id uuid)
returns boolean
language sql
security definer
set search_path = public
as $$
  select exists (
    select 1 from public.clients where id = p_client_id and user_id = auth.uid()
  );
$$;

-- Helper function to check if user owns the conversation for a message
create or replace function is_conversation_member(p_conversation_id text)
returns boolean
language sql
security definer
set search_path = public
as $$
  select exists (
    select 1 from public.conversations c
    join public.clients cl on c.client_id = cl.id
    where c.id = p_conversation_id and cl.user_id = auth.uid()
  );
$$;


--=========================================
-- Public Tables (Readable by all, writable by admin)
--=========================================

-- BILLING_TYPES
alter table billing_types enable row level security;
drop policy if exists "Allow public read access" on billing_types;
create policy "Allow public read access" on billing_types for select using (true);
drop policy if exists "Allow admin full access" on billing_types;
create policy "Allow admin full access" on billing_types for all using (is_admin()) with check (is_admin());

-- CATEGORIES_METADATA
alter table categories_metadata enable row level security;
drop policy if exists "Allow public read access" on categories_metadata;
create policy "Allow public read access" on categories_metadata for select using (true);
drop policy if exists "Allow admin full access" on categories_metadata;
create policy "Allow admin full access" on categories_metadata for all using (is_admin()) with check (is_admin());

-- PACKAGES
alter table packages enable row level security;
drop policy if exists "Allow public read access" on packages;
create policy "Allow public read access" on packages for select using (true);
drop policy if exists "Allow admin full access" on packages;
create policy "Allow admin full access" on packages for all using (is_admin()) with check (is_admin());

-- SERVICE_CATEGORIES
alter table service_categories enable row level security;
drop policy if exists "Allow public read access" on service_categories;
create policy "Allow public read access" on service_categories for select using (true);
drop policy if exists "Allow admin full access" on service_categories;
create policy "Allow admin full access" on service_categories for all using (is_admin()) with check (is_admin());


--=========================================
-- User-Specific Tables
--=========================================

-- CLIENTS
alter table clients enable row level security;
-- Drop old multi-action policy
drop policy if exists "Allow user to manage their own profile" on clients;
-- Create new single-action policies
drop policy if exists "Allow user to read their own profile" on clients;
create policy "Allow user to read their own profile" on clients for select
  using (auth.uid() = user_id);
drop policy if exists "Allow user to insert their own profile" on clients;
create policy "Allow user to insert their own profile" on clients for insert
  with check (auth.uid() = user_id);
drop policy if exists "Allow user to update their own profile" on clients;
create policy "Allow user to update their own profile" on clients for update
  using (auth.uid() = user_id)
  with check (auth.uid() = user_id);
-- Admin access
drop policy if exists "Allow admin full access" on clients;
create policy "Allow admin full access" on clients for all using (is_admin()) with check (is_admin());

-- ORDERS
alter table orders enable row level security;
-- Drop old multi-action policy
drop policy if exists "Allow user to read and create their own orders" on orders;
-- Create new single-action policies
drop policy if exists "Allow user to read their own orders" on orders;
create policy "Allow user to read their own orders" on orders for select
  using (is_client_owner(orders.client_id));
drop policy if exists "Allow user to insert their own orders" on orders;
create policy "Allow user to insert their own orders" on orders for insert
  with check (is_client_owner(orders.client_id));
-- Admin access
drop policy if exists "Allow admin full access" on orders;
create policy "Allow admin full access" on orders for all using (is_admin()) with check (is_admin());

-- CONVERSATIONS
alter table conversations enable row level security;
-- Drop old multi-action policy
drop policy if exists "Allow user to manage their own conversations" on conversations;
-- Create new single-action policies
drop policy if exists "Allow user to read their own conversations" on conversations;
create policy "Allow user to read their own conversations" on conversations for select
  using (is_client_owner(conversations.client_id));
drop policy if exists "Allow user to insert their own conversations" on conversations;
create policy "Allow user to insert their own conversations" on conversations for insert
  with check (is_client_owner(conversations.client_id));
drop policy if exists "Allow user to update their own conversations" on conversations;
create policy "Allow user to update their own conversations" on conversations for update
  using (is_client_owner(conversations.client_id))
  with check (is_client_owner(conversations.client_id));
-- Admin access
drop policy if exists "Allow admin full access" on conversations;
create policy "Allow admin full access" on conversations for all using (is_admin()) with check (is_admin());

-- MESSAGES
alter table messages enable row level security;
-- Drop old multi-action policy
drop policy if exists "Allow user to read and create messages in their own conversations" on messages;
-- Create new single-action policies
drop policy if exists "Allow user to read messages in their own conversations" on messages;
create policy "Allow user to read messages in their own conversations" on messages for select
  using (is_conversation_member(messages.conversation_id));
drop policy if exists "Allow user to insert messages in their own conversations" on messages;
create policy "Allow user to insert messages in their own conversations" on messages for insert
  with check (is_conversation_member(messages.conversation_id));
-- Admin access
drop policy if exists "Allow admin full access" on messages;
create policy "Allow admin full access" on messages for all using (is_admin()) with check (is_admin());

-- SUBSCRIPTIONS
alter table subscriptions enable row level security;
drop policy if exists "Allow user to read their own subscriptions" on subscriptions;
create policy "Allow user to read their own subscriptions" on subscriptions for select
    using (is_client_owner(subscriptions.client_id));
drop policy if exists "Allow admin full access" on subscriptions;
create policy "Allow admin full access" on subscriptions for all using (is_admin()) with check (is_admin());

-- NOTIFICATIONS
alter table notifications enable row level security;
-- Drop old multi-action policy
drop policy if exists "Allow user to read and update their own notifications" on notifications;
-- Create new single-action policies
drop policy if exists "Allow user to read their own notifications" on notifications;
create policy "Allow user to read their own notifications" on notifications for select
  using (is_client_owner(notifications.client_id));
drop policy if exists "Allow user to update their own notifications" on notifications;
create policy "Allow user to update their own notifications" on notifications for update
  using (is_client_owner(notifications.client_id))
  with check (is_client_owner(notifications.client_id));
-- Admin access
drop policy if exists "Allow admin full access" on notifications;
create policy "Allow admin full access" on notifications for all using (is_admin()) with check (is_admin());

--=========================================
-- Stripe Integration Tables
--=========================================

-- INVOICES
alter table invoices enable row level security;
drop policy if exists "Allow user to read their own invoices" on invoices;
create policy "Allow user to read their own invoices" on invoices for select
  using (is_client_owner(invoices.client_id));
drop policy if exists "Allow admin full access" on invoices;
create policy "Allow admin full access" on invoices for all using (is_admin()) with check (is_admin());

-- PAYMENT_LOGS
alter table payment_logs enable row level security;
drop policy if exists "Allow admin full access" on payment_logs;
create policy "Allow admin full access" on payment_logs for all using (is_admin()) with check (is_admin());

-- STRIPE_PRODUCTS
alter table stripe_products enable row level security;
drop policy if exists "Allow admin full access" on stripe_products;
create policy "Allow admin full access" on stripe_products for all using (is_admin()) with check (is_admin());

--=========================================
-- Admin-Only and Public Insert Tables
--=========================================

-- CONTACT_SUBMISSIONS
alter table contact_submissions enable row level security;
drop policy if exists "Allow anonymous users to insert" on contact_submissions;
create policy "Allow anonymous users to insert" on contact_submissions for insert with check (true);
drop policy if exists "Allow admin full read/delete access" on contact_submissions;
create policy "Allow admin full read/delete access" on contact_submissions for all using (is_admin());

--=========================================
-- Settings Tables (Admin only)
--=========================================

-- PAYMENT_SETTINGS
alter table payment_settings enable row level security;
drop policy if exists "Allow public read access" on payment_settings;
create policy "Allow public read access" on payment_settings
  for select
  to authenticated, anon
  using (true);
drop policy if exists "Allow admin full access" on payment_settings;
create policy "Allow admin full access" on payment_settings for all using (is_admin()) with check (is_admin());

-- SMTP_SETTINGS
alter table smtp_settings enable row level security;
drop policy if exists "Allow admin full access" on smtp_settings;
create policy "Allow admin full access" on smtp_settings for all using (is_admin()) with check (is_admin());

-- ANNOUNCEMENT_SETTINGS
alter table announcement_settings enable row level security;
drop policy if exists "Allow public read access" on announcement_settings;
create policy "Allow public read access" on announcement_settings
  for select
  to authenticated, anon
  using (true);
drop policy if exists "Allow admin full access" on announcement_settings;
create policy "Allow admin full access" on announcement_settings for all using (is_admin()) with check (is_admin());

-- EMAIL_TEMPLATES
alter table email_templates enable row level security;
drop policy if exists "Allow admin full access" on email_templates;
create policy "Allow admin full access" on email_templates for all using (is_admin()) with check (is_admin());


--=========================================
-- Discount Codes Table (Public read, admin write)
--=========================================

-- DISCOUNT_CODES
alter table discount_codes enable row level security;
drop policy if exists "Allow public read access to active codes" on discount_codes;
create policy "Allow public read access to active codes" on discount_codes for select using (is_active = true);
drop policy if exists "Allow admin full access" on discount_codes;
create policy "Allow admin full access" on discount_codes for all using (is_admin()) with check (is_admin());