import React, { useState } from 'react';
import { Button, FormField, SidePanel } from '../../components/ui';
import { supabase } from '../../lib/supabaseClient';

interface CreateClientDrawerProps {
    onClose: () => void;
    onSuccess: (newClient: any) => void;
}

const CreateClientDrawer: React.FC<CreateClientDrawerProps> = ({ onClose, onSuccess }) => {
    const [formData, setFormData] = useState({ companyName: '', contactName: '', email: '' });
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData(prev => ({ ...prev, [e.target.name]: e.target.value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        if (!formData.companyName || !formData.contactName || !formData.email) {
            setError('All fields are required.');
            return;
        }
        setIsLoading(true);
        try {
            const { data, error: invokeError } = await supabase.functions.invoke('admin-create-client', {
                body: formData,
            });

            if (invokeError) throw invokeError;
            if (data.error) throw new Error(data.error);

            alert('Client created successfully! A setup link can now be sent.');
            onSuccess(data.client);

        } catch (err: any) {
            console.error('Failed to create client:', err);
            setError(err.message || 'An unexpected error occurred.');
        } finally {
            setIsLoading(false);
        }
    };
    
    const renderFooter = () => (
        <div className="flex gap-2">
            <Button form="create-client-form" type="submit" isLoading={isLoading} disabled={isLoading}>Create Client</Button>
            <Button type="button" variant="secondary" onClick={onClose} disabled={isLoading}>Cancel</Button>
        </div>
    );

    return (
        <SidePanel
            isOpen={true}
            onClose={onClose}
            title="Create New Client"
            size="lg"
            footer={renderFooter()}
        >
            <form id="create-client-form" onSubmit={handleSubmit} className="space-y-6">
                <p className="text-sm text-gray-500">This will create a new client account. You can then build a cart for them and send a setup link to complete payment.</p>
                {error && <div className="p-3 bg-red-50 text-red-700 text-sm border border-red-200">{error}</div>}
                <FormField label="Company Name" name="companyName" value={formData.companyName} onChange={handleChange} required />
                <FormField label="Primary Contact Name" name="contactName" value={formData.contactName} onChange={handleChange} required />
                <FormField label="Contact Email" name="email" type="email" value={formData.email} onChange={handleChange} required />
            </form>
        </SidePanel>
    );
};

export default CreateClientDrawer;