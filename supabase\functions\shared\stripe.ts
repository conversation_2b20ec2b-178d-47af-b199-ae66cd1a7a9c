// FIX: Declare <PERSON>o global to satisfy TypeScript compiler in this environment.
declare const Deno: any;

import Stripe from 'https://esm.sh/stripe@16.2.0';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import { Database } from '../../../types/database.ts';

let stripeInstance: Stripe | null = null;
let supabaseInstance: ReturnType<typeof createClient<Database>> | null = null;

export function getSupabaseClient() {
  if (supabaseInstance) {
    return supabaseInstance;
  }
  supabaseInstance = createClient<Database>(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_ANON_KEY')!
  );
  return supabaseInstance;
}

export function getSupabaseAdminClient() {
  return createClient<Database>(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
  );
}


export async function getStripeInstance(): Promise<Stripe> {
  if (stripeInstance) {
    return stripeInstance;
  }
  
  const secretKey = Deno.env.get('STRIPE_SECRET_KEY');
  if (!secretKey) {
    console.error('Stripe secret key not found in environment variables.');
    throw new Error('Stripe secret key not configured. Please set it in your Supabase project secrets.');
  }

  stripeInstance = new Stripe(secretKey, {
    apiVersion: '2024-06-20',
    httpClient: Stripe.createFetchHttpClient(),
    telemetry: false, // Disable telemetry for edge functions
  });

  return stripeInstance;
}
