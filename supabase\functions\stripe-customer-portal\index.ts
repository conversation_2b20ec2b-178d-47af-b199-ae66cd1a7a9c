import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabase = getSupabaseAdminClient();
        
        // 1. Authenticate the user from the JWT
        const { data: { user }, error: userError } = await supabase.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
        if (userError) throw userError;

        // 2. Get the client's Stripe customer ID
        const { data: client, error: clientError } = await supabase
            .from('clients')
            .select('stripe_customer_id')
            .eq('user_id', user.id)
            .single();
        
        if (clientError || !client || !client.stripe_customer_id) {
            throw new Error('Stripe customer ID not found for this user.');
        }

        const { returnUrl } = await req.json();
        if (!returnUrl) {
            throw new Error('returnUrl is required.');
        }

        // 3. Create a Stripe Customer Portal session
        const stripe = await getStripeInstance();
        const portalSession = await stripe.billingPortal.sessions.create({
            customer: client.stripe_customer_id,
            return_url: returnUrl,
        });

        return new Response(
            JSON.stringify({ url: portalSession.url }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error creating customer portal session:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
});