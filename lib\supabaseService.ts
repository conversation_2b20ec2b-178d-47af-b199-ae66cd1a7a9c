// Service layer for Supabase operations with consistent data transformation

import { supabase } from '@/lib/supabaseClient';
import type {
  Client,
  Order,
  Conversation,
  Message,
  ContactSubmission,
  PackageItem,
  BillingType,
  CategoryMetadata,
  SignupData,
  ServiceCategoryItem,
  PaymentSettings,
  SmtpSettings,
  AnnouncementSettings,
  DiscountCode,
  Subscription,
  Invoice,
  EmailTemplate,
  ProfileData,
  DashboardMetrics,
} from '@/types';
import type { DatabasePaymentSettings, DatabaseSmtpSettings, DatabaseEmailTemplate } from '@/types/database';

import {
  transformDatabaseClientToClient,
  transformClientToDatabaseClient,
  transformDatabaseOrderToOrder,
  transformOrderToDatabaseOrder,
  transformDatabaseConversationToConversation,
  transformDatabaseMessageToMessage,
  transformDatabaseContactSubmissionToContactSubmission,
  transformContactSubmissionToDatabaseContactSubmission,
  transformDatabasePackageToPackage,
  transformPackageToDatabasePackage,
  transformDatabaseBillingTypeToBillingType,
  transformBillingTypeToDatabaseBillingType,
  transformDatabaseCategoryMetadataToCategoryMetadata,
  transformCategoryMetadataToDatabaseCategoryMetadata,
  transformDatabaseServiceCategoryToServiceCategory,
  transformServiceCategoryToDatabaseServiceCategory,
  transformDatabasePaymentSettingsToPaymentSettings,
  transformPaymentSettingsToDatabasePaymentSettings,
  transformDatabaseSmtpSettingsToSmtpSettings,
  transformSmtpSettingsToDatabaseSmtpSettings,
  transformDatabaseAnnouncementSettingsToAnnouncementSettings,
  transformAnnouncementSettingsToDatabaseAnnouncementSettings,
  transformDatabaseDiscountCodeToDiscountCode,
  transformDiscountCodeToDatabaseDiscountCode,
  transformDatabaseSubscriptionToSubscription,
  transformDatabaseInvoiceToInvoice,
  transformDatabaseEmailTemplateToEmailTemplate
} from '@/lib/dataTransformers';

// Consistent select query for clients to ensure all related data is fetched uniformly.
// Invoices are fetched separately on-demand in specific components.
// Using explicit foreign key joins for robustness.
const CLIENT_SELECT_QUERY = '*, orders!client_id(*, order_items(*), order_discount_codes(*)), conversations!client_id(*, messages(*)), cart_items(*)';


// Utility to convert camelCase object to snake_case for DB operations
const camelToSnake = (obj: any): any => {
    if (Array.isArray(obj)) {
      return obj.map(v => camelToSnake(v));
    } else if (obj !== null && typeof obj === 'object') {
      return Object.keys(obj).reduce((acc, key) => {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        acc[snakeKey] = camelToSnake(obj[key]);
        return acc;
      }, {} as { [key: string]: any });
    }
    return obj;
  };

/**
 * Client operations
 */
export class ClientService {
  static async getByUserId(userId: string): Promise<Client | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('clients')
      .select(CLIENT_SELECT_QUERY)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching client:', error.message);
      return null;
    }

    return data ? transformDatabaseClientToClient(data) : null;
  }

  static async getById(id: string): Promise<Client | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('clients')
      .select(CLIENT_SELECT_QUERY)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching client by id:', error.message);
      return null;
    }

    return data ? transformDatabaseClientToClient(data) : null;
  }


  static async create(clientData: Omit<Client, 'id'>): Promise<Client | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbClient = transformClientToDatabaseClient(clientData);
    const { data, error } = await supabase
      .from('clients')
      .insert(dbClient as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating client:', error);
      return null;
    }

    return data ? transformDatabaseClientToClient(data) : null;
  }

  static async updateProfile(clientId: string, profile: Partial<ProfileData>): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbProfileData = camelToSnake(profile);

    const { error } = await supabase
      .from('clients')
      .update(dbProfileData as any)
      .eq('id', clientId);

    if (error) {
      console.error('Error updating client profile:', error);
      return false;
    }

    return true;
  }

  static async updateCart(clientId: string, cart: any[]): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');
    
    // The `cart` is now a separate table `cart_items`, this function might need to be refactored to handle cart items individually.
    // For now, assuming it's managed client-side and this is a placeholder or needs a different implementation.
    // This is a legacy function from when cart was a JSONB field.
    console.warn("ClientService.updateCart is likely deprecated due to schema changes. Cart should be managed in `cart_items` table.");
    return true;
  }

  static async getAll(): Promise<Client[]> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('clients')
      .select(CLIENT_SELECT_QUERY);

    if (error) {
      console.error('Error fetching all clients:', error.message);
      throw new Error(error.message);
    }

    return (data || []).map(transformDatabaseClientToClient);
  }

  static async delete(id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting client:', error);
      return false;
    }
    return true;
  }
}

/**
 * Order operations
 */
export class OrderService {
  static async getAll(): Promise<Order[]> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('orders')
      .select('*, order_items(*), order_discount_codes(*)')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching orders:', error.message);
      throw new Error(error.message);
    }

    return (data || []).map(transformDatabaseOrderToOrder);
  }

  static async getById(id: string): Promise<Order | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('orders')
      .select('*, order_items(*), order_discount_codes(*)')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching order:', error);
      return null;
    }

    return data ? transformDatabaseOrderToOrder(data) : null;
  }

  static async getByClientId(clientId: string): Promise<Order[]> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('orders')
      .select('*, order_items(*), order_discount_codes(*)')
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching client orders:', error.message);
      throw new Error(error.message);
    }

    return (data || []).map(transformDatabaseOrderToOrder);
  }

  static async create(orderData: Order): Promise<Order | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbOrder = transformOrderToDatabaseOrder(orderData);
    const { data, error } = await supabase
      .from('orders')
      .insert(dbOrder as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating order:', error);
      return null;
    }

    return data ? transformDatabaseOrderToOrder(data) : null;
  }

  static async update(id: string, orderData: Partial<Order>): Promise<Order | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbOrderData = camelToSnake(orderData);

    const { data, error } = await supabase
      .from('orders')
      .update(dbOrderData as any)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating order:', error.message);
      throw new Error(error.message);
    }

    return data ? transformDatabaseOrderToOrder(data) : null;
  }

  static async delete(id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { error } = await supabase
      .from('orders')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting order:', error);
      return false;
    }

    return true;
  }
}

/**
 * Conversation operations
 */
export class ConversationService {
  static async create(conversationData: Omit<Conversation, 'messages'>): Promise<Conversation | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbData = camelToSnake(conversationData);
    const { data, error } = await supabase
      .from('conversations')
      .insert(dbData as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating conversation:', error.message || error);
      return null;
    }

    return data ? transformDatabaseConversationToConversation(data) : null;
  }

  static async markAsRead(conversationId: string, isClientRead: boolean): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const updateData = isClientRead
      ? { client_has_unread: false }
      : { admin_has_unread: false };

    const { error } = await supabase
      .from('conversations')
      .update(updateData as any)
      .eq('id', conversationId);

    if (error) {
      console.error('Error marking conversation as read:', error);
      return false;
    }

    return true;
  }

  static async markAsReadByAdmin(conversationId: string): Promise<boolean> {
    return this.markAsRead(conversationId, false);
  }

  static async update(conversationId: string, updates: Partial<Conversation>): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { messages, ...validUpdates } = updates;
    const dbUpdates = camelToSnake(validUpdates);

    const { error } = await supabase
      .from('conversations')
      .update(dbUpdates as any)
      .eq('id', conversationId);

    if (error) {
      console.error('Error updating conversation:', error);
      return false;
    }

    return true;
  }
}

/**
 * Message operations
 */
export class MessageService {
  static async create(messageData: Message & { conversationId: string }): Promise<Message | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbMessage = camelToSnake(messageData);

    const { data, error } = await supabase
      .from('messages')
      .insert(dbMessage as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating message:', error.message || error);
      return null;
    }

    return data ? transformDatabaseMessageToMessage(data) : null;
  }

  static async sendFromAdmin(conversationId: string, content: string): Promise<Message | null> {
    const newMessage: Message & { conversationId: string } = {
        id: `msg-admin-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        sender: 'admin' as const,
        content,
        createdAt: new Date().toISOString(),
        conversationId: conversationId
    };
    return this.create(newMessage);
  }
}

/**
 * Contact submission operations
 */
export class ContactSubmissionService {
  static async create(submissionData: Omit<ContactSubmission, 'id' | 'submittedAt' | 'isRead' | 'isArchived'>): Promise<ContactSubmission | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbSubmission = transformContactSubmissionToDatabaseContactSubmission(submissionData);
    const { data, error } = await supabase
      .from('contact_submissions')
      .insert(dbSubmission as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating contact submission:', error);
      return null;
    }

    return data ? transformDatabaseContactSubmissionToContactSubmission(data) : null;
  }

  static async getAll(): Promise<ContactSubmission[]> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('contact_submissions')
      .select('*');

    if (error) {
      console.error('Error fetching contact submissions:', error.message);
      throw new Error(error.message);
    }

    return (data || []).map(transformDatabaseContactSubmissionToContactSubmission);
  }

  static async getById(id: string): Promise<ContactSubmission | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('contact_submissions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching contact submission:', error);
      return null;
    }

    return data ? transformDatabaseContactSubmissionToContactSubmission(data) : null;
  }

  static async update(id: string, updates: Partial<ContactSubmission>): Promise<boolean> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const dbUpdates = camelToSnake(updates);

      const { error } = await supabase
        .from('contact_submissions')
        .update(dbUpdates as any)
        .eq('id', id);

      if (error) {
        console.error('Error updating contact submission:', error);
        return false;
      }
      return true;
  }
  
  static async delete(id: string): Promise<boolean> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const { error } = await supabase
        .from('contact_submissions')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting contact submission:', error);
        return false;
      }
      return true;
  }
}

/**
 * Package operations
 */
export class PackageService {
  static async getAll(): Promise<PackageItem[]> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('packages')
      .select('*');

    if (error) {
      console.error('Error fetching packages:', error.message);
      throw new Error(error.message);
    }

    return (data || []).map(transformDatabasePackageToPackage);
  }

  static async getById(id: string): Promise<PackageItem | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('packages')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching package:', error);
      return null;
    }

    return data ? transformDatabasePackageToPackage(data) : null;
  }

  static async create(packageData: PackageItem): Promise<PackageItem | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbPackage = transformPackageToDatabasePackage(packageData);
    const { data, error } = await supabase
      .from('packages')
      .insert(dbPackage as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating package:', error);
      return null;
    }

    return data ? transformDatabasePackageToPackage(data) : null;
  }

  static async update(id: string, packageData: Partial<PackageItem>): Promise<PackageItem | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbPackage = transformPackageToDatabasePackage(packageData as PackageItem);
    const { id: _, ...dbPackageForUpdate } = dbPackage;
    const { data, error } = await supabase
      .from('packages')
      .update(dbPackageForUpdate as any)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating package:', error.message);
      throw new Error(error.message);
    }

    return data ? transformDatabasePackageToPackage(data) : null;
  }

  static async delete(id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { error } = await supabase
      .from('packages')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting package:', error);
      return false;
    }

    return true;
  }
}

/**
 * Billing type operations
 */
export class BillingTypeService {
  static async getAll(): Promise<BillingType[]> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('billing_types')
      .select('*, billing_options(*)');

    if (error) {
      console.error('Error fetching billing types:', error.message);
      throw new Error(error.message);
    }

    return (data || []).map(transformDatabaseBillingTypeToBillingType);
  }

  static async create(billingTypeData: BillingType): Promise<BillingType | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbBillingType = transformBillingTypeToDatabaseBillingType(billingTypeData);
    const { data, error } = await supabase
      .from('billing_types')
      .insert(dbBillingType as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating billing type:', error);
      return null;
    }

    return data ? transformDatabaseBillingTypeToBillingType(data) : null;
  }

  static async update(id: string, billingTypeData: BillingType): Promise<BillingType | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbBillingType = transformBillingTypeToDatabaseBillingType(billingTypeData);
    const { data, error } = await supabase
      .from('billing_types')
      .update(dbBillingType as any)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating billing type:', error.message);
      throw new Error(error.message);
    }

    return data ? transformDatabaseBillingTypeToBillingType(data) : null;
  }

  static async delete(id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { error } = await supabase
      .from('billing_types')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting billing type:', error);
      return false;
    }

    return true;
  }
}

/**
 * Category metadata operations
 */
export class CategoryMetadataService {
  static async getAll(): Promise<{ [key: string]: CategoryMetadata }> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('categories_metadata')
      .select('*');

    if (error) {
      console.error('Error fetching category metadata:', error.message);
      throw new Error(error.message);
    }

    const categoriesMetadata: { [key: string]: CategoryMetadata } = {};
    (data || []).forEach((item: any) => {
      categoriesMetadata[item.id] = transformDatabaseCategoryMetadataToCategoryMetadata(item);
    });

    return categoriesMetadata;
  }

  static async create(categoryData: CategoryMetadata & { id: string }): Promise<CategoryMetadata | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbCategoryData = transformCategoryMetadataToDatabaseCategoryMetadata(categoryData);
    const { data, error } = await supabase
      .from('categories_metadata')
      .insert(dbCategoryData as any)
      .select()
      .single();

    if (error) {
      console.error('Error creating category metadata:', error);
      return null;
    }

    return data ? transformDatabaseCategoryMetadataToCategoryMetadata(data) : null;
  }

  static async update(id: string, categoryData: Partial<CategoryMetadata>): Promise<CategoryMetadata | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const dbUpdateData = camelToSnake(categoryData);
    if (categoryData.billingType !== undefined) {
        dbUpdateData.billing_type_id = categoryData.billingType;
        delete dbUpdateData.billing_type;
    }

    const { data, error } = await supabase
      .from('categories_metadata')
      .update(dbUpdateData as any)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating category metadata:', error.message);
      throw new Error(error.message);
    }

    return data ? transformDatabaseCategoryMetadataToCategoryMetadata(data) : null;
  }

  static async delete(id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { error } = await supabase
      .from('categories_metadata')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting category metadata:', error);
      return false;
    }

    return true;
  }
}

/**
 * Service Category (About Tab) operations
 */
export class ServiceCategoryService {
  static async getAll(): Promise<ServiceCategoryItem[]> {
    if (!supabase) throw new Error('Supabase client not initialized');
    const { data, error } = await supabase.from('service_categories').select('*');
    if (error) {
      console.error('Error fetching service categories:', error.message);
      throw new Error(error.message);
    }
    return (data || []).map(transformDatabaseServiceCategoryToServiceCategory);
  }

  static async create(categoryData: ServiceCategoryItem): Promise<ServiceCategoryItem | null> {
    if (!supabase) throw new Error('Supabase client not initialized');
    const dbData = transformServiceCategoryToDatabaseServiceCategory(categoryData);
    const { data, error } = await supabase.from('service_categories').insert(dbData as any).select().single();
    if (error) {
      console.error('Error creating service category:', error);
      return null;
    }
    return data ? transformDatabaseServiceCategoryToServiceCategory(data) : null;
  }

  static async update(id: string, categoryData: Partial<ServiceCategoryItem>): Promise<ServiceCategoryItem | null> {
    if (!supabase) throw new Error('Supabase client not initialized');
    const dbUpdateData = camelToSnake(categoryData);
    const { data, error } = await supabase
      .from('service_categories')
      .update(dbUpdateData as any)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating service category:', error.message);
      throw new Error(error.message);
    }
    
    return data ? transformDatabaseServiceCategoryToServiceCategory(data) : null;
  }

  static async delete(id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');
    const { error } = await supabase.from('service_categories').delete().eq('id', id);
    if (error) {
      console.error('Error deleting service category:', error);
      return false;
    }
    return true;
  }
}

/**
 * Discount Code operations
 */
export class DiscountCodeService {
    static async getAll(): Promise<DiscountCode[]> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const { data, error } = await supabase.from('discount_codes').select('*');
      if (error) {
        console.error('Error fetching discount codes:', error.message);
        throw new Error(error.message);
      }
      return (data || []).map(transformDatabaseDiscountCodeToDiscountCode);
    }
  
    static async getByCode(code: string): Promise<DiscountCode | null> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const { data, error } = await supabase
        .from('discount_codes')
        .select('*')
        .eq('code', code.toUpperCase())
        .single();
  
      if (error) {
        // 'PGRST116' is the error code for "single() row not found"
        if (error.code !== 'PGRST116') {
            console.error('Error fetching discount code:', error);
        }
        return null;
      }
      return data ? transformDatabaseDiscountCodeToDiscountCode(data) : null;
    }
  
    static async create(codeData: DiscountCode): Promise<DiscountCode | null> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const dbData = transformDiscountCodeToDatabaseDiscountCode(codeData);
      const { data, error } = await supabase.from('discount_codes').insert(dbData as any).select().single();
      if (error) {
        console.error('Error creating discount code:', error);
        return null;
      }
      return data ? transformDatabaseDiscountCodeToDiscountCode(data) : null;
    }
  
    static async update(id: string, codeData: Partial<DiscountCode>): Promise<DiscountCode | null> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const dbUpdateData = camelToSnake(codeData);
      const { data, error } = await supabase.from('discount_codes').update(dbUpdateData as any).eq('id', id).select().single();
      if (error) {
        console.error('Error updating discount code:', error.message);
        throw new Error(error.message);
      }
      return data ? transformDatabaseDiscountCodeToDiscountCode(data) : null;
    }
  
    static async delete(id: string): Promise<boolean> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const { error } = await supabase.from('discount_codes').delete().eq('id', id);
      if (error) {
        console.error('Error deleting discount code:', error);
        return false;
      }
      return true;
    }
  }

/**
 * Subscription operations
 */
export class SubscriptionService {
    static async getAllWithClientInfo(): Promise<Subscription[]> {
        if (!supabase) throw new Error('Supabase client not initialized');

        const { data, error } = await supabase
            .from('subscriptions')
            .select(`
                *, 
                clients(*),
                packages(name),
                invoices(*)
            `);

        if (error) {
            console.error('Error fetching subscriptions with invoices:', error.message);
            throw new Error(error.message);
        }

        return (data || []).map(transformDatabaseSubscriptionToSubscription);
    }
    
    static async getByClientId(clientId: string): Promise<Subscription[]> {
        if (!supabase) throw new Error('Supabase client not initialized');
    
        const { data, error } = await supabase
            .from('subscriptions')
            .select(`
                *,
                packages(name)
            `)
            .eq('client_id', clientId)
            .order('current_period_end', { ascending: false });
    
        if (error) {
            console.error('Error fetching subscriptions for client:', error.message);
            throw new Error(error.message);
        }
    
        return (data || []).map(transformDatabaseSubscriptionToSubscription);
    }
}

/**
 * Invoice operations
 */
export class InvoiceService {
    static async getAllWithClientInfo(): Promise<Invoice[]> {
        if (!supabase) throw new Error('Supabase client not initialized');

        const { data, error } = await supabase
            .from('invoices')
            .select(`
                *, 
                clients(*)
            `)
            .order('paid_at', { ascending: false });

        if (error) {
            console.error('Error fetching invoices:', error.message);
            throw new Error(error.message);
        }
        
        return (data || []).map((dbInvoice: any) => ({
            ...transformDatabaseInvoiceToInvoice(dbInvoice),
            clientName: dbInvoice.clients?.company_name || 'Unknown Client',
        }));
    }
    
    static async getByClientId(clientId: string): Promise<Invoice[]> {
        if (!supabase) throw new Error('Supabase client not initialized');

        const { data, error } = await supabase
            .from('invoices')
            .select('*')
            .eq('client_id', clientId)
            .order('paid_at', { ascending: false });

        if (error) {
            console.error('Error fetching invoices for client:', error.message);
            throw new Error(error.message);
        }

        return (data || []).map(transformDatabaseInvoiceToInvoice);
    }
}

/**
 * Notification operations
 */
export class NotificationService {
    static async getAllUnreadAdmin(): Promise<any[]> {
        if (!supabase) throw new Error('Supabase client not initialized');
        const { data, error } = await supabase
            .from('notifications')
            .select(`*, clients(*)`)
            .is('read_at', null)
            .order('created_at', { ascending: false });
        
        if (error) {
            console.error('Error fetching notifications:', error.message);
            throw new Error(error.message);
        }
        return data || [];
    }

    static async markAsRead(notificationId: string): Promise<boolean> {
        if (!supabase) throw new Error('Supabase client not initialized');
        const { error } = await supabase
            .from('notifications')
            .update({ read_at: new Date().toISOString() } as any)
            .eq('id', notificationId);
        
        if (error) {
            console.error('Error marking notification as read:', error);
            return false;
        }
        return true;
    }
}

/**
 * Stripe Product Mapping operations
 */
export class StripeProductService {
    static async getAll(): Promise<any[]> {
        if (!supabase) throw new Error('Supabase client not initialized');
        const { data, error } = await supabase.from('stripe_products').select('*');
        if (error) {
            console.error('Error fetching stripe products mapping:', error.message);
            throw new Error(error.message);
        }
        return data || [];
    }
}

/**
 * Email Template operations
 */
export class EmailTemplateService {
    static async getAll(): Promise<EmailTemplate[]> {
        if (!supabase) throw new Error('Supabase client not initialized');
        const { data, error } = await supabase.from('email_templates').select('*');
        if (error) {
            console.error('Error fetching email templates:', error.message);
            throw new Error(error.message);
        }
        return (data || []).map(transformDatabaseEmailTemplateToEmailTemplate);
    }
    
    static async update(id: string, templateData: Partial<EmailTemplate>): Promise<EmailTemplate | null> {
        if (!supabase) throw new Error('Supabase client not initialized');
        
        const dbUpdateData = camelToSnake(templateData);

        const { data, error } = await supabase
            .from('email_templates')
            .update(dbUpdateData as any)
            .eq('id', id)
            .select()
            .single();

        if (error) {
            console.error('Error updating email template:', error.message);
            throw new Error(error.message);
        }
        return data ? transformDatabaseEmailTemplateToEmailTemplate(data) : null;
    }
}


/**
 * Generic admin operations for any table
 */
export class AdminService {
  static async updateRecord(tableName: string, data: any, id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { error } = await supabase
      .from(tableName as any)
      .update(data as any)
      .eq('id', id);

    if (error) {
      console.error(`Error updating ${tableName}:`, error);
      return false;
    }

    return true;
  }

  static async createRecord(tableName: string, data: any): Promise<any> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data: createdData, error } = await supabase
      .from(tableName as any)
      .insert(data as any)
      .select()
      .single();

    if (error) {
      console.error(`Error creating in ${tableName}:`, error);
      throw error;
    }

    return createdData;
  }

  static async deleteRecord(tableName: string, id: string): Promise<boolean> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { error } = await supabase
      .from(tableName as any)
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting from ${tableName}:`, error);
      return false;
    }

    return true;
  }

  static async getDashboardMetrics(dates?: { startDate: string, endDate: string }): Promise<DashboardMetrics | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase.rpc('get_dashboard_metrics', {
        start_date: dates?.startDate,
        end_date: dates?.endDate
    });

    if (error) {
        console.error('Error fetching dashboard metrics:', error.message);
        throw new Error(error.message);
    }
    
    return data as DashboardMetrics;
  }

  static async getPaymentSettings(): Promise<PaymentSettings | null> {
      if (!supabase) throw new Error('Supabase client not initialized');
  
      const { data, error } = await supabase
          .from('payment_settings')
          .select('stripe_enabled, stripe_public_key, method_credit_card, method_apple_pay, method_google_pay')
          .eq('id', 1);
  
      if (error) {
          console.error('Error fetching payment settings:', error.message);
          throw new Error(error.message);
      }
  
      if (!data || data.length === 0) {
          throw new Error('Payment settings not found. Please ensure they are configured in the database and RLS policy allows access.');
      }
      
      const settingsData = data[0] as any;
      
      const dbSettings: DatabasePaymentSettings = {
          id: 1,
          stripe_enabled: settingsData.stripe_enabled,
          stripe_public_key: settingsData.stripe_public_key,
          method_credit_card: settingsData.method_credit_card,
          method_apple_pay: settingsData.method_apple_pay,
          method_google_pay: settingsData.method_google_pay,
          stripe_secret_key: '' // Intentionally empty for security
      };
  
      return transformDatabasePaymentSettingsToPaymentSettings(dbSettings);
  }

  static async getSmtpSettings(): Promise<SmtpSettings | null> {
    if (!supabase) throw new Error('Supabase client not initialized');

    const { data, error } = await supabase
      .from('smtp_settings')
      .select('*')
      .single();

    if (error) {
      console.error('Error fetching SMTP settings:', error.message);
      throw new Error(error.message);
    }

    return data ? transformDatabaseSmtpSettingsToSmtpSettings(data) : null;
  }

  static async updatePaymentSettings(settings: PaymentSettings): Promise<PaymentSettings | null> {
    if (!supabase) throw new Error('Supabase client not initialized');
    
    const dbSettings = transformPaymentSettingsToDatabasePaymentSettings(settings);

    const { data, error } = await supabase
      .from('payment_settings')
      .update(dbSettings as any)
      .eq('id', 1) // Assuming single row with id 1
      .select()
      .single();

    if (error) {
      console.error('Error updating payment settings:', error.message);
      throw new Error(error.message);
    }

    return data ? transformDatabasePaymentSettingsToPaymentSettings(data) : null;
  }

  static async updateSmtpSettings(settings: SmtpSettings): Promise<SmtpSettings | null> {
    if (!supabase) throw new Error('Supabase client not initialized');
    
    const dbSettings = transformSmtpSettingsToDatabaseSmtpSettings(settings);

    const { data, error } = await supabase
      .from('smtp_settings')
      .update(dbSettings as any)
      .eq('id', 1) // Assuming single row with id 1
      .select()
      .single();

    if (error) {
      console.error('Error updating SMTP settings:', error.message);
      throw new Error(error.message);
    }

    return data ? transformDatabaseSmtpSettingsToSmtpSettings(data) : null;
  }

  static async getAnnouncementSettings(): Promise<AnnouncementSettings | null> {
    if (!supabase) throw new Error('Supabase client not initialized');
    const { data, error } = await supabase
        .from('announcement_settings')
        .select('*')
        .single();
    if (error) {
        console.error('Error fetching announcement settings:', error.message);
        throw new Error(error.message);
    }
    return data ? transformDatabaseAnnouncementSettingsToAnnouncementSettings(data) : null;
  }

  static async updateAnnouncementSettings(settings: AnnouncementSettings): Promise<AnnouncementSettings | null> {
      if (!supabase) throw new Error('Supabase client not initialized');
      const dbSettings = transformAnnouncementSettingsToDatabaseAnnouncementSettings(settings);
      const { data, error } = await supabase
          .from('announcement_settings')
          .update(dbSettings as any)
          .eq('id', 1)
          .select()
          .single();
      if (error) {
          console.error('Error updating announcement settings:', error.message);
          throw new Error(error.message);
      }
      return data ? transformDatabaseAnnouncementSettingsToAnnouncementSettings(data) : null;
  }
}