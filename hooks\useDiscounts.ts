import { useQuery } from '@tanstack/react-query';
import { DiscountCodeService } from '../lib/supabaseService';

export const useDiscountCodes = () => {
  return useQuery({
    queryKey: ['discount-codes'],
    queryFn: DiscountCodeService.getAll,
    staleTime: 5 * 60 * 1000,
  });
};

export const useDiscountCodeByCode = (code: string) => {
    return useQuery({
        queryKey: ['discount-codes', 'code', code],
        queryFn: () => DiscountCodeService.getByCode(code),
        enabled: !!code,
    });
};