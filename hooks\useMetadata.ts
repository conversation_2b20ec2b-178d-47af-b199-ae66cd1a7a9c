import { useQuery } from '@tanstack/react-query';
import { CategoryMetadataService, BillingTypeService } from '@/lib/supabaseService';

export const useCategoriesMetadata = () => {
  return useQuery({
    queryKey: ['categories-metadata'],
    queryFn: CategoryMetadataService.getAll,
    staleTime: 30 * 60 * 1000, // Categories metadata is very static
  });
};

export const useBillingTypes = () => {
  return useQuery({
    queryKey: ['billing-types'],
    queryFn: BillingTypeService.getAll,
    staleTime: 30 * 60 * 1000, // Billing types are very static
  });
};