import React, { useState, useMemo } from 'react';
import type { ChartDataPoint } from '../../types';
import { useDashboardMetrics } from '../../hooks';

type ChartPeriod = 'daily' | 'monthly' | 'yearly';
type DateRangePreset = 'last_30_days' | 'this_month' | 'last_90_days' | 'this_year' | 'all_time';

const KpiCard: React.FC<{ title: string; value: string; icon: string }> = ({ title, value, icon }) => (
    <div className="bg-white border border-gray-300 p-6">
        <div className="flex items-center justify-between">
            <div>
                <p className="text-sm text-gray-600">{title}</p>
                <p className="text-3xl font-bold text-black">{value}</p>
            </div>
            <i className={`fa-solid ${icon} text-3xl text-gray-300`}></i>
        </div>
    </div>
);

const BarChart: React.FC<{ data: ChartDataPoint[], title: string, hasProjection?: boolean }> = ({ data, title, hasProjection = false }) => {
    if (!data || data.length === 0) {
        return (
            <div className="bg-white border border-gray-300 p-6">
                <h3 className="text-lg font-semibold mb-4 text-black">{title}</h3>
                <div className="flex items-center justify-center h-64 text-gray-500">No data available for this period.</div>
            </div>
        );
    }
    const maxValue = Math.max(1, ...data.flatMap(d => [d.oneTime, d.recurring, d.projection || 0]));
    return (
        <div className="bg-white border border-gray-300 p-6 overflow-hidden">
            <h3 className="text-lg font-semibold mb-4 text-black">{title}</h3>
            <div className="overflow-x-auto pb-4">
                <div className="flex gap-4 items-end h-64 border-b border-gray-200 pb-2" style={{minWidth: `${data.length * 50}px`}}>
                    {data.map(d => (
                        <div key={d.label} className="flex flex-col items-center h-full justify-end group relative" style={{ flex: '1 1 50px' }}>
                            <div className="flex items-end h-full w-full justify-center gap-px">
                                <div className="w-1/3 bg-gray-300 hover:bg-gray-400" style={{ height: `${(d.oneTime / maxValue) * 100}%` }}></div>
                                <div className="w-1/3 bg-black hover:bg-gray-800" style={{ height: `${(d.recurring / maxValue) * 100}%` }}></div>
                                {hasProjection && d.projection && (
                                    <div className="w-1/3 bg-white border-2 border-dashed border-black" style={{ height: `${(d.projection / maxValue) * 100}%` }}></div>
                                )}
                            </div>
                            <span className="text-xs text-gray-500 mt-2 whitespace-nowrap">{d.label}</span>
                             <div className="absolute bottom-full mb-2 w-max p-2 bg-black text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                                <p><strong>One-Time:</strong> ${d.oneTime.toLocaleString()}</p>
                                <p><strong>Recurring:</strong> ${d.recurring.toLocaleString()}</p>
                                {d.subscriptions !== undefined && <p><strong>New Subs:</strong> {d.subscriptions}</p>}
                                {hasProjection && d.projection && <p><strong>Projection:</strong> ${d.projection.toLocaleString()}</p>}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
             <div className="flex justify-center gap-4 text-xs mt-4">
                <span className="flex items-center gap-2"><div className="w-3 h-3 bg-gray-300"></div> One-Time</span>
                <span className="flex items-center gap-2"><div className="w-3 h-3 bg-black"></div> Recurring</span>
                {hasProjection && <span className="flex items-center gap-2"><div className="w-3 h-3 border-2 border-dashed border-black"></div> Projection</span>}
            </div>
        </div>
    );
};

const DashboardTab: React.FC = () => {
    const [chartPeriod, setChartPeriod] = useState<ChartPeriod>('monthly');
    const [dateRangePreset, setDateRangePreset] = useState<DateRangePreset>('all_time');

    const dateRange = useMemo(() => {
        const now = new Date();
        let startDate: Date | null = null;
        let endDate: Date | null = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);

        switch (dateRangePreset) {
            case 'last_30_days':
                startDate = new Date();
                startDate.setDate(now.getDate() - 30);
                break;
            case 'this_month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
            case 'last_90_days':
                startDate = new Date();
                startDate.setDate(now.getDate() - 90);
                break;
            case 'this_year':
                startDate = new Date(now.getFullYear(), 0, 1);
                break;
            case 'all_time':
            default:
                return undefined;
        }

        return {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
        };
    }, [dateRangePreset]);
    
    const { data: dashboardMetrics, isLoading, isError } = useDashboardMetrics(dateRange);

    if (isLoading) {
        return <div className="text-center p-20"><i className="fa-solid fa-spinner fa-spin text-4xl text-gray-400"></i></div>;
    }

    if (isError || !dashboardMetrics) {
        return <div className="text-center p-20 text-red-600">Failed to load dashboard metrics.</div>;
    }

    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <KpiCard title="Total Revenue" value={`$${dashboardMetrics.totalRevenue.toLocaleString()}`} icon="fa-dollar-sign" />
                <KpiCard title="MRR (All Time)" value={`$${dashboardMetrics.mrr.toLocaleString()}`} icon="fa-arrow-rotate-right" />
                <KpiCard title="New Clients" value={dashboardMetrics.totalClients.toLocaleString()} icon="fa-users" />
                <KpiCard title="Active Subscriptions" value={dashboardMetrics.activeSubscriptions.toLocaleString()} icon="fa-arrow-trend-up" />
                <KpiCard title="Projected Revenue (30d)" value={`$${dashboardMetrics.projectedRevenue.toLocaleString()}`} icon="fa-wand-magic-sparkles" />
            </div>
            <div className="bg-white border border-gray-300 p-4 flex items-center justify-between flex-wrap gap-4">
                <div className="flex items-center gap-2">
                    <h3 className="text-lg font-semibold text-black">Analytics for:</h3>
                     <div className="flex gap-1">
                        {(['last_30_days', 'this_month', 'last_90_days', 'this_year', 'all_time'] as DateRangePreset[]).map(p => (
                            <button key={p} onClick={() => setDateRangePreset(p)} className={`px-3 py-1 text-xs font-medium border ${dateRangePreset === p ? 'bg-black text-white border-black' : 'bg-white text-gray-700 border-gray-300 hover:border-black'}`}>
                                {p.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </button>
                        ))}
                    </div>
                </div>
                <div className="flex gap-2">
                    {(['daily', 'monthly', 'yearly'] as ChartPeriod[]).map(p => (
                        <button key={p} onClick={() => setChartPeriod(p)} className={`px-3 py-1 text-sm font-medium border ${chartPeriod === p ? 'bg-black text-white border-black' : 'bg-white text-gray-700 border-gray-300 hover:border-black'}`}>
                            {p.charAt(0).toUpperCase() + p.slice(1)}
                        </button>
                    ))}
                </div>
            </div>
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                <BarChart data={dashboardMetrics[chartPeriod]} title="Revenue Analytics" hasProjection={true} />
                <BarChart data={dashboardMetrics[chartPeriod]} title="New Subscriptions" />
            </div>
        </div>
    );
};

export default DashboardTab;