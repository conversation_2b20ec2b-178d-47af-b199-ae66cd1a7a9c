-- TUTO: This script creates the database schema for the Voice AI Space portal.
-- To use it, go to the SQL Editor in your Supabase dashboard and run the entire script.

-- 1. BILLING_TYPES TABLE
-- Stores different ways billing can be calculated (e.g., subscriptions, bundles).
create table if not exists billing_types (
  id text primary key,
  name text not null,
  public_title text,
  description text,
  is_system_type boolean default false
);
comment on table billing_types is 'Stores different billing structures like subscriptions or bundles.';

-- 2. BILLING_OPTIONS TABLE
-- Stores the specific options for each billing type.
create table if not exists billing_options (
  id text primary key,
  billing_type_id text not null references billing_types(id) on delete cascade,
  label text not null,
  multiplier numeric not null,
  discount numeric not null default 0,
  is_partner_tier boolean default false,
  benefit_text text,
  is_enabled boolean default true,
  valid_from timestamptz,
  valid_to timestamptz,
  order_index integer default 0
);
comment on table billing_options is 'Specific options for each billing type, replaces JSONB array.';

-- 3. CATEGORIES_METADATA TABLE
-- Stores metadata for package categories, like titles and descriptions.
create table if not exists categories_metadata (
  id text primary key,
  tab_title text not null,
  page_title text not null,
  description text,
  billing_type_id text references billing_types(id) on delete set null,
  is_enabled boolean not null default true,
  valid_from timestamptz,
  valid_to timestamptz,
  order_index integer default 0
);
comment on table categories_metadata is 'Metadata for package categories displayed on the landing page.';

-- 4. PACKAGES TABLE
-- Stores all available service packages.
create table if not exists packages (
  id text primary key,
  category_id text not null references categories_metadata(id) on delete cascade,
  name text not null,
  price numeric not null,
  billing text not null, -- e.g., '/month', '/year', 'one-time'
  features text[] not null default '{}',
  is_addon boolean default false,
  cta_text text,
  cta_link text,
  order_index integer default 0,
  is_enabled boolean not null default true,
  valid_from timestamptz,
  valid_to timestamptz
);
comment on table packages is 'Individual service packages that clients can purchase.';

-- 5. SERVICE_CATEGORIES TABLE
-- Stores the content for the "About" tab service cards.
create table if not exists service_categories (
  id text primary key,
  title text not null,
  description text,
  items text[] not null default '{}',
  link_to_tab text,
  order_index integer default 0,
  is_enabled boolean default true
);
comment on table service_categories is 'Stores content for the service overview cards on the About tab.';

-- 6. DISCOUNT_CODES TABLE
create table if not exists discount_codes (
  id uuid primary key default gen_random_uuid(),
  code text unique not null,
  type text not null check (type in ('percentage', 'fixed')),
  value numeric not null,
  valid_from timestamptz,
  valid_to timestamptz,
  is_stackable boolean not null default false,
  is_active boolean not null default true,
  created_at timestamptz default now()
);
comment on table discount_codes is 'Stores promotional discount codes.';

-- 7. CLIENTS TABLE
-- Stores profile information for each client, linked to an authenticated user.
create table if not exists clients (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id) on delete cascade unique not null,
  role text not null default 'client',
  email text not null unique,
  created_at timestamptz default now(),
  stripe_customer_id text,
  -- Profile data moved from JSONB to columns
  company_name text,
  industry text,
  company_size text,
  website text,
  description text,
  primary_contact text,
  title text,
  phone text,
  address text,
  city text,
  state text,
  zip_code text,
  country text,
  billing_email text,
  billing_contact text,
  billing_phone text,
  account_number text,
  payment_method text,
  payment_method_last4 text,
  billing_address text,
  account_status text,
  subscription_tier text,
  member_since text,
  auto_renewal boolean,
  notifications_product_updates boolean default true,
  notifications_event_updates boolean default true,
  notifications_newsletter boolean default true,
  notifications_account_alerts boolean default true,
  -- cart jsonb is replaced by cart_items table
  cancelled_package_ids text[] default '{}'
);
comment on table clients is 'B2B Client profiles, linked to Supabase auth users.';

-- 8. CART_ITEMS TABLE
-- Replaces the `cart` JSONB column in the `clients` table.
create table if not exists cart_items (
  id uuid primary key default gen_random_uuid(),
  client_id uuid not null references clients(id) on delete cascade,
  package_id text not null,
  name text not null,
  price numeric not null,
  billing text not null,
  features text[] not null default '{}',
  is_addon boolean,
  category text not null,
  final_price numeric not null,
  final_billing text not null,
  added_at timestamptz default now()
);
comment on table cart_items is 'Stores items in a client''s shopping cart.';

-- 9. ORDERS TABLE
-- Stores order history for each client.
create table if not exists orders (
  id text primary key,
  client_id uuid not null references clients(id) on delete cascade,
  created_at timestamptz default now(),
  subtotal numeric not null,
  vat_amount numeric not null,
  total numeric not null,
  status text not null, -- e.g., 'Completed', 'Processing'
  stripe_payment_intent_id text,
  stripe_invoice_id text,
  paid_at timestamptz,
  discount_amount numeric
);
comment on table orders is 'Records of completed purchases by clients.';

-- 10. ORDER_ITEMS TABLE
-- Replaces the `items` JSONB column in the `orders` table.
create table if not exists order_items (
  id uuid primary key default gen_random_uuid(),
  order_id text not null references orders(id) on delete cascade,
  package_id text, -- Not a strict FK to preserve history if package is deleted
  name text not null,
  price numeric not null,
  billing text not null,
  features text[] not null default '{}',
  is_addon boolean,
  final_price numeric not null,
  final_billing text not null,
  category text not null
);
comment on table order_items is 'Stores line items for a specific order.';

-- 11. ORDER_DISCOUNT_CODES TABLE
-- Replaces the `discount_codes` JSONB column in the `orders` table.
create table if not exists order_discount_codes (
  id uuid primary key default gen_random_uuid(),
  order_id text not null references orders(id) on delete cascade,
  discount_code_id uuid references discount_codes(id) on delete set null,
  code text not null,
  type text not null,
  value numeric not null
);
comment on table order_discount_codes is 'Junction table for discounts applied to an order.';

-- 12. CONVERSATIONS TABLE
create table if not exists conversations (
  id text primary key,
  client_id uuid not null references clients(id) on delete cascade,
  subject text not null,
  last_updated timestamptz default now(),
  status text default 'open', -- 'open' or 'closed'
  client_has_unread boolean default false,
  admin_has_unread boolean default true
);
comment on table conversations is 'A thread of messages between a client and an admin.';

-- 13. MESSAGES TABLE
create table if not exists messages (
  id text primary key,
  conversation_id text not null references conversations(id) on delete cascade,
  sender text not null, -- 'client' or 'admin'
  content text not null,
  created_at timestamptz default now()
);
comment on table messages is 'An individual message within a conversation.';

-- 14. CONTACT_SUBMISSIONS TABLE
create table if not exists contact_submissions (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  company_name text,
  email text not null,
  phone text,
  subject text not null,
  message text not null,
  submitted_at timestamptz default now(),
  is_read boolean default false,
  is_archived boolean default false
);
comment on table contact_submissions is 'Submissions from the public contact form.';

-- 15. PAYMENT_SETTINGS TABLE
create table if not exists payment_settings (
  id int primary key default 1,
  stripe_enabled boolean not null default true,
  stripe_public_key text,
  stripe_secret_key text,
  method_credit_card boolean default true,
  method_apple_pay boolean default false,
  method_google_pay boolean default false,
  constraint single_row check (id = 1)
);
comment on table payment_settings is 'Stores global payment gateway settings.';

-- 16. SMTP_SETTINGS TABLE
create table if not exists smtp_settings (
  id int primary key default 1,
  test_mode boolean not null default true,
  server text,
  port int,
  username text,
  password text,
  encryption text,
  from_address text,
  from_name text,
  test_email_recipient text,
  constraint single_row check (id = 1)
);
comment on table smtp_settings is 'Stores global SMTP settings for sending emails.';

-- 17. SUBSCRIPTIONS TABLE
create table if not exists subscriptions (
  id uuid primary key default gen_random_uuid(),
  client_id uuid references clients(id),
  package_id text references packages(id) on delete set null,
  stripe_subscription_id text unique not null,
  status text not null,
  current_period_start timestamptz not null,
  current_period_end timestamptz not null,
  trial_start timestamptz,
  trial_end timestamptz,
  canceled_at timestamptz,
  cancel_at_period_end boolean default false,
  last_payment_error_message text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);
comment on table subscriptions is 'Stores Stripe subscription data for clients.';

-- 18. STRIPE_PRODUCTS TABLE
create table if not exists stripe_products (
  id uuid primary key default gen_random_uuid(),
  package_id text references packages(id),
  stripe_product_id text unique not null,
  stripe_price_id text,
  price_type text, -- one_time, recurring
  billing_interval text, -- month, year (for recurring)
  created_at timestamptz default now()
);
comment on table stripe_products is 'Maps local packages to Stripe products and prices.';

-- 19. INVOICES TABLE
create table if not exists invoices (
  id uuid primary key default gen_random_uuid(),
  client_id uuid references clients(id),
  stripe_invoice_id text unique not null,
  stripe_subscription_id text,
  amount_total integer not null,
  currency text default 'eur',
  status text not null,
  due_date timestamptz,
  paid_at timestamptz,
  invoice_pdf text,
  hosted_invoice_url text,
  created_at timestamptz default now()
);
comment on table invoices is 'Stores Stripe invoice data.';

-- 20. PAYMENT_LOGS TABLE
create table if not exists payment_logs (
  id uuid primary key default gen_random_uuid(),
  action text not null,
  stripe_id text not null,
  amount integer,
  currency text default 'eur',
  customer_id text,
  metadata text,
  created_at timestamptz default now()
);
comment on table payment_logs is 'Audit trail for Stripe payment events.';

-- 21. NOTIFICATIONS TABLE
create table if not exists notifications (
  id uuid primary key default gen_random_uuid(),
  client_id uuid references clients(id),
  type text not null,
  message text not null,
  stripe_subscription_id text,
  stripe_invoice_id text,
  read_at timestamptz,
  created_at timestamptz default now()
);
comment on table notifications is 'User-facing notifications for billing events.';

-- 22. ANNOUNCEMENT_SETTINGS TABLE
create table if not exists announcement_settings (
  id int primary key default 1,
  is_enabled boolean not null default false,
  message text,
  start_date timestamptz,
  end_date timestamptz,
  constraint single_row check (id = 1)
);
comment on table announcement_settings is 'Stores global settings for the announcement banner.';

-- 23. EMAIL_TEMPLATES TABLE
create table if not exists email_templates (
  id text primary key,
  subject text not null,
  body text not null, -- HTML content
  description text,
  variables text[] not null default '{}'
);
comment on table email_templates is 'Stores customizable email templates.';

-- ADD INDEXES FOR PERFORMANCE
create index if not exists idx_clients_email on clients(email);
create index if not exists idx_cart_items_client_id on cart_items(client_id);
create index if not exists idx_order_items_order_id on order_items(order_id);
create index if not exists idx_order_discount_codes_order_id on order_discount_codes(order_id);
create index if not exists idx_subscriptions_client_id on subscriptions(client_id);
create index if not exists idx_subscriptions_stripe_id on subscriptions(stripe_subscription_id);
create index if not exists idx_subscriptions_package_id on subscriptions(package_id);
create index if not exists idx_invoices_client_id on invoices(client_id);
create index if not exists idx_invoices_stripe_id on invoices(stripe_invoice_id);
create index if not exists idx_payment_logs_stripe_id on payment_logs(stripe_id);
create index if not exists idx_notifications_client_id on notifications(client_id);
create index if not exists idx_discount_codes_code on discount_codes(code);

-- ADD FOREIGN KEY FOR INVOICE -> SUBSCRIPTION LINK
alter table if exists invoices add constraint invoices_stripe_subscription_id_fkey foreign key (stripe_subscription_id) references subscriptions(stripe_subscription_id) on delete set null;
create index if not exists idx_invoices_stripe_subscription_id on invoices(stripe_subscription_id);