import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';
import Stripe from 'https://esm.sh/stripe@16.2.0';

// FIX: Declare Deno global to satisfy TypeScript compiler in this environment.
declare const Deno: any;

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = getSupabaseAdminClient();
    
    // 1. Check for admin privileges
    const { data: { user } } = await supabase.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
    if (!user) throw new Error("User not found");
    
    const { data: adminProfile, error: profileError } = await supabase
        .from('clients')
        .select('role')
        .eq('user_id', user.id)
        .single();

    if (profileError || adminProfile?.role !== 'admin') {
         throw new Error("Unauthorized: Only admins can sync clients.");
    }

    // 2. Get clientId from request and validate
    const { clientId } = await req.json();
    if (!clientId) {
        return new Response(
            JSON.stringify({ error: "clientId is required." }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
    
    // 3. Fetch client details from Supabase
    const { data: client, error: clientError } = await supabase
        .from('clients')
        .select('*') // Select all columns for the profile
        .eq('id', clientId)
        .single();
    
    if (clientError || !client) throw clientError || new Error("Client not found.");

    const { stripe_customer_id, email } = client;
    
    const stripe = await getStripeInstance();
    let customer;

    const customerPayload: Stripe.CustomerUpdateParams = {
        email: email,
        name: client.company_name,
        phone: client.phone || undefined,
        address: {
            line1: client.address,
            city: client.city,
            state: client.state,
            postal_code: client.zip_code,
            country: client.country,
        },
        description: `Primary Contact: ${client.primary_contact}`,
        metadata: {
          supabase_client_id: clientId,
        },
    };

    if (stripe_customer_id) {
        // Update existing customer
        customer = await stripe.customers.update(stripe_customer_id, customerPayload);
    } else {
        // No Stripe ID, try to find by email to prevent duplicates
        const existingCustomers = await stripe.customers.list({ email, limit: 1 });
        if (existingCustomers.data.length > 0) {
            customer = await stripe.customers.update(existingCustomers.data[0].id, customerPayload);
        } else {
            // Create new customer
            customer = await stripe.customers.create(customerPayload as Stripe.CustomerCreateParams);
        }
        
        // Update our DB with the new/found ID
        const { error: updateError } = await supabase
            .from('clients')
            .update({ stripe_customer_id: customer.id })
            .eq('id', clientId);

        if (updateError) throw updateError;
    }
    
    return new Response(
        JSON.stringify({ success: true, message: `Client ${client.company_name} synced with Stripe.`, customer }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error syncing Stripe customer:', error);
    return new Response(
        JSON.stringify({ error: error.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
