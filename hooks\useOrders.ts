import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { OrderService } from '@/lib/supabaseService';
import type { Order } from '@/types';

export const useOrders = () => {
  return useQuery({
    queryKey: ['orders'],
    queryFn: OrderService.getAll,
    staleTime: 1 * 60 * 1000, // Orders change frequently
  });
};

export const useOrder = (id: string) => {
  return useQuery({
    queryKey: ['orders', id],
    queryFn: () => OrderService.getById(id),
    enabled: !!id,
  });
};

export const useOrdersByClient = (clientId: string) => {
  return useQuery({
    queryKey: ['orders', 'client', clientId],
    queryFn: () => OrderService.getByClientId(clientId),
    enabled: !!clientId,
  });
};

export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: OrderService.create,
    onSuccess: (newOrder) => {
      // Invalidate orders queries to include new order
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      // FIX: Property 'client_id' does not exist on type 'Order'. Did you mean 'clientId'?
      if (newOrder.clientId) {
        // FIX: Property 'client_id' does not exist on type 'Order'. Did you mean 'clientId'?
        queryClient.invalidateQueries({ queryKey: ['orders', 'client', newOrder.clientId] });
      }
      // Also invalidate client data as it may include order references
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    },
  });
};