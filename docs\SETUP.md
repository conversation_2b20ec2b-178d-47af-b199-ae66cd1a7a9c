# Production Deployment & Setup Guide

This guide provides step-by-step instructions for deploying and configuring the Voice AI Space service portal for a production environment.

## 1. Project Overview

-   **Frontend**: React (with Vite)
-   **Backend & Database**: Supabase (PostgreSQL, Auth, Edge Functions)
-   **Payments**: Stripe
-   **State Management**: <PERSON><PERSON><PERSON>, React Query

## 2. Prerequisites

Before you begin, ensure you have the following installed:

-   [Node.js](https://nodejs.org/) (v18 or later)
-   [npm](https://www.npmjs.com/) (usually comes with Node.js)
-   [Supabase CLI](https://supabase.com/docs/guides/cli)

You will also need accounts for:

-   [Supabase](https://supabase.com/)
-   [Stripe](https://stripe.com/)

---

## 3. Supabase Configuration

### Step 3.1: Create a Supabase Project

1.  Go to the [Supabase Dashboard](https://app.supabase.com/) and create a new project.
2.  Once the project is created, navigate to **Project Settings > API**.
3.  Keep this page open. You will need the **Project URL** and the **`anon` public key**.

### Step 3.2: Run Database Migrations

You will run a series of SQL scripts to set up your database schema, Row Level Security (RLS), and initial required data.

1.  Navigate to the **SQL Editor** in your Supabase dashboard.
2.  Run the scripts from the `/scripts` directory **in the following order**:
    1.  `schema.sql.md`: Creates all the necessary tables and columns.
    2.  `rls.sql.md`: Enables Row Level Security and sets up access policies.
    3.  `init.sql.md`: Inserts essential data required for the app to function (e.g., billing types).

### Step 3.3: Enable Realtime Functionality

The application uses real-time subscriptions for live chat and notifications. You must enable this for the relevant tables.

1.  Go to the **SQL Editor** in your Supabase dashboard.
2.  Run the following SQL commands to publish changes from these tables to the real-time service:

    ```sql
    -- Enable Realtime for messaging and notifications
    ALTER PUBLICATION supabase_realtime ADD TABLE messages;
    ALTER PUBLICATION supabase_realtime ADD TABLE conversations;
    ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
    ```
> Alternatively, you can enable this in the Supabase Dashboard by navigating to **Database > Replication** and enabling replication for the `messages`, `conversations`, and `notifications` tables.

**Verification:**
After enabling, you can test if Realtime is working by listening for changes in the Supabase Dashboard. Go to **Database > Replication**. You should see the enabled tables under "Source". Any new message should appear in the "Broadcast" and "Realtime" logs.

### Step 3.4: Create and Configure Admin User

The application's security policies depend on a user having the `admin` role in the `clients` table. This must be set up manually for your first admin user.

1.  In your Supabase Dashboard, go to **Authentication > Users**.
2.  Click **"Add user"** and create your admin user (e.g., `<EMAIL>`). Choose a strong password.
3.  After the user is created, **copy their User UID**.
4.  Navigate to the **SQL Editor** and run the following command, replacing the placeholders with your admin user's details. This creates their profile and assigns the critical `admin` role.

    ```sql
    -- IMPORTANT: Replace placeholders with your admin user's actual UID and email.
    INSERT INTO public.clients (user_id, role, profile)
    VALUES (
      'PASTE_YOUR_ADMIN_USER_UID_HERE',
      'admin',
      '{
        "companyName": "Admin",
        "primaryContact": "Admin User",
        "email": "<EMAIL>",
        "accountStatus": "Active"
      }'
    );
    ```

### Step 3.5: Configure Supabase Secrets for Edge Functions

The Edge Functions need secure access to your Stripe keys.

1.  Link your local project to your remote Supabase instance:
    ```bash
    supabase login
    supabase link --project-ref <YOUR_PROJECT_REF>
    ```
2.  Set the required secrets. Get these keys from your Stripe Dashboard (see Step 4).
    ```bash
    # Replace with your actual Stripe secret key
    supabase secrets set STRIPE_SECRET_KEY=sk_...

    # Replace with your actual Stripe webhook signing secret
    supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_...
    ```

---

## 4. Stripe Configuration

### Step 4.1: Get API Keys

1.  Log in to your [Stripe Dashboard](https://dashboard.stripe.com/).
2.  Navigate to the **Developers > API keys** section.
3.  You will need two keys:
    -   **Publishable key**: Starts with `pk_...`
    -   **Secret key**: Starts with `sk_...`

> **Security Note**: Never expose your Secret key on the client-side. It will be stored securely in Supabase.

### Step 4.2: Configure Webhooks

Webhooks are essential for Stripe to communicate payment and subscription events back to your application.

1.  In your Stripe Dashboard, go to **Developers > Webhooks**.
2.  Click **"Add an endpoint"**.
3.  Set the **Endpoint URL** to your Supabase Edge Function URL:
    ```
    https://<YOUR_SUPABASE_PROJECT_REF>.supabase.co/functions/v1/stripe-webhook-handler
    ```
4.  Click **"Select events"** and add the following events:
    -   `payment_intent.succeeded`
    -   `charge.refunded`
    -   `invoice.payment_succeeded`
    -   `invoice.payment_failed`
    -   `customer.created`
    -   `customer.subscription.created`
    -   `customer.subscription.updated`
    -   `customer.subscription.deleted`
5.  After creating the endpoint, find the **Signing secret** (starts with `whsec_...`). You will need this for the Supabase secrets configuration (Step 3.5).

### Step 4.3: Configure Stripe Customer Portal

The application uses the Stripe Customer Portal to allow clients to manage their subscriptions, payment methods, and view billing history.

1.  In your Stripe Dashboard, go to **Settings** (gear icon in the top-right).
2.  Under **Products**, click on **Customer portal**.
3.  Configure the portal to your liking. It's recommended to enable:
    -   **Payment methods**: Allow customers to update their payment methods.
    -   **Invoice history**: Allow customers to view their past invoices.
    -   **Subscription management**: Allow customers to cancel their subscriptions.
4.  Under **App settings**, find the **Allowed return URLs** section.
5.  Add the URL to your application's profile page.
    -   Example for local development: `http://localhost:5173/profile`
    -   Example for production: `https://your-app-domain.com/profile`
6.  Click **"Save changes"**.

---

## 5. Application Configuration

### Step 5.1: Configure Frontend Supabase Keys

1.  Open the `index.html` file in the project root.
2.  Find the `<script>` tag with `window.process`.
3.  Replace the placeholder values with your Supabase Project URL and `anon` key from Step 3.1.

    ```html
    <script>
      window.process = {
        env: {
          SUPABASE_URL: 'YOUR_SUPABASE_URL', // <-- Replace this
          SUPABASE_ANON_KEY: 'YOUR_SUPABASE_ANON_KEY' // <-- Replace this
        }
      };
    </script>
    ```

### Step 5.2: Configure Payment & SMTP Settings in Supabase

You must insert your Stripe and SMTP credentials directly into the Supabase database.

1.  Go to the **SQL Editor** in your Supabase dashboard.
2.  Run the following queries, **replacing the placeholder values with your actual keys**.

    ```sql
    -- Configure Stripe Payment Settings
    -- Replace with your Stripe publishable key
    UPDATE payment_settings
    SET
      stripe_public_key = 'pk_live_...'
    WHERE id = 1;

    -- Configure SMTP Settings for sending emails
    -- Replace with your actual SMTP provider's details
    UPDATE smtp_settings
    SET
      test_mode = false, -- Set to 'false' for production
      server = 'smtp.your-provider.com',
      port = 587,
      username = 'your-smtp-username',
      password = 'your-smtp-password',
      encryption = 'starttls', -- or 'ssl', 'none'
      from_address = '<EMAIL>',
      from_name = 'Voice AI Space Portal'
    WHERE id = 1;
    ```
> **Note**: The Stripe Secret Key is managed via Supabase Secrets (Step 3.5) and is not stored in this table for security reasons.

---

## 6. Deployment

### Step 6.1: Deploy Supabase Edge Functions

Deploy all the server-side logic required for payments and customer management. JWT verification for each function is managed by the `supabase/config.toml` file.

From your project's root directory, run:

```bash
# Deploy all functions defined in supabase/functions/
supabase functions deploy
```

### Step 6.2: Build and Deploy the Frontend

1.  **Build the application**: This command compiles the React app into static files located in the `dist/` directory.
    ```bash
    npm run build
    ```

2.  **Deploy the static files**: Host the contents of the `dist/` directory on a static hosting provider like:
    -   [Vercel](https://vercel.com/)
    -   [Netlify](https://www.netlify.com/)
    -   [Supabase Storage](https://supabase.com/docs/guides/storage) (for simple hosting)

---

## 7. Post-Deployment Checklist

-   [ ] Environment variables in `index.html` are set to production Supabase keys.
-   [ ] Payment and SMTP settings in the Supabase database are correctly configured with your production keys.
-   [ ] Supabase Edge Function secrets (`STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`) are set.
-   [ ] The Stripe webhook endpoint is active and points to the correct Supabase Function URL.
-   [ ] The Stripe Customer Portal is configured with the correct return URL.
-   [ ] Test the user signup flow.
-   [ ] Test the login flow for both client and admin accounts.
-   [ ] Perform a test transaction using [Stripe's test card numbers](https://stripe.com/docs/testing).
-   [ ] Verify that a successful payment creates an order in your Supabase `orders` table and a customer in your Stripe dashboard.
-   [ ] Verify that real-time features like live chat are working for both clients and admins.
-   [ ] (Optional) For a staging or demo environment, run `scripts/demo.sql.md` to populate the database with sample data.

You have now successfully deployed the Voice AI Space service portal!