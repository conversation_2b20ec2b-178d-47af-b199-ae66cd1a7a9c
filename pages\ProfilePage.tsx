import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/stores/useAuthStore';
import { useMessagingStore } from '@/stores/useMessagingStore';
import type { Order, OrderStatus, ProfileData, Client, Invoice } from '@/types';
import ProfileTab from '@/pages/ProfileTab';
import ClientMessagingTab from '@/pages/ClientMessagingTab';
import ClientInvoicesTab from '@/pages/ClientInvoicesTab';
import ClientSubscriptionsTab from '@/pages/ClientSubscriptionsTab'; // Import du nouvel onglet
import { useInvoicesByClient } from '@/hooks';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui';
import { Button } from '@/components/ui';
import { SidePanel } from '@/components/ui';


const OrderHistoryTab: React.FC<{ orders: Order[], onViewDetails: (order: Order) => void }> = ({ orders, onViewDetails }) => {
    const statusColor: { [key in OrderStatus]: string } = {
        Completed: 'bg-green-100 text-green-800',
        Processing: 'bg-blue-100 text-blue-800',
        Cancelled: 'bg-gray-100 text-gray-800',
        Refunded: 'bg-yellow-100 text-yellow-800',
    };

    return (
        <div className="bg-white border border-gray-300">
            <div className="p-6 border-b border-gray-200">
                 <h3 className="text-lg font-semibold text-black">Order History</h3>
            </div>
            {orders.length === 0 ? (
                 <div className="text-center p-10 text-gray-500">You have not placed any orders yet.</div>
            ) : (
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Order ID</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead className="text-right">Total</TableHead>
                            <TableHead className="text-center">Items</TableHead>
                            <TableHead className="text-center">Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {orders.map((order) => (
                            <TableRow key={order.id}>
                                <TableCell className="font-mono text-xs text-gray-800">{order.id}</TableCell>
                                <TableCell className="text-gray-600">{new Date(order.createdAt).toLocaleDateString()}</TableCell>
                                <TableCell className="text-right font-medium text-black">${order.total.toFixed(2)}</TableCell>
                                <TableCell className="text-center text-gray-600">{order.items.length}</TableCell>
                                <TableCell className="text-center">
                                    <span className={`px-2 py-1 text-xs font-medium ${statusColor[order.status]}`}>{order.status}</span>
                                </TableCell>
                                <TableCell className="text-right">
                                    <Button variant="secondary" size="sm" onClick={() => onViewDetails(order)}>
                                        View Details
                                    </Button>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            )}
        </div>
    );
};

const OrderDetailSidePanel: React.FC<{
    order: Order | null;
    clientId: string;
    onClose: () => void;
}> = ({ order, clientId, onClose }) => {
    if (!order) return null;

    const { data: invoices = [] } = useInvoicesByClient(clientId);
    const relatedInvoice = invoices.find(inv => inv.stripeInvoiceId === order.stripeInvoiceId);

    const DetailItem: React.FC<{ label: string, value: React.ReactNode }> = ({ label, value }) => (
        <div>
            <p className="text-xs text-gray-500 font-medium uppercase tracking-wider">{label}</p>
            <div className="text-sm text-black mt-1">{value || 'N/A'}</div>
        </div>
    );

    return (
        <SidePanel isOpen={!!order} onClose={onClose} title={`Order #${order.id}`} size="lg">
            <div className="space-y-8">
                <section>
                    <h4 className="text-base font-semibold text-black mb-3">Order Summary</h4>
                    <div className="grid grid-cols-2 gap-y-4 gap-x-6 p-4 bg-gray-50 border border-gray-200">
                        <DetailItem label="Order Date" value={new Date(order.createdAt).toLocaleString()} />
                        <DetailItem label="Status" value={<span className="font-medium">{order.status}</span>} />
                    </div>
                </section>

                <section>
                    <h4 className="text-base font-semibold text-black mb-3">Items Ordered ({order.items.length})</h4>
                    <div className="space-y-3 border-t border-b border-gray-200 divide-y divide-gray-200">
                        {order.items.map(item => (
                            <div key={item.cartId} className="flex justify-between items-center py-3">
                                <div>
                                    <p className="font-semibold text-black">{item.name}</p>
                                    <p className="text-xs text-gray-500">{item.finalBilling}</p>
                                </div>
                                <p className="font-semibold text-black">${item.finalPrice.toFixed(2)}</p>
                            </div>
                        ))}
                    </div>
                </section>

                <section>
                    <h4 className="text-base font-semibold text-black mb-3">Financials</h4>
                    <div className="space-y-2 text-sm">
                        <div className="flex justify-between"><span className="text-gray-600">Subtotal</span><span className="font-medium text-black">${order.subtotal.toFixed(2)}</span></div>
                        {order.discountAmount > 0 && (
                            <div className="flex justify-between text-sm text-green-600">
                                <span>Discount ({order.discountCodes?.map(c => c.code).join(', ')})</span>
                                <span>-${order.discountAmount.toFixed(2)}</span>
                            </div>
                        )}
                        <div className="flex justify-between"><span className="text-gray-600">VAT (20%)</span><span className="font-medium text-black">${order.vatAmount.toFixed(2)}</span></div>
                        <div className="flex justify-between font-bold text-base pt-2 border-t border-gray-300 mt-2">
                            <span className="text-black">Total Paid</span>
                            <span className="text-black">${order.total.toFixed(2)}</span>
                        </div>
                    </div>
                </section>
                
                {(relatedInvoice?.hostedInvoiceUrl || relatedInvoice?.invoicePdf) && (
                    <div className="pt-4 border-t border-gray-200 flex flex-col sm:flex-row gap-2">
                        {relatedInvoice.hostedInvoiceUrl && (
                            <a href={relatedInvoice.hostedInvoiceUrl} target="_blank" rel="noopener noreferrer" className="flex-1">
                                <Button variant="secondary" className="w-full">View Invoice</Button>
                            </a>
                        )}
                        {relatedInvoice.invoicePdf && (
                            <a href={relatedInvoice.invoicePdf} target="_blank" rel="noopener noreferrer" className="flex-1">
                                <Button className="w-full">Download PDF</Button>
                            </a>
                        )}
                    </div>
                )}
            </div>
        </SidePanel>
    );
};

type ProfileTabKey = 'profile' | 'orders' | 'subscriptions' | 'invoices' | 'messaging';

const ProfilePage: React.FC = () => {
    const { user, setClientData } = useAuthStore();
    const { 
        sendMessage, 
        startNewConversation, 
        markConversationAsRead,
        subscribeToUpdates,
        unsubscribeFromUpdates
    } = useMessagingStore();
    const [activeTab, setActiveTab] = useState<ProfileTabKey>('profile');
    const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
    
    const client = user.data;
    
    useEffect(() => {
        subscribeToUpdates();
        return () => {
            unsubscribeFromUpdates();
        };
    }, [subscribeToUpdates, unsubscribeFromUpdates]);


    if (!client) {
      return <div>Loading profile...</div>;
    }

    const setProfileData = (updater: (prevData: ProfileData) => ProfileData) => {
        setClientData(prevClient => ({
            ...prevClient,
            profile: updater(prevClient.profile),
        }));
    };
    
    const navItems: { id: ProfileTabKey; label: string; icon: string, unreadCount?: number }[] = [
        { id: 'profile', label: 'Company Profile', icon: 'fa-building' },
        { id: 'orders', label: 'Order History', icon: 'fa-receipt' },
        { id: 'subscriptions', label: 'My Subscriptions', icon: 'fa-repeat' },
        { id: 'invoices', label: 'Invoices', icon: 'fa-file-invoice-dollar' },
        { id: 'messaging', label: 'Messages', icon: 'fa-comments', unreadCount: (client.conversations || []).filter(c => c.clientHasUnread).length },
    ];
    
    return (
        <div className="max-w-7xl mx-auto">
             <div className="mb-8">
                <h1 className="text-3xl font-bold text-black">Client Portal</h1>
                <p className="text-gray-600">Manage your profile, orders, and communications.</p>
            </div>

            <div className="border-b border-gray-300 flex mb-6 overflow-x-auto no-scrollbar">
                {navItems.map(item => (
                    <button 
                        key={item.id}
                        onClick={() => setActiveTab(item.id)} 
                        className={`flex items-center gap-2 px-4 py-4 text-sm font-medium border-b-2 whitespace-nowrap -mb-px ${activeTab === item.id ? 'border-black text-black bg-gray-50' : 'border-transparent text-gray-600 hover:text-black hover:border-gray-300'}`}
                    >
                        <i className={`fa-solid ${item.icon}`}></i> {item.label}
                        {item.unreadCount > 0 && 
                            <span className="bg-red-600 text-white text-xs font-bold w-5 h-5 flex items-center justify-center rounded-full">{item.unreadCount}</span>
                        }
                    </button>
                ))}
            </div>
            
            <div className="mt-8">
                {activeTab === 'profile' && <ProfileTab client={client} setProfileData={setProfileData} />}
                {activeTab === 'orders' && <OrderHistoryTab orders={client.orders || []} onViewDetails={setSelectedOrder} />}
                {activeTab === 'subscriptions' && <ClientSubscriptionsTab clientId={client.id} />}
                {activeTab === 'invoices' && <ClientInvoicesTab clientId={client.id} />}
                {activeTab === 'messaging' && <ClientMessagingTab 
                    conversations={client.conversations || []} 
                    handleSendMessage={(convoId, content) => sendMessage(convoId, content, client.id)}
                    handleStartNewConversation={(subject, msg) => startNewConversation(subject, msg, client.id)}
                    handleMarkConversationAsRead={markConversationAsRead}
                />}
            </div>

            <OrderDetailSidePanel
                order={selectedOrder}
                clientId={client.id}
                onClose={() => setSelectedOrder(null)}
            />
        </div>
    );
};

export default ProfilePage;