import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import Hero from '@/components/Hero';
import Tabs from '@/components/Tabs';
import PackageTab from '@/pages/PackageTab';
import AboutTab from '@/pages/AboutTab';
import { usePackagesByCategory, useCategoriesMetadata, useBillingTypes, useAdminActions } from '@/hooks';
import { useCartStore } from '@/stores/useCartStore';
import { useAuthStore } from '@/stores/useAuthStore';
import type { NavItem, PackageItem, CategoryMetadata } from '@/types';
import SkeletonCard from '@/components/skeletons/SkeletonCard';

// Skeleton for the Tabs component while data is loading
const TabsSkeleton: React.FC = () => (
    <div className="bg-white border-2 border-gray-400 animate-pulse">
      <div className="px-4 sm:px-6">
        <div className="flex justify-center overflow-x-auto no-scrollbar">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="px-6 py-3">
              <div className="h-6 w-24 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
);

const isItemVisible = (item: { isEnabled?: boolean, validFrom?: string | null, validTo?: string | null }) => {
    if (item.isEnabled === false) return false;
    const now = new Date();
    const from = item.validFrom ? new Date(item.validFrom) : null;
    const to = item.validTo ? new Date(item.validTo) : null;
    if (from && now < from) return false;
    if (to && now > to) return false;
    return true;
};


const LandingPage: React.FC = () => {
  // Fetch data using React Query hooks
  const { data: packages = {}, isLoading: packagesLoading } = usePackagesByCategory();
  const { data: categoriesMetadata = {}, isLoading: categoriesLoading } = useCategoriesMetadata();
  const { data: billingTypes = [], isLoading: billingTypesLoading } = useBillingTypes();

  // Get cart and auth state
  const { cart, addToCart } = useCartStore();
  const { user, isLoading: authLoading } = useAuthStore();
  const isAdmin = user.type === 'admin';
  const adminActions = useAdminActions();
  const queryClient = useQueryClient();

  const isLoading = packagesLoading || categoriesLoading || billingTypesLoading || authLoading;
  const [activeTab, setActiveTab] = useState('about');
  const [selectedBillingOptions, setSelectedBillingOptions] = useState<{ [billingTypeId: string]: string }>({});

  // State for NavItems and DND
  const [orderedNavItems, setOrderedNavItems] = useState<NavItem[]>([]);
  const [draggedId, setDraggedId] = useState<string | null>(null);
  const [dropTargetId, setDropTargetId] = useState<string | null>(null);
  
  useEffect(() => {
    const conferenceKey = 'onlineDemos';

    const visibleCategories = Object.entries(categoriesMetadata)
      .filter(([key, metadata]) => isAdmin || isItemVisible(metadata))
      .sort(([, a], [, b]) => ((a as CategoryMetadata).orderIndex ?? 0) - ((b as CategoryMetadata).orderIndex ?? 0))
      .reduce((acc: { [key: string]: CategoryMetadata }, [key, metadata]) => {
        acc[key] = metadata as CategoryMetadata;
        return acc;
      }, {});

      const allDynamicKeys = Object.keys(visibleCategories).filter(k => k !== conferenceKey);

      const dynamicItems = allDynamicKeys.map(key => {
        const metadata = visibleCategories[key];
        return {
          id: key,
          label: metadata.tabTitle,
          mobileLabel: metadata.tabTitle,
          laptopLabel: metadata.tabTitle,
          isDraggable: true,
        };
      });

    const conferenceMeta = visibleCategories[conferenceKey];
    const conferenceItem = conferenceMeta ? [{
        id: 'conference',
        label: conferenceMeta.tabTitle,
        mobileLabel: conferenceMeta.tabTitle,
        laptopLabel: conferenceMeta.tabTitle,
        isDraggable: true
    }] : [];

    setOrderedNavItems([
      { id: 'about', label: 'About Our Services', mobileLabel: 'Service', laptopLabel: 'About Services', isDraggable: false },
      ...dynamicItems,
      ...conferenceItem,
    ]);

  }, [categoriesMetadata, isAdmin]);
  

  const handleBillingOptionChange = (billingTypeId: string, optionId: string) => {
    setSelectedBillingOptions(prev => ({
        ...prev,
        [billingTypeId]: optionId,
    }));
  };

  // DND Handlers for Tabs
  const handleDragStart = (id: string) => setDraggedId(id);
  const handleDragEnter = (id: string) => {
    if (draggedId !== id) {
        setDropTargetId(id);
    }
  };
  const handleDragEnd = () => {
      setDraggedId(null);
      setDropTargetId(null);
  };
  const handleDrop = async (droppedOnId: string) => {
      if (!draggedId || draggedId === droppedOnId) {
          handleDragEnd();
          return;
      }
  
      const items = [...orderedNavItems];
      const draggedIndex = items.findIndex(item => item.id === draggedId);
      const targetIndex = items.findIndex(item => item.id === droppedOnId);
  
      const [draggedItem] = items.splice(draggedIndex, 1);
      items.splice(targetIndex, 0, draggedItem);
      
      setOrderedNavItems(items); // Optimistic UI update
      handleDragEnd();
  
      // Persist changes
      const dynamicItems = items.filter(item => item.isDraggable);
      const updatePromises = dynamicItems.map((item, index) => {
          const {id, ...rest} = item; // we only need id and index
          return adminActions.updateRecord('categories_metadata', { orderIndex: index }, id)
      });
      try {
          await Promise.all(updatePromises);
          await queryClient.invalidateQueries({ queryKey: ['categories-metadata'] });
      } catch (e) {
          console.error("Failed to save category order", e);
          // Revert UI state on error - refetching should do this, but could be explicit
      }
  };

  const renderTabContent = () => {
    if (activeTab === 'about') {
        // AboutTab has its own internal loading state via useServiceCategories
        return <AboutTab setActiveTab={setActiveTab} isAdmin={isAdmin} />;
    }

    const isDynamicTab = activeTab !== 'about';

    if (isDynamicTab && isLoading) {
        return (
            <div className="max-w-7xl mx-auto space-y-12">
                {/* Skeleton for DynamicBillingToggle */}
                <div className="bg-white border border-gray-300 p-6 animate-pulse">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                        <div className="w-full lg:w-1/2 space-y-2">
                            <div className="h-6 bg-gray-200 w-1/3 rounded"></div>
                            <div className="h-4 bg-gray-200 w-2/3 rounded"></div>
                        </div>
                        <div className="flex flex-wrap gap-2">
                            <div className="h-10 w-24 bg-gray-200 rounded"></div>
                            <div className="h-10 w-24 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                </div>

                {/* Skeleton for Title and Description */}
                <div className="mb-8">
                  <div className="h-10 bg-gray-200 w-1/2 mb-4 animate-pulse rounded"></div>
                  <div className="h-4 bg-gray-200 w-3/4 animate-pulse rounded"></div>
                </div>

                {/* Skeleton for Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {Array.from({ length: 4 }).map((_, index) => <SkeletonCard key={index} />)}
                </div>
            </div>
        );
    }
    
    const categoryKey = activeTab === 'conference' ? 'onlineDemos' : activeTab;
    const metadata = categoriesMetadata[categoryKey];
    const packageList = packages[categoryKey];

    if (metadata && packageList) {
        const billingType = billingTypes.find(bt => bt.id === metadata.billingType);
        const selectedOptionId = selectedBillingOptions[metadata.billingType] || billingType?.options[0]?.id;
        const selectedOption = billingType?.options.find(opt => opt.id === selectedOptionId);
        
        const visiblePackages = isAdmin ? packageList : packageList.filter(isItemVisible);

        return (
          <PackageTab
            pageTitle={metadata.pageTitle}
            description={metadata.description}
            packages={visiblePackages}
            category={categoryKey}
            cart={cart}
            addToCart={addToCart}
            isLoading={false} // Loading is handled above
            billingType={billingType}
            selectedBillingOption={selectedOption}
            onBillingOptionChange={handleBillingOptionChange}
            isAdmin={isAdmin}
          />
        );
    }

    // Fallback if data is loaded but the specific category is not found
    if (isDynamicTab && !isLoading) {
        return (
            <div className="text-center py-20">
                <i className="fa-solid fa-box-open text-5xl text-gray-300 mb-4"></i>
                <h2 className="text-2xl font-semibold text-black">Category Not Available</h2>
                <p className="text-gray-600 mt-2">The requested service category is not available at the moment.</p>
            </div>
        )
    }
    
    // Default fallback, e.g. if tab state is somehow invalid
    return <AboutTab setActiveTab={setActiveTab} isAdmin={isAdmin} />;
  };

  return (
    <div className="space-y-8">
      <Hero />

      <div className="sticky top-[56px] z-20">
        <div className="max-w-6xl mx-auto">
          {isLoading ? (
            <TabsSkeleton />
          ) : (
            <Tabs 
              navItems={orderedNavItems} 
              activeTab={activeTab} 
              setActiveTab={setActiveTab}
              isAdmin={isAdmin}
              onDragStart={handleDragStart}
              onDrop={handleDrop}
              onDragEnter={handleDragEnter}
              onDragEnd={handleDragEnd}
              draggedId={draggedId}
              dropTargetId={dropTargetId}
            />
          )}
        </div>
      </div>
      <div className="mt-8">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default LandingPage;