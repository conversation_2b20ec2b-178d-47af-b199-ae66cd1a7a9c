-- TUTO: This script initializes the database with essential data that is required for the application to function.
-- Run this script AFTER running schema.sql.md and rls.sql.md, but BEFORE running demo.sql.md.

-- Temporarily disable RLS to allow inserting data as a superuser
set session_replication_role = replica;

-- Clear existing data to ensure a clean slate
delete from billing_options;
delete from billing_types;

-- INSERT BILLING TYPES (Initialization Data)
insert into billing_types (id, name, public_title, description, is_system_type) values
('standard_subscription', 'Standard Subscription', 'Choose Your Billing Cycle', 'Save more with longer commitments. Billed upfront for the chosen period.', true),
('event_bundle', 'Event Bundles', 'Bundle and Save on Events', 'Purchase multiple event packages for a discount.', true),
('none', 'None (One-Time Only)', 'One-Time Purchase', 'This is a single, one-time payment.', true);

-- INSERT BILLING OPTIONS for 'standard_subscription'
insert into billing_options (id, billing_type_id, label, multiplier, discount, benefit_text, is_partner_tier) values
('monthly', 'standard_subscription', 'Monthly', 1, 0, 'Billed monthly', false),
('quarterly', 'standard_subscription', 'Quarterly', 3, 10, 'Billed every 3 months', false),
('half-yearly', 'standard_subscription', 'Half-Yearly', 6, 15, 'Includes 30-day pause option', false),
('yearly', 'standard_subscription', 'Yearly', 12, 20, 'Includes Partner Tier benefits', true);

-- INSERT BILLING OPTIONS for 'event_bundle'
insert into billing_options (id, billing_type_id, label, multiplier, discount) values
('single_event', 'event_bundle', 'Single Event', 1, 0),
('3_events', 'event_bundle', '3-Event Bundle', 3, 15),
('6_events', 'event_bundle', '6-Event Bundle', 6, 25);


-- INSERT ANNOUNCEMENT SETTINGS
delete from announcement_settings;
insert into announcement_settings (id, is_enabled, message, start_date, end_date) values
(1, false, '<p>Sample announcement! Check out our <a href="/#media" style="font-weight: bold; text-decoration: underline;">Media Packages</a> for new offers.</p>', null, null);

-- INSERT EMAIL TEMPLATES
delete from email_templates;
insert into email_templates (id, subject, description, variables, body) values
(
  'client_setup_link',
  'Complete Your Voice AI Space Account Setup for {{companyName}}',
  'Email sent to a new client created by an admin, containing a link to set their password and pay for pre-selected services.',
  ARRAY['{{companyName}}', '{{cartItemsList}}', '{{cartTotal}}', '{{setupLink}}'],
  '<h1>Welcome to Voice AI Space!</h1>
<p>Your account for <strong>{{companyName}}</strong> has been created. Please complete your profile setup and pay for the services selected for you by our team.</p>
<h3>Services in your cart:</h3>
<ul>
    {{cartItemsList}}
</ul>
<p><strong>Total (inc. VAT): ${{cartTotal}}</strong></p>
<p>Please click the link below to set your password and complete your purchase:</p>
<p><a href="{{setupLink}}" style="display: inline-block; padding: 12px 20px; background-color: black; color: white; text-decoration: none; font-weight: bold;">Complete Setup & Pay Now</a></p>
<p>If you have any questions, please reply to this email.</p>
<p>Best,<br>The Voice AI Space Team</p>'
);


-- Re-enable RLS
set session_replication_role = 'origin';

-- Final message
SELECT 'Initialization data (Billing Types & Announcement Settings) has been successfully inserted.';
