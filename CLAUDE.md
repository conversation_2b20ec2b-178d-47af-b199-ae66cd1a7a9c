# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start development server with Vite
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Supabase Edge Functions
- `supabase functions deploy <function-name>` - Deploy specific function
- `supabase functions logs <function-name> --follow` - Monitor function logs
- `supabase secrets set KEY=value` - Set environment secrets for functions
- `supabase link --project-ref <ref>` - Link to remote project

### Notes
- No test or lint commands are configured in package.json
- Uses Vite as the build tool and development server
- TypeScript configuration with strict mode enabled
- ES modules with top-level type declarations
- Supabase CLI required for Edge Functions development

## Architecture Overview

This is a React-based service portal for VoiceAI Space with Supabase backend integration. The application uses a single-page architecture with client-side routing and state management.

### Core Structure
- **App.tsx** - Main application component with React Router and component imports
- **index.tsx** - Application entry point with React Router setup
- **types.ts** - Comprehensive type definitions for the entire application
- **stores/** - Zustand-based state management stores
  - `useAuthStore.ts` - Authentication state and user management
  - `useCartStore.ts` - Shopping cart state management
  - `useDataStore.ts` - Application data state
  - `useMessagingStore.ts` - Real-time messaging state
  - `useAdminStore.ts` - Admin-specific state
  - `useOrderStore.ts` - Order management state
  - `useToastStore.ts` - Toast notification state
- **lib/supabaseClient.ts** - Supabase client configuration
- **lib/dataTransformers.ts** - Data transformation functions between database and application formats
- **lib/supabaseService.ts** - Service layer with type-safe database operations
- **lib/queryClient.ts** - TanStack Query client configuration

### Key Features
- **Authentication**: Supabase Auth with email/password
- **User Management**: Client profiles with admin impersonation capability
- **E-commerce**: Package browsing, cart management, and checkout flow
- **Messaging**: Real-time client-admin communication system
- **Admin Dashboard**: Full administrative interface for managing clients, packages, and settings

### Component Architecture
- **components/** - Reusable UI components
  - `Navbar.tsx` - Main navigation with cart, authentication, and impersonation states
  - `Footer.tsx` - Site footer with links
  - `CartDrawer.tsx` - Sliding cart drawer with checkout functionality
  - `CheckoutForm.tsx` - Stripe payment form integration
  - `ImpersonationBanner.tsx` - Admin impersonation indicator
  - `PackageCard.tsx` - Product display cards with pricing
  - `PasswordStrengthMeter.tsx` - Real-time password validation
  - `Tabs.tsx` - Reusable tab navigation component
  - `Hero.tsx` - Landing page hero section
  - `icons.tsx` - SVG icon components
- **pages/** - Full page components and tab views
  - `LandingPage.tsx` - Public homepage with package display
  - `LoginPage.tsx` - Authentication form
  - `SignupPage.tsx` - User registration with profile data
  - `ProfilePage.tsx` - Client profile management and messaging
  - `AdminPage.tsx` - Administrative dashboard
  - `CartPage.tsx` - Shopping cart and checkout
  - `ContactPage.tsx` - Contact form submission
  - Tab components: `ProfileTab.tsx`, `AdminTab.tsx`, `PackageTab.tsx`, etc.
- **components/ui/** - Basic UI primitives
  - `FormField.tsx` - Reusable form input with validation
  - `ToggleSwitch.tsx` - Toggle switch component
- **components/skeletons/** - Loading state components
  - `SkeletonCard.tsx` - Package card loading placeholder

### Data Flow
- **Zustand State Management**: Application state distributed across specialized Zustand stores
- **TanStack Query**: Server state management with caching, background updates, and optimistic updates
- **Hook-based Initialization**: Auth state initialization via `useAuthInitialization` hook
- **Real-time Updates**: Real-time updates managed through `useMessagingStore` with Supabase subscriptions
- **Service Layer**: Type-safe operations through dedicated service classes
- **Data Transformation**: Automatic conversion between database and application formats

### Database Integration
- **Supabase Tables**:
  - `clients` - User profiles, cart data, and orders (JSON fields)
  - `packages` - Service packages with pricing and features
  - `orders` - Purchase records with items and totals
  - `conversations` - Client-admin messaging threads
  - `messages` - Individual messages within a conversation
  - `contact_submissions` - Contact form submissions
  - `payment_settings` - Stripe configuration
  - `smtp_settings` - Email server configuration
  - `categories_metadata` - Package category information
  - `billing_types` - Billing cycle definitions with options
- **Authentication**: Supabase Auth with user_id foreign key relationships
- **Admin Detection**: Role-based check using the `role` column in the `clients` table (`role = 'admin'`).
- **Type Safety**: Complete database schema types in `types/database.ts`
- **Data Transformation**: Bidirectional transformers for snake_case ↔ camelCase conversion
- **Service Classes**: Dedicated service classes for each entity with CRUD operations

### State Management Patterns
- **Zustand Stores**: Modular state management with dedicated stores for different concerns
- **Authentication State**: `useAuthStore` manages session, user state, and auth initialization
- **Shopping Cart**: `useCartStore` handles cart state with database synchronization
- **Data Management**: `useDataStore` centralizes application data fetching and caching
- **Admin Features**: `useAdminStore` manages admin-specific state and impersonation
- **Real-time Messaging**: `useMessagingStore` handles Supabase subscriptions for live updates
- **Order Management**: `useOrderStore` tracks order state and checkout flow
- **Notifications**: `useToastStore` manages toast notifications across the app
- **TanStack Query Integration**: Server state caching and background synchronization

### Naming Conventions & Data Mapping
- **Database Columns**: snake_case (user_id, created_at, client_id, conversation_id, last_updated)
- **TypeScript/React Properties**: camelCase (companyName, primaryContact, billingEmail)
- **Database References**: Keep snake_case for database column references (conversation_id, user_id)
- **Application Properties**: Use camelCase for application-specific properties
- **Data Transformation**: Explicit transformers in `lib/dataTransformers.ts` handle conversion between formats
- **Service Layer**: `lib/supabaseService.ts` provides type-safe database operations with automatic transformation
- **Schema Types**: Database schema types in `types/database.ts` for compile-time safety

### Routing Architecture
- **React Router v7**: Client-side routing with React Router DOM
- **Protected Routes**: Custom ProtectedRoute component with auth and admin requirements
- **Route Structure**:
  - `/` - Landing page (public)
  - `/login` - Authentication page
  - `/signup` - User registration
  - `/profile` - Client dashboard (protected)
  - `/admin` - Admin dashboard (admin only)
  - `/cart` - Shopping cart and checkout
  - `/contact` - Contact form
  - `/terms`, `/privacy` - Legal pages
  - `/verify-email` - Email verification
- **Navigation Guards**: Automatic redirection based on authentication state
- **Dynamic Content**: Route content changes based on user type and authentication

### Dependencies & Technology Stack
- **React 18.3.1**: Modern React with hooks and strict mode
- **React Router DOM 7.8.2**: Client-side routing
- **Supabase 2.45.0**: Backend as a service (auth, database, real-time)
- **Zustand 5.0.8**: Lightweight state management
- **TanStack React Query 5.89.0**: Server state management with devtools
- **Stripe Integration**: Payment processing with React components
  - `@stripe/stripe-js 4.1.0`
  - `@stripe/react-stripe-js 2.8.0`
- **React Error Boundary 4.0.13**: Error boundary component for error handling
- **TypeScript 5.8.2**: Strong typing with strict configuration
- **Vite 6.2.0**: Build tool and development server
- **Tailwind CSS**: Utility-first CSS framework (implied from class usage)

### Configuration
- **Supabase Configuration**: Client configuration in `lib/supabaseClient.ts`
- **Vite Configuration**: Path alias for project root (`@/`)
- **TypeScript**: Strict mode with comprehensive type definitions
- **Build Configuration**: ES modules with modern target
- **TanStack Query**: Configured in `lib/queryClient.ts` with default options

### Payment Integration
- **Stripe Integration**: Complete payment processing setup
  - Stripe Elements for secure form handling
  - Dynamic payment methods configuration
  - Admin-configurable payment settings
- **Payment Settings**: Database-stored configuration
  - Stripe public/secret keys
  - Enabled payment methods (credit card, Apple Pay, Google Pay)
- **Checkout Flow**: Guest and authenticated user checkout support

### Security & Best Practices
- **Type Safety**: Comprehensive TypeScript types for all entities
- **Data Validation**: Input validation and sanitization
- **Authentication**: Secure Supabase Auth integration
- **Authorization**: Role-based access control (client/admin)
- **SQL Injection Prevention**: Parameterized Supabase queries
- **XSS Protection**: React's built-in XSS protection
- **Environment Variables**: Secure credential management

### Performance Considerations
- **Loading States**: Skeleton components and loading indicators
- **Data Fetching**: Efficient parallel data loading
- **State Management**: Centralized state to avoid prop drilling
- **Bundle Size**: Modern ES modules with tree shaking
- **Real-time Updates**: Selective state updates to minimize re-renders

### Supabase Edge Functions
The project includes multiple Edge Functions for backend operations:
- **Stripe Integration Functions**: Customer creation, payment intents, webhooks, refunds, subscriptions
- **Admin Functions**: Client creation, setup links, product management
- **Configuration**: Function-specific JWT verification settings in `supabase/config.toml`
- **Shared Utilities**: Common CORS and Stripe utilities in `supabase/functions/shared/`

### Development Notes
- State management has been migrated from central App.tsx to distributed Zustand stores
- TanStack Query provides server state management and caching
- Real-time features use Supabase subscriptions managed through dedicated stores
- All new components should follow the established store patterns for state management
