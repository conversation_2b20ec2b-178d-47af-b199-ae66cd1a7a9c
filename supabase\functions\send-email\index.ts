import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../shared/cors.ts';
import { SmtpClient } from "https://deno.land/x/smtp/mod.ts";

// FIX: Declare Deno global to satisfy TypeScript compiler in this environment.
declare const Deno: any;

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabaseAdmin = createClient(
            Deno.env.get('SUPABASE_URL')!,
            Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
        );

        // Security check: Ensure the caller is authenticated.
        const { data: { user } } = await supabaseAdmin.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
        if (!user) {
            throw new Error("Unauthorized: Authentication required.");
        }

        const { to, subject, html } = await req.json();
        if (!to || !subject || !html) {
            return new Response(
                JSON.stringify({ error: "Missing required fields: to, subject, html." }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }
        
        // Fetch SMTP settings from the database
        const { data: smtpSettings, error: smtpError } = await supabaseAdmin
            .from('smtp_settings')
            .select('*')
            .single();

        if (smtpError || !smtpSettings) {
            throw smtpError || new Error("SMTP settings not configured in the database.");
        }

        // Handle Test Mode
        if (smtpSettings.test_mode) {
            console.log("--- SMTP TEST MODE ---");
            console.log(`Email intended for: ${to}`);
            console.log(`Subject: ${subject}`);
            console.log(`Body (HTML): ${html}`);
            console.log("--- END TEST MODE ---");

            return new Response(
                JSON.stringify({ success: true, message: 'Email logged in test mode.' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }

        // Configure and use SMTP client
        const smtp = new SmtpClient();
        await smtp.connect({
            hostname: smtpSettings.server,
            port: smtpSettings.port,
            username: smtpSettings.username,
            password: smtpSettings.password,
            tls: smtpSettings.encryption === 'starttls',
        });

        await smtp.send({
            from: `"${smtpSettings.from_name}" <${smtpSettings.from_address}>`,
            to: to,
            subject: subject,
            html: html,
        });

        await smtp.close();
        
        return new Response(
            JSON.stringify({ success: true, message: 'Email sent successfully.' }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error in send-email function:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
});