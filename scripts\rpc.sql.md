-- TUTO: This script creates a database function (RPC) to calculate dashboard metrics.
-- Run this script in your Supabase SQL Editor AFTER running schema.sql.md.
-- This function provides a real, performant implementation to replace the client-side mock calculations.
-- V2: Adds date range filtering and revenue projection.

drop function if exists get_dashboard_metrics();

create or replace function get_dashboard_metrics(
    start_date timestamptz default null,
    end_date timestamptz default null
)
returns json
security definer
language plpgsql
as $$
declare
  metrics json;
  -- KPI variables
  total_revenue numeric;
  mrr numeric;
  total_clients integer;
  active_subscriptions integer;
  projected_revenue numeric;
  -- Chart data variables
  daily_data json;
  monthly_data json;
  yearly_data json;
  -- Date range variables
  from_date timestamptz;
  to_date timestamptz;
begin
  -- Set date range defaults if not provided
  from_date := coalesce(start_date, '1970-01-01'::timestamptz);
  to_date := coalesce(end_date, now());

  -- KPI Calculations (within date range)
  select coalesce(sum(total), 0) into total_revenue from orders where status = 'Completed' and created_at between from_date and to_date;
  select count(distinct id) into total_clients from clients where role = 'client' and created_at between from_date and to_date;
  
  -- MRR and Active Subscriptions are point-in-time metrics, so they ignore the date range for accuracy.
  select coalesce(sum(
    case
      when p.billing = '/month' then p.price
      when p.billing = '/year' then p.price / 12
      else 0
    end
  ), 0) into mrr
  from subscriptions s
  join packages p on s.package_id = p.id
  where s.status in ('active', 'trialing');

  select count(*) into active_subscriptions from subscriptions where status in ('active', 'trialing');
  
  -- Projected Revenue (Next 30 Days)
  select coalesce(
      mrr + (
          select sum(total) / 3 
          from orders 
          where status = 'Completed' and created_at >= now() - interval '90 days'
      ), mrr
  ) into projected_revenue;

  -- Daily Data (for the specified range, or last 30 days if no range)
  select json_agg(t) into daily_data from (
    select
      to_char(d.day, 'MM-DD') as label,
      coalesce(o.one_time, 0)::numeric as "oneTime",
      coalesce(o.recurring, 0)::numeric as "recurring",
      coalesce(s.new_subs, 0)::integer as "subscriptions"
    from generate_series(
        greatest(from_date, now() - interval '90 days'), -- Limit daily view for performance
        to_date, 
        '1 day'::interval
    ) as d(day)
    left join (
      select
        date_trunc('day', created_at) as day,
        sum(case when oi.billing = 'one-time' then oi.final_price else 0 end) as one_time,
        sum(case when oi.billing != 'one-time' then oi.final_price else 0 end) as recurring
      from orders join order_items oi on orders.id = oi.order_id where status = 'Completed' group by 1
    ) as o on o.day = d.day
    left join (
        select date_trunc('day', created_at) as day, count(*) as new_subs from subscriptions group by 1
    ) as s on s.day = d.day
    order by d.day
  ) t;

  -- Monthly Data
  select json_agg(t) into monthly_data from (
    with months as (
        select date_trunc('month', m)::date as month from generate_series(from_date, to_date, '1 month'::interval) m
    ),
    monthly_revenue as (
        select
            date_trunc('month', o.created_at)::date as month,
            sum(case when oi.billing = 'one-time' then oi.final_price else 0 end) as one_time,
            sum(case when oi.billing != 'one-time' then oi.final_price else 0 end) as recurring
        from orders o join order_items oi on o.id = oi.order_id
        where o.status = 'Completed' and o.created_at between from_date and to_date
        group by 1
    ),
    monthly_subs as (
        select date_trunc('month', created_at)::date as month, count(*) as new_subs
        from subscriptions where created_at between from_date and to_date group by 1
    )
    select
        to_char(m.month, 'Mon') as label,
        coalesce(mr.one_time, 0)::numeric as "oneTime",
        coalesce(mr.recurring, 0)::numeric as "recurring",
        coalesce(ms.new_subs, 0)::integer as "subscriptions",
        -- Projection for the current month
        case
            when date_trunc('month', now())::date = m.month and extract(day from now()) < extract(day from (date_trunc('month', now()) + interval '1 month - 1 day'))
            then round(
                (coalesce(mr.one_time, 0) + coalesce(mr.recurring, 0)) / 
                extract(day from now()) * 
                extract(day from (date_trunc('month', now()) + interval '1 month - 1 day'))
            )
            else null
        end::numeric as projection
    from months m
    left join monthly_revenue mr on m.month = mr.month
    left join monthly_subs ms on m.month = ms.month
    order by m.month
  ) t;

  -- Yearly Data
  select json_agg(t) into yearly_data from (
    with years as (
        select date_trunc('year', y)::date as year from generate_series(from_date, to_date, '1 year'::interval) y
    ),
    yearly_revenue as (
        select
            date_trunc('year', o.created_at)::date as year,
            sum(case when oi.billing = 'one-time' then oi.final_price else 0 end) as one_time,
            sum(case when oi.billing != 'one-time' then oi.final_price else 0 end) as recurring
        from orders o join order_items oi on o.id = oi.order_id
        where o.status = 'Completed' and o.created_at between from_date and to_date
        group by 1
    ),
    yearly_subs as (
        select date_trunc('year', created_at)::date as year, count(*) as new_subs
        from subscriptions where created_at between from_date and to_date group by 1
    )
    select
        to_char(y.year, 'YYYY') as label,
        coalesce(yr.one_time, 0)::numeric as "oneTime",
        coalesce(yr.recurring, 0)::numeric as "recurring",
        coalesce(ys.new_subs, 0)::integer as "subscriptions",
        -- Projection for the current year
        case
            when date_trunc('year', now())::date = y.year and extract(doy from now()) < 365
            then round(
                (coalesce(yr.one_time, 0) + coalesce(yr.recurring, 0)) / 
                extract(doy from now()) * 365
            )
            else null
        end::numeric as projection
    from years y
    left join yearly_revenue yr on y.year = yr.year
    left join yearly_subs ys on y.year = ys.year
    order by y.year
  ) t;

  -- Assemble metrics into a single JSON object with camelCase keys
  select json_build_object(
    'totalRevenue', total_revenue,
    'mrr', round(mrr),
    'totalClients', total_clients,
    'activeSubscriptions', active_subscriptions,
    'projectedRevenue', round(coalesce(projected_revenue, 0)),
    'daily', coalesce(daily_data, '[]'::json),
    'monthly', coalesce(monthly_data, '[]'::json),
    'yearly', coalesce(yearly_data, '[]'::json)
  ) into metrics;

  return metrics;
end;
$$;