import React from 'react';
import { Link } from 'react-router-dom';
import { useAuthStore } from '../stores/useAuthStore';
import { useCartStore } from '../stores/useCartStore';

const Navbar: React.FC = () => {
  const { session, user, isImpersonating, logout } = useAuthStore();
  const { cart, setIsCartOpen } = useCartStore();

  const handleLogout = async () => {
    await logout();
  };

  const openCart = () => setIsCartOpen(true);

  const isLoggedIn = !!session;
  const userType = user.type;
  const cartCount = cart.length;
  return (
    <header className="bg-white border-b border-gray-300">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-14">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="flex items-center gap-2 text-xl text-black">
              <img src="/animated-eyes.gif" alt="Animated Eyes Logo" className="w-8 h-8" />
              <span>
                <span className="font-bold">Voice AI</span>
                <span className="ml-1">Space <span className="font-bold">Marketplace</span></span>
              </span>
            </Link>
          </div>

          {/* Right side icons and buttons */}
          <div className="flex items-center gap-4">
            {isLoggedIn && !isImpersonating ? (
              <>
                {userType === 'admin' && (
                  <Link to="/admin" className="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-black">
                    <i className="fa-solid fa-shield-halved w-5 h-5"></i>
                    <span>Admin</span>
                  </Link>
                )}
                {userType === 'client' && (
                  <Link to="/profile" className="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-black">
                    <i className="fa-solid fa-user w-5 h-5"></i>
                    <span>Profile</span>
                  </Link>
                )}
                <button onClick={handleLogout} className="text-sm font-medium text-gray-600 hover:text-black">
                  Logout
                </button>
              </>
            ) : (
              !isImpersonating && (
                <div className="flex items-center gap-2">
                    <Link to="/login" className="text-sm font-medium text-gray-600 hover:text-black px-3 py-2">
                        Sign In
                    </Link>
                    <Link to="/signup" className="inline-flex items-center justify-center whitespace-nowrap transition-colors focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 bg-black text-white hover:bg-gray-800 px-4 py-2 text-sm font-semibold">
                        Sign Up
                    </Link>
                </div>
              )
            )}

            {!isImpersonating && <div className="h-6 border-l border-gray-200" />}

            <button onClick={openCart} className="relative p-2 text-gray-600 hover:text-black hover:bg-gray-50">
              <i className="fa-solid fa-cart-shopping text-xl"></i>
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 block h-5 w-5 text-[10px] bg-red-600 text-white flex items-center justify-center">
                  {cartCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;