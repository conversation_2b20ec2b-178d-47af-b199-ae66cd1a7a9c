import React, { useState, useRef, useEffect, useMemo } from 'react';
import type { Conversation } from '@/types';
import { <PERSON><PERSON>, FormField } from '@/components/ui';

interface ClientMessagingTabProps {
  conversations: Conversation[];
  handleSendMessage: (conversationId: string, content: string) => void;
  handleStartNewConversation: (subject: string, initialMessage: string) => void;
  handleMarkConversationAsRead: (conversationId: string) => void;
}

interface NewConversationModalProps {
    onClose: () => void;
    onSubmit: (subject: string, message: string) => void;
}

const timeSince = (date: Date): string => {
    const seconds = Math.floor((new Date().getTime() - new Date(date).getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + " years ago";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + " months ago";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + " days ago";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + " hours ago";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + " minutes ago";
    return Math.floor(seconds) + " seconds ago";
}

const NewConversationModal: React.FC<NewConversationModalProps> = ({ onClose, onSubmit }) => {
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');

    const handleSubmit = () => {
        if (!subject.trim() || !message.trim()) {
            setError('Subject and message are required.');
            return;
        }
        onSubmit(subject, message);
    };

    return (
        <>
            <div onClick={onClose} className="fixed inset-0 bg-black bg-opacity-60 z-40" />
            <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-lg bg-white border border-gray-200 shadow-2xl z-50">
                <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-xl font-bold text-black">Start a New Conversation</h3>
                        <Button variant="ghost" size="sm" onClick={onClose}><i className="fa-solid fa-xmark w-5 h-5"></i></Button>
                    </div>
                    <div className="space-y-4">
                        <FormField label="Subject" name="subject" value={subject} onChange={(e) => setSubject(e.target.value)} />
                        <FormField label="Message" name="message" as="textarea" value={message} onChange={(e) => setMessage(e.target.value)} />
                        {error && <p className="text-red-600 text-sm">{error}</p>}
                        <div className="flex gap-3 pt-4 border-t border-gray-200">
                            <Button onClick={handleSubmit}>Send Message</Button>
                            <Button onClick={onClose} variant="secondary">Cancel</Button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

const ClientMessagingTab: React.FC<ClientMessagingTabProps> = ({ conversations, handleSendMessage, handleStartNewConversation, handleMarkConversationAsRead }) => {
    const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
    const [isNewConvoModalOpen, setIsNewConvoModalOpen] = useState(false);
    const [reply, setReply] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const prevConvoCountRef = useRef(conversations.length);

    const sortedConversations = useMemo(() => 
        [...conversations].sort((a,b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()),
        [conversations]
    );

    useEffect(() => {
        const hasNewConvo = sortedConversations.length > prevConvoCountRef.current;

        if (hasNewConvo && sortedConversations.length > 0) {
            setActiveConversation(sortedConversations[0]);
        } else if (activeConversation) {
            const updatedConvo = sortedConversations.find(c => c.id === activeConversation.id);
            const nextConvo = updatedConvo || sortedConversations[0] || null;
            if (JSON.stringify(nextConvo) !== JSON.stringify(activeConversation)) {
                setActiveConversation(nextConvo);
            }
        } else if (sortedConversations.length > 0) {
            setActiveConversation(sortedConversations[0]);
        }

        prevConvoCountRef.current = sortedConversations.length;
    }, [sortedConversations, activeConversation]);


    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [activeConversation]);

    const handleSelectConversation = (convo: Conversation) => {
        setActiveConversation(convo);
        if (convo.clientHasUnread) {
            handleMarkConversationAsRead(convo.id);
        }
    };
    
    const handleSendReply = (e: React.FormEvent) => {
        e.preventDefault();
        if (!reply.trim() || !activeConversation) return;
        handleSendMessage(activeConversation.id, reply);
        setReply('');
    };

    const handleNewConvoSubmit = (subject: string, message: string) => {
        handleStartNewConversation(subject, message);
        setIsNewConvoModalOpen(false);
    };

    return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-350px)]">
            <div className="lg:col-span-1 bg-white border border-gray-300 flex flex-col">
                <div className="p-4 border-b border-gray-200">
                    <Button onClick={() => setIsNewConvoModalOpen(true)} className="w-full flex items-center justify-center gap-2">
                        <i className="fa-solid fa-plus"></i> New Conversation
                    </Button>
                </div>
                <div className="flex-1 overflow-y-auto">
                    {sortedConversations.length > 0 ? sortedConversations.map(convo => (
                        <button 
                            key={convo.id} 
                            onClick={() => handleSelectConversation(convo)}
                            className={`w-full text-left border-b border-gray-200 p-4 transition-colors ${convo.clientHasUnread ? 'border-l-4 border-black' : 'border-l-4 border-transparent'} ${activeConversation?.id === convo.id ? 'bg-gray-100' : 'hover:bg-gray-50'}`}
                        >
                            <div className="flex justify-between items-start">
                                <h4 className={`font-semibold ${convo.clientHasUnread ? 'text-black' : 'text-gray-800'}`}>{convo.subject}</h4>
                                <span className="text-xs text-gray-500 flex-shrink-0 ml-2">{timeSince(new Date(convo.lastUpdated))}</span>
                            </div>
                            <p className="text-sm text-gray-500 truncate mt-1">
                                {convo.messages[convo.messages.length - 1]?.content}
                            </p>
                        </button>
                    )) : (
                        <div className="flex items-center justify-center h-full text-center p-4">
                            <p className="text-gray-500">No conversations yet. <br/> Start a new one to get in touch!</p>
                        </div>
                    )}
                </div>
            </div>

            <div className="lg:col-span-2 bg-white border border-gray-300 flex flex-col">
                {activeConversation ? (
                    <>
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="text-lg font-bold text-black">{activeConversation.subject}</h3>
                            <p className="text-sm text-gray-600">Status: <span className="font-medium text-black capitalize">{activeConversation.status}</span></p>
                        </div>
                        <div className="flex-1 p-6 overflow-y-auto bg-gray-50 space-y-4">
                            {activeConversation.messages.map(msg => (
                                <div key={msg.id} className={`flex ${msg.sender === 'client' ? 'justify-end' : 'justify-start'}`}>
                                    <div className={`p-3 max-w-lg ${msg.sender === 'client' ? 'bg-black text-white' : 'bg-white border border-gray-200'}`}>
                                        <p className="text-sm text-black">{msg.content}</p>
                                        <p className={`text-xs mt-2 ${msg.sender === 'client' ? 'text-gray-300' : 'text-gray-500'}`}>
                                            {msg.sender === 'client' ? 'You' : 'Admin'} • {new Date(msg.createdAt).toLocaleString()}
                                        </p>
                                    </div>
                                </div>
                            ))}
                           <div ref={messagesEndRef} />
                        </div>
                        <div className="border-t border-gray-200 p-4 bg-white">
                            <form onSubmit={handleSendReply} className="flex gap-3">
                                <input
                                    type="text"
                                    value={reply}
                                    onChange={(e) => setReply(e.target.value)}
                                    placeholder="Type your message..."
                                    className="flex-1 px-3 py-2 border border-gray-300 text-sm bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                                />
                                <Button type="submit" className="flex items-center gap-2"><i className="fa-solid fa-paper-plane"></i> Send</Button>
                            </form>
                        </div>
                    </>
                ) : (
                    <div className="flex items-center justify-center h-full">
                        <p className="text-gray-500">Select a conversation to view.</p>
                    </div>
                )}
            </div>
            {isNewConvoModalOpen && <NewConversationModal onClose={() => setIsNewConvoModalOpen(false)} onSubmit={handleNewConvoSubmit} />}
        </div>
    );
};

export default ClientMessagingTab;