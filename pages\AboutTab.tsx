import React, { useState, useEffect } from 'react';
import { useServiceCategories } from '@/hooks/useServiceCategories';
import { useAdminActions } from '@/hooks/useAdminActions';
import type { ServiceCategoryItem } from '@/types';
import FormField from '@/components/ui/FormField';
import { useQueryClient } from '@tanstack/react-query';
import SkeletonCard from '@/components/skeletons/SkeletonCard';

// --- ServiceCategoryCard Component ---
interface ServiceCategoryCardProps {
  category: ServiceCategoryItem;
  onClick?: () => void;
  isAdmin?: boolean;
  onSave: (data: ServiceCategoryItem) => void;
  onDelete: (id: string) => void;
  onDragStart: () => void;
  onDragEnd: () => void;
  onDragEnter: () => void;
  onDrop: () => void;
  isDragging: boolean;
  isDropTarget: boolean;
}

const ServiceCategoryCard: React.FC<ServiceCategoryCardProps> = ({ 
    category, 
    onClick, 
    isAdmin, 
    onSave, 
    onDelete, 
    onDragStart, 
    onDrop, 
    onDragEnd, 
    onDragEnter,
    isDragging,
    isDropTarget
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<ServiceCategoryItem>(category);

  useEffect(() => {
    // If the card is a new placeholder, open it in edit mode immediately
    if (category.id === 'new-category-placeholder') {
      setIsEditing(true);
      setFormData(category);
    }
  }, [category]);
  
  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    if (category.id === 'new-category-placeholder') {
        onDelete('new-category-placeholder'); // Special key to remove placeholder
    } else {
        setFormData(category);
        setIsEditing(false);
    }
  };
  
  const handleItemChange = (index: number, value: string) => {
    const newItems = [...formData.items];
    newItems[index] = value;
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const addItem = () => setFormData(prev => ({ ...prev, items: [...prev.items, ''] }));
  const removeItem = (index: number) => setFormData(prev => ({ ...prev, items: prev.items.filter((_, i) => i !== index) }));

  if (isEditing) {
    return (
        <div className="bg-white border-2 border-blue-500 p-6 flex flex-col h-full ring-2 ring-blue-200">
            <FormField label="ID (unique)" name="id" value={formData.id} onChange={e => setFormData(p => ({...p, id: e.target.value}))} disabled={category.id !== 'new-category-placeholder'}/>
            <FormField label="Title" name="title" value={formData.title} onChange={e => setFormData(p => ({...p, title: e.target.value}))} />
            <FormField label="Description" name="description" as="textarea" value={formData.description || ''} onChange={e => setFormData(p => ({...p, description: e.target.value}))} />
            <div className="space-y-2 mt-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">List Items</label>
                {formData.items.map((item, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <input type="text" value={item} onChange={e => handleItemChange(index, e.target.value)} className="w-full px-2 py-1 border border-gray-300 text-sm bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent" placeholder="Feature description..."/>
                    <button onClick={() => removeItem(index)} className="text-red-500 hover:bg-red-50 p-1" title="Remove Item"><i className="fa-solid fa-times"></i></button>
                  </div>
                ))}
                <button onClick={addItem} className="text-sm text-blue-600 hover:underline">+ Add Item</button>
            </div>
             <FormField label="Link to Tab (optional)" name="linkToTab" value={formData.linkToTab || ''} onChange={e => setFormData(p => ({...p, linkToTab: e.target.value}))} placeholder="e.g., media"/>
             <div className="flex gap-2 mt-auto pt-4">
                <button onClick={handleSave} className="px-3 py-1 bg-green-600 text-white text-sm font-medium hover:bg-green-700">Save</button>
                <button onClick={handleCancel} className="px-3 py-1 bg-gray-200 text-sm font-medium hover:bg-gray-300">Cancel</button>
                <button onClick={() => onDelete(formData.id)} className="px-3 py-1 bg-red-600 text-white text-sm font-medium hover:bg-red-700 ml-auto">Delete</button>
             </div>
        </div>
    );
  }

  return (
    <div 
      className={`bg-white border border-gray-300 p-6 relative group h-full flex flex-col transition-all duration-300
        ${onClick ? 'cursor-pointer hover:border-black' : ''}
        ${isDragging ? 'opacity-40 scale-105 shadow-lg -rotate-2' : 'opacity-100 scale-100 shadow-sm'}
        ${isDropTarget ? 'ring-2 ring-black ring-offset-2' : ''}
      `}
      onClick={onClick}
      draggable={isAdmin}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onDragOver={(e) => e.preventDefault()}
      onDragEnter={onDragEnter}
      onDrop={onDrop}
    >
      {isAdmin && (
        <>
            <div className="absolute top-1 left-1 text-gray-300 cursor-grab p-2 opacity-0 group-hover:opacity-100 transition-opacity" title="Drag to reorder"><i className="fa-solid fa-grip-vertical"></i></div>
            <button onClick={(e) => { e.stopPropagation(); setIsEditing(true); }} className="absolute top-1 right-1 text-gray-300 hover:text-black p-2 opacity-0 group-hover:opacity-100 transition-opacity" title="Edit Card"><i className="fa-solid fa-pencil"></i></button>
        </>
      )}
      <h3 className="text-lg font-semibold mb-2 text-black">{category.title}</h3>
      <p className="text-sm text-gray-600 mb-4">{category.description}</p>
      <ul className="space-y-1 text-sm text-gray-700 flex-grow">
        {category.items.map((item, index) => <li key={index} className="flex items-start"><span className="mr-2 text-black">&bull;</span><span dangerouslySetInnerHTML={{ __html: item }} /></li>)}
      </ul>
      {onClick && (
        <div className="mt-4 text-sm text-black font-medium">
          Click to explore →
        </div>
      )}
    </div>
  );
};


// --- Main AboutTab Component ---
interface AboutTabProps {
  setActiveTab?: (tab: string) => void;
  isAdmin?: boolean;
}

const AboutTab: React.FC<AboutTabProps> = ({ setActiveTab, isAdmin }) => {
  const { data: serviceCategories = [], isLoading, isError, error } = useServiceCategories();
  const [orderedCategories, setOrderedCategories] = useState<ServiceCategoryItem[]>([]);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null);
  
  const adminActions = useAdminActions();
  const queryClient = useQueryClient();

  useEffect(() => {
    const sorted = [...serviceCategories].sort((a, b) => (a.orderIndex ?? 0) - (b.orderIndex ?? 0));
    setOrderedCategories(sorted);
  }, [serviceCategories]);

  const handleAddNewCard = () => {
    const newCard: ServiceCategoryItem = {
      id: 'new-category-placeholder',
      title: 'New Service Card',
      description: 'A brief description of this service category.',
      items: ['Feature one', 'Feature two'],
      orderIndex: orderedCategories.length,
      isEnabled: true,
    };
    setOrderedCategories(prev => [...prev, newCard]);
  };
  
  const handleSaveCard = async (data: ServiceCategoryItem) => {
    try {
        if(data.id === 'new-category-placeholder') {
          const { id: _, ...createData } = data;
          await adminActions.createRecord('service_categories', createData);
        } else {
            const { id, ...updateData } = data;
            await adminActions.updateRecord('service_categories', updateData, id);
        }
        await queryClient.invalidateQueries({ queryKey: ['service-categories'] });
    } catch (error) {
        console.error("Failed to save card:", error);
        const message = error instanceof Error ? error.message : String(error);
        alert(`Error saving changes: ${message}`);
    }
  };

  const handleDeleteCard = async (id: string) => {
    if (id === 'new-category-placeholder') {
        setOrderedCategories(prev => prev.filter(c => c.id !== id));
        return;
    }
    if (window.confirm("Are you sure you want to delete this card?")) {
        try {
            await adminActions.deleteRecord('service_categories', id);
            await queryClient.invalidateQueries({ queryKey: ['service-categories'] });
        } catch (error) {
            console.error("Failed to delete card:", error);
            const message = error instanceof Error ? error.message : String(error);
            alert(`Error deleting card: ${message}`);
        }
    }
  };

  const handleDragStart = (index: number) => {
    if (!isAdmin) return;
    setDraggedIndex(index);
  };
  
  const handleDrop = async (dropIndex: number) => {
    if (!isAdmin || draggedIndex === null || draggedIndex === dropIndex) return;
    
    const newCategories = [...orderedCategories];
    const [draggedItem] = newCategories.splice(draggedIndex, 1);
    newCategories.splice(dropIndex, 0, draggedItem);

    setOrderedCategories(newCategories);

    try {
        const updatePromises = newCategories.map((cat, index) => 
            adminActions.updateRecord('service_categories', { orderIndex: index }, cat.id)
        );
        await Promise.all(updatePromises);
        await queryClient.invalidateQueries({ queryKey: ['service-categories'] });
    } catch (error) {
        console.error("Failed to save new order:", error);
        const message = error instanceof Error ? error.message : String(error);
        alert(`Failed to save new order: ${message}`);
        // Revert to original order on failure
        const sorted = [...serviceCategories].sort((a, b) => (a.orderIndex ?? 0) - (b.orderIndex ?? 0));
        setOrderedCategories(sorted);
    } finally {
      handleDragEnd();
    }
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDropTargetIndex(null);
  }

  const handleDragEnter = (index: number) => {
    if (draggedIndex !== null && draggedIndex !== index) {
      setDropTargetIndex(index);
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-12">
      {/* How to Get Started Section */}
      <div className="bg-white border border-gray-300 p-8">
        <h3 className="text-2xl font-semibold mb-6 text-center text-black">How to Get Started</h3>
        <div className="grid gap-8 grid-cols-1 sm:grid-cols-3">
          <div className="text-center p-4">
            <div className="w-12 h-12 bg-black text-white flex items-center justify-center mx-auto mb-4 text-lg font-bold">1</div>
            <p className="font-semibold text-black">Browse Services</p>
            <p className="text-sm text-gray-600">Explore our categories to find the best fit.</p>
          </div>
          <div className="text-center p-4 relative">
            <div className="w-12 h-12 bg-black text-white flex items-center justify-center mx-auto mb-4 text-lg font-bold">2</div>
            <p className="font-semibold text-black">Add to Cart</p>
            <p className="text-sm text-gray-600 mb-6">Select your preferred packages and billing options.</p>
          </div>
          <div className="text-center p-4">
            <div className="w-12 h-12 bg-black text-white flex items-center justify-center mx-auto mb-4 text-lg font-bold">3</div>
            <p className="font-semibold text-black">Checkout & Grow</p>
            <p className="text-sm text-gray-600">Complete your purchase and let's get started.</p>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => <SkeletonCard key={i} />)
        ) : isError ? (
          <div className="md:col-span-2 bg-red-50 border border-red-300 text-red-800 p-6">
            <h4 className="font-bold">Error Loading Service Categories</h4>
            <p className="text-sm mt-1">There was a problem fetching data. Please check your configuration or try again later.</p>
            <pre className="text-xs mt-2 bg-red-100 p-2 overflow-auto">{error instanceof Error ? error.message : JSON.stringify(error)}</pre>
          </div>
        ) : (
          orderedCategories.map((category, index) => (
            <ServiceCategoryCard
              key={category.id}
              category={category}
              onClick={category.linkToTab ? () => setActiveTab?.(category.linkToTab!) : undefined}
              isAdmin={isAdmin}
              onSave={handleSaveCard}
              onDelete={handleDeleteCard}
              onDragStart={() => handleDragStart(index)}
              onDrop={() => handleDrop(index)}
              onDragEnd={handleDragEnd}
              onDragEnter={() => handleDragEnter(index)}
              isDragging={draggedIndex === index}
              isDropTarget={dropTargetIndex === index}
            />
          ))
        )}
        
        {isAdmin && !isLoading && !isError && (
            <button 
                onClick={handleAddNewCard}
                className="border-2 border-dashed border-gray-300 bg-gray-50 text-gray-500 hover:border-black hover:text-black transition-colors duration-300 flex flex-col items-center justify-center min-h-[200px] p-6">
                <i className="fa-solid fa-plus text-3xl mb-2"></i>
                <span className="font-semibold">Add New Service Card</span>
            </button>
        )}
      </div>
    </div>
  );
};

export default AboutTab;