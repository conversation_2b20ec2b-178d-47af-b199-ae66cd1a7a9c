import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabaseAdmin = getSupabaseAdminClient();
        
        // 1. Check for admin privileges
        const { data: { user } } = await supabaseAdmin.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
        
        // Use a role check instead of hardcoded email
        const { data: clientProfile, error: profileError } = await supabaseAdmin
            .from('clients')
            .select('role')
            .eq('user_id', user.id)
            .single();

        if (profileError || clientProfile?.role !== 'admin') {
             throw new Error("Unauthorized: Only admins can archive products.");
        }

        // 2. Get packageId from request and validate
        const { packageId } = await req.json();
        if (!packageId) {
            return new Response(
                JSON.stringify({ error: "packageId is required." }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }

        // 3. Find the Stripe product ID from our mapping table
        const { data: mapping, error: mappingError } = await supabaseAdmin
            .from('stripe_products')
            .select('stripe_product_id')
            .eq('package_id', packageId)
            .single();

        if (mappingError) {
            // If the mapping doesn't exist, it's not an error. There's nothing to archive.
            if (mappingError.code === 'PGRST116') { // 'PGRST116' is "JSON object requested, multiple (or no) rows returned"
                console.log(`No Stripe product mapping found for package ${packageId}. Nothing to archive.`);
                 return new Response(JSON.stringify({ success: true, message: 'No Stripe product to archive.' }), {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                });
            }
            throw mappingError;
        }

        const stripeProductId = mapping.stripe_product_id;
        if (!stripeProductId) {
             return new Response(JSON.stringify({ success: true, message: 'No Stripe product ID found in mapping.' }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            });
        }
        
        // 4. Archive the product on Stripe
        const stripe = await getStripeInstance();
        await stripe.products.update(stripeProductId, { active: false });

        console.log(`Successfully archived Stripe product ${stripeProductId} for package ${packageId}.`);

        // 5. Optionally, we could also delete the mapping row here, but let's keep it for historical purposes.

        return new Response(
            JSON.stringify({ success: true, message: `Stripe product ${stripeProductId} archived.` }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error archiving Stripe product:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
});