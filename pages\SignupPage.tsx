import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import type { SignupData } from '@/types';
import { useAuthStore } from '@/stores/useAuthStore';
import { <PERSON><PERSON>, FormField } from '@/components/ui';
import PasswordStrengthMeter from '@/components/PasswordStrengthMeter';

const calculatePasswordStrength = (password: string): number => {
    if (!password) return 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[^A-Za-z0-9]/.test(password),
    };
    if (!checks.length) return 1;
    let conditionsMet = Object.values(checks).filter(Boolean).length - 1;
    if (password.length < 8) return 1;
    if (conditionsMet <= 1) return 1;
    if (conditionsMet === 2) return 2;
    if (conditionsMet === 3) return 3;
    if (conditionsMet >= 4) return 4;
    return 0;
};


const SignupPage: React.FC = () => {
  const navigate = useNavigate();
  const { signup } = useAuthStore();
  
  const [accountDetails, setAccountDetails] = useState<SignupData>({
    companyName: '',
    industry: '',
    companySize: '1-10 employees',
    website: '',
    description: '',
    primaryContact: '',
    title: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    billingEmail: '',
    billingContact: '',
    billingPhone: '',
  });
  const [password, setPassword] = useState('');
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [confirmPassword, setConfirmPassword] = useState('');
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    const requiredFields: (keyof SignupData & string)[] = [
        'companyName', 'primaryContact', 'email', 'phone', 
        'address', 'city', 'state', 'zipCode', 'country', 'billingEmail'
    ];
    
    requiredFields.forEach(field => {
        const value = accountDetails[field];
        if (typeof value !== 'string' || !value.trim()) {
            errors[field] = 'This field is required.';
        }
    });

    if (accountDetails.email && !/\S+@\S+\.\S+/.test(accountDetails.email)) {
      errors.email = 'Email is invalid.';
    }
    if (accountDetails.billingEmail && !/\S+@\S+\.\S+/.test(accountDetails.billingEmail)) {
        errors.billingEmail = 'Billing email is invalid.';
    }
    if (!password) {
        errors.password = 'Password is required.';
    } else if (passwordStrength < 4) {
        errors.password = 'Password must be strong. Include uppercase, lowercase, numbers, and special characters.';
    }
    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match.';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAccountDetailsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setAccountDetails(prev => ({ ...prev, [name]: value }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setPasswordStrength(calculatePasswordStrength(newPassword));
  };

  const onSignupSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm()) {
      const success = await signup(accountDetails, password);
      if(success) {
        navigate('/verify-email');
      } else {
        setFormErrors({ form: 'An error occurred during signup. The email may already be in use.' });
      }
    }
  };

  return (
    <div className="flex items-center justify-center">
      <div className="max-w-4xl w-full mx-auto">
        <div className="bg-white p-8 border border-gray-300">
          <h2 className="text-3xl font-bold text-center mb-1 text-black">Create Your Company Account</h2>
          <p className="text-center text-gray-600 mb-8">Get started with our services by setting up your profile.</p>
          
          <form onSubmit={onSignupSubmit} className="space-y-8">
             {formErrors.form && <p className="text-red-600 text-sm text-center">{formErrors.form}</p>}
            {/* Account Credentials */}
            <fieldset className="space-y-4">
              <legend className="text-lg font-semibold text-black border-b border-gray-200 pb-2 mb-4 w-full">Account Credentials</legend>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                 <FormField label="Email address" name="email" value={accountDetails.email || ''} onChange={handleAccountDetailsChange} error={formErrors.email} type="email" />
                 <div />
                 <div>
                    <FormField label="Password" name="password" type="password" value={password} onChange={handlePasswordChange} error={formErrors.password} />
                    <PasswordStrengthMeter score={passwordStrength} />
                    {password.length > 0 && passwordStrength < 4 && (
                      <p className="text-xs text-gray-500 mt-1">
                          Password must contain uppercase, lowercase, numbers, and special characters.
                      </p>
                    )}
                 </div>
                 <FormField label="Confirm Password" name="confirmPassword" type="password" value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} error={formErrors.confirmPassword} />
              </div>
            </fieldset>

            {/* Company Information */}
            <fieldset className="space-y-4">
              <legend className="text-lg font-semibold text-black border-b border-gray-200 pb-2 mb-4 w-full">Company Information</legend>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField label="Company Name" name="companyName" value={accountDetails.companyName || ''} onChange={handleAccountDetailsChange} error={formErrors.companyName} />
                <FormField label="Industry" name="industry" value={accountDetails.industry || ''} onChange={handleAccountDetailsChange} error={formErrors.industry} required={false} />
                <FormField label="Company Size" name="companySize" value={accountDetails.companySize || ''} onChange={handleAccountDetailsChange} as="select" options={['1-10 employees', '11-50 employees', '50-100 employees', '101-500 employees', '500+ employees']} required={false} />
                <FormField label="Website" name="website" value={accountDetails.website || ''} onChange={handleAccountDetailsChange} type="url" required={false} />
                <div className="md:col-span-2">
                    <FormField label="Company Description" name="description" value={accountDetails.description || ''} onChange={handleAccountDetailsChange} as="textarea" required={false} />
                </div>
              </div>
            </fieldset>

            {/* Primary Contact */}
            <fieldset className="space-y-4">
              <legend className="text-lg font-semibold text-black border-b border-gray-200 pb-2 mb-4 w-full">Primary Contact (this will be you)</legend>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField label="Full Name" name="primaryContact" value={accountDetails.primaryContact || ''} onChange={handleAccountDetailsChange} error={formErrors.primaryContact} />
                <FormField label="Title" name="title" value={accountDetails.title || ''} onChange={handleAccountDetailsChange} required={false} />
                <FormField label="Phone Number" name="phone" value={accountDetails.phone || ''} onChange={handleAccountDetailsChange} error={formErrors.phone} type="tel" />
              </div>
            </fieldset>

            {/* Company Address */}
            <fieldset className="space-y-4">
              <legend className="text-lg font-semibold text-black border-b border-gray-200 pb-2 mb-4 w-full">Company Address</legend>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2"><FormField label="Street Address" name="address" value={accountDetails.address || ''} onChange={handleAccountDetailsChange} error={formErrors.address} /></div>
                <FormField label="City" name="city" value={accountDetails.city || ''} onChange={handleAccountDetailsChange} error={formErrors.city} />
                <FormField label="State/Province" name="state" value={accountDetails.state || ''} onChange={handleAccountDetailsChange} error={formErrors.state} />
                <FormField label="ZIP/Postal Code" name="zipCode" value={accountDetails.zipCode || ''} onChange={handleAccountDetailsChange} error={formErrors.zipCode} />
                <FormField label="Country" name="country" value={accountDetails.country || ''} onChange={handleAccountDetailsChange} error={formErrors.country} as="select" options={['United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Australia', 'Other']} />
              </div>
            </fieldset>

             {/* Billing Information */}
            <fieldset className="space-y-4">
              <legend className="text-lg font-semibold text-black border-b border-gray-200 pb-2 mb-4 w-full">Billing Information</legend>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField label="Billing Email" name="billingEmail" value={accountDetails.billingEmail || ''} onChange={handleAccountDetailsChange} error={formErrors.billingEmail} type="email" />
                <FormField label="Billing Contact Name" name="billingContact" value={accountDetails.billingContact || ''} onChange={handleAccountDetailsChange} required={false} />
                <FormField label="Billing Phone" name="billingPhone" value={accountDetails.billingPhone || ''} onChange={handleAccountDetailsChange} type="tel" required={false} />
              </div>
            </fieldset>

            <div>
              <Button
                type="submit"
                size="lg"
                className="w-full"
              >
                Create Account & Proceed to Verification
              </Button>
            </div>
          </form>

          <p className="mt-8 text-center text-sm text-gray-600">
            Already have an account?{' '}
            <Link to="/login" className="font-medium text-black hover:underline">
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SignupPage;