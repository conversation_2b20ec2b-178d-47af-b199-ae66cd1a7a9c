import React, { useState, useRef, useEffect } from 'react';
import type { Conversation, Client } from '../../types';
import { useAdminActions } from '../../hooks';
import { Button } from '../../components/ui';

const timeSince = (date: Date): string => {
    const seconds = Math.floor((new Date().getTime() - new Date(date).getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + " years ago";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + " months ago";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + " days ago";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + " hours ago";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + " minutes ago";
    return Math.floor(seconds) + " seconds ago";
}

interface MessagingTabProps {
    clients: Client[];
    allConversations: (Conversation & { clientName: string })[];
    activeConversation: (Conversation & { clientName: string; }) | null;
    setActiveConversation: (conversation: (Conversation & { clientName: string; }) | null) => void;
}

const MessagingTab: React.FC<MessagingTabProps> = ({ clients, allConversations, activeConversation, setActiveConversation }) => {
    const [adminReply, setAdminReply] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const adminActions = useAdminActions();

    useEffect(() => {
        if (activeConversation) {
            const updatedActiveConvo = allConversations.find(c => c.id === activeConversation.id);
            const nextConvo = updatedActiveConvo || allConversations[0] || null;
            if (JSON.stringify(nextConvo) !== JSON.stringify(activeConversation)) {
                setActiveConversation(nextConvo);
            }
        } else if (allConversations.length > 0) {
            setActiveConversation(allConversations[0]);
        }
    }, [allConversations, activeConversation, setActiveConversation]);
    
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [activeConversation]);

    const handleSelectConversation = (convo: Conversation & { clientName: string }) => {
        setActiveConversation(convo);
        // FIX: Property 'admin_has_unread' does not exist on type 'Conversation'. Did you mean 'adminHasUnread'?
        if (convo.adminHasUnread) {
            // FIX: Property 'client_id' does not exist on type 'Conversation'. Did you mean 'clientId'?
            adminActions.markConversationAsRead(convo.clientId, convo.id);
        }
    };

    return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-350px)]">
            <div className="lg:col-span-1 bg-white border border-gray-300 flex flex-col">
                <div className="p-4 border-b border-gray-200"><h3 className="text-lg font-semibold text-black">Client Conversations</h3></div>
                <div className="flex-1 overflow-y-auto">
                    {allConversations.map(convo => (
                        // FIX: Property 'admin_has_unread' does not exist on type 'Conversation'. Did you mean 'adminHasUnread'?
                        <button key={convo.id} onClick={() => handleSelectConversation(convo)} className={`w-full text-left border-b border-gray-200 p-4 transition-colors ${convo.adminHasUnread ? 'border-l-4 border-black' : 'border-l-4 border-transparent'} ${activeConversation?.id === convo.id ? 'bg-gray-100' : 'hover:bg-gray-50'}`}>
                            <div className="flex justify-between items-start">
                                {/* FIX: Property 'admin_has_unread' does not exist on type 'Conversation'. Did you mean 'adminHasUnread'? */}
                                <h4 className={`font-semibold ${convo.adminHasUnread ? 'text-black' : 'text-gray-800'}`}>{convo.clientName}</h4>
                                {/* FIX: Property 'last_updated' does not exist on type 'Conversation'. Did you mean 'lastUpdated'? */}
                                <span className="text-xs text-gray-500 flex-shrink-0 ml-2">{timeSince(new Date(convo.lastUpdated))}</span>
                            </div>
                            <p className="text-sm text-gray-500 truncate mt-1">{convo.subject}</p>
                        </button>
                    ))}
                </div>
            </div>
            <div className="lg:col-span-2 bg-white border border-gray-300 flex flex-col">
                {activeConversation ? (
                    <>
                        <div className="p-4 border-b border-gray-200"><h3 className="text-lg font-bold text-black">{activeConversation.subject}</h3></div>
                        <div className="flex-1 p-6 overflow-y-auto bg-gray-50 space-y-4">
                            {activeConversation.messages.map(msg => (
                               <div key={msg.id} className={`flex ${msg.sender === 'admin' ? 'justify-end' : 'justify-start'}`}>
                                    <div className={`p-3 max-w-lg ${msg.sender === 'admin' ? 'bg-black text-white' : 'bg-white border border-gray-200'}`}>
                                        <p className="text-sm text-black">{msg.content}</p>
                                        <p className={`text-xs mt-2 ${msg.sender === 'admin' ? 'text-gray-300' : 'text-gray-500'}`}>
                                            {/* FIX: Property 'created_at' does not exist on type 'Message'. Did you mean 'createdAt'? */}
                                            {msg.sender === 'admin' ? 'You' : 'Client'} • {new Date(msg.createdAt).toLocaleString()}
                                        </p>
                                    </div>
                               </div>
                            ))}
                           <div ref={messagesEndRef} />
                        </div>
                        <div className="border-t border-gray-200 p-4 bg-white">
                            {/* FIX: Property 'client_id' does not exist on type 'Conversation'. Did you mean 'clientId'? */}
                            <form onSubmit={(e) => { e.preventDefault(); adminActions.sendMessage(activeConversation.clientId, activeConversation.id, adminReply); setAdminReply(''); }} className="flex gap-3">
                                <input type="text" value={adminReply} onChange={(e) => setAdminReply(e.target.value)} placeholder="Type your message..." className="flex-1 px-3 py-2 border border-gray-300 text-sm bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"/>
                                <Button type="submit">Send</Button>
                            </form>
                        </div>
                    </>
                ) : <div className="flex items-center justify-center h-full text-gray-500">Select a conversation.</div>}
            </div>
        </div>
    );
};

export default MessagingTab;
