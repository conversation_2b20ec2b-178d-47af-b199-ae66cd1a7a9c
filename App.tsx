import React from 'react';
import { Routes, Route } from 'react-router-dom';

// Import stores
import { useAuthInitialization } from '@/stores/useAuthStore';
import { useAnnouncementSettings } from '@/hooks/useSettings';


// Import all components and pages
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import CartDrawer from '@/components/CartDrawer';
import ImpersonationBanner from '@/components/ImpersonationBanner';
import ProtectedRoute from '@/components/ProtectedRoute';
import LandingPage from '@/pages/LandingPage';
import LoginPage from '@/pages/LoginPage';
import SignupPage from '@/pages/SignupPage';
import AdminPage from '@/pages/AdminPage';
import ProfilePage from '@/pages/ProfilePage';
import CartPage from '@/pages/CartPage';
import EmailVerificationPage from '@/pages/EmailVerificationPage';
import ContactPage from '@/pages/ContactPage';
import TermsPage from '@/pages/TermsPage';
import PrivacyPolicyPage from '@/pages/PrivacyPolicyPage';
import AnnouncementBanner from '@/components/AnnouncementBanner';
import PromoCodeHandler from '@/components/PromoCodeHandler';
import CompleteSetupPage from '@/pages/CompleteSetupPage';
import Toast from '@/components/Toast';
import OrderConfirmationPage from '@/pages/OrderConfirmationPage';

function App() {
  // Initialize auth state and listen for changes
  useAuthInitialization();
  const { data: announcementSettings } = useAnnouncementSettings();

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Toast />
      <PromoCodeHandler />
      <div className="sticky top-0 z-30">
        <AnnouncementBanner settings={announcementSettings} />
        <ImpersonationBanner />
        <Navbar />
      </div>
      <main className="flex-grow container mx-auto px-6 py-8">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path="/cart" element={<CartPage />} />
          <Route path="/complete-setup" element={<CompleteSetupPage />} />
          <Route path="/profile" element={
            <ProtectedRoute>
              <ProfilePage />
            </ProtectedRoute>
          } />
          <Route path="/order-confirmation" element={
            <ProtectedRoute>
              <OrderConfirmationPage />
            </ProtectedRoute>
          } />
          <Route path="/admin" element={
            <ProtectedRoute requireAdmin={true}>
              <AdminPage />
            </ProtectedRoute>
          } />
          <Route path="/verify-email" element={<EmailVerificationPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/terms" element={<TermsPage />} />
          <Route path="/privacy" element={<PrivacyPolicyPage />} />
        </Routes>
      </main>
      <Footer />
      <CartDrawer />
    </div>
  );
}

export default App;