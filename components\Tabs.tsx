import React from 'react';
import type { NavItem } from '@/types';

interface TabsProps {
  navItems: NavItem[];
  activeTab: string;
  setActiveTab: (tab: string) => void;
  isAdmin?: boolean;
  onDragStart?: (id: string) => void;
  onDrop?: (id: string) => void;
  onDragEnter?: (id: string) => void;
  onDragEnd?: () => void;
  draggedId?: string | null;
  dropTargetId?: string | null;
}

const Tabs: React.FC<TabsProps> = ({ 
  navItems, 
  activeTab, 
  setActiveTab, 
  isAdmin,
  onDragStart,
  onDrop,
  onDragEnter,
  onDragEnd,
  draggedId,
  dropTargetId
}) => {
  return (
    <div className="bg-white border-2 border-gray-400">
      <div className="px-4 sm:px-6">
        <div className="flex justify-center overflow-x-auto no-scrollbar" onDragEnd={onDragEnd}>
          {navItems.map((item) => (
            <React.Fragment key={item.id}>
                {isAdmin && item.isDraggable && dropTargetId === item.id && draggedId !== item.id && (
                    <div className="w-1 h-8 bg-black self-center transition-all" />
                )}
                <button
                  onClick={() => setActiveTab(item.id)}
                  draggable={isAdmin && item.isDraggable}
                  onDragStart={() => isAdmin && item.isDraggable && onDragStart?.(item.id)}
                  onDrop={() => isAdmin && item.isDraggable && onDrop?.(item.id)}
                  onDragEnter={() => isAdmin && item.isDraggable && onDragEnter?.(item.id)}
                  onDragOver={(e) => e.preventDefault()}
                  className={`px-6 py-3 text-base font-medium transition-all duration-200 whitespace-nowrap first:rounded-l-none last:rounded-r-none ${
                    activeTab === item.id
                      ? 'bg-gray-200 text-black'
                      : 'bg-white text-gray-700 hover:bg-gray-100 hover:text-black'
                  } ${isAdmin && item.isDraggable ? 'cursor-grab' : ''}
                  ${isAdmin && draggedId === item.id ? 'opacity-40 scale-105 shadow-lg -rotate-2' : ''}
                  `}
                >
                  <span className="sm:hidden">{item.mobileLabel}</span>
                  <span className="hidden sm:inline xl:hidden">{item.laptopLabel}</span>
                  <span className="hidden xl:inline">{item.label}</span>
                </button>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Tabs;