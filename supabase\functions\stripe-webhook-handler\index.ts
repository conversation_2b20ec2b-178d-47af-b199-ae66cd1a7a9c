// FIX: Declare <PERSON>o global to satisfy TypeScript compiler in this environment.
declare const Deno: any;

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@16.2.0';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';
import { Database } from '../../../types/database.ts';

const supabase = getSupabaseAdminClient();

// --- Event Handler Functions ---

async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    console.log(`Handling successful payment for PI: ${paymentIntent.id}`);
    
    if (paymentIntent.invoice) {
        console.log(`PI ${paymentIntent.id} is for an invoice. Skipping duplicate processing as invoice.payment_succeeded will handle it.`);
        return;
    }

    console.log("No invoice linked to PI. This is a one-time payment. A record-keeping invoice will be created.");

    // Find the order in our DB. There might be a slight delay from when the client creates it.
    let { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select('id')
        .eq('stripe_payment_intent_id', paymentIntent.id)
        .single();
    
    // Race condition handling
    if (orderError && orderError.code === 'PGRST116') {
        console.log(`Webhook Race Condition: Order for PI ${paymentIntent.id} not found. Retrying in 2s.`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        const { data: retryOrderData, error: retryOrderError } = await supabase
            .from('orders')
            .select('id')
            .eq('stripe_payment_intent_id', paymentIntent.id)
            .single();
        
        if (retryOrderError || !retryOrderData) {
            console.error(`Webhook Error on retry: Still could not find order for payment_intent ${paymentIntent.id}. Error: ${retryOrderError?.message}`);
            return;
        }
        orderData = retryOrderData;
    } else if (orderError) {
        console.error(`Webhook Error: Could not find order for payment_intent ${paymentIntent.id}. Error: ${orderError.message}`);
        return;
    }

    const orderId = orderData.id;
    const stripeCustomerId = paymentIntent.customer as string | null;

    if (!stripeCustomerId) {
        console.error(`Webhook Error: PaymentIntent ${paymentIntent.id} is missing a stripe_customer_id.`);
        return;
    }

    try {
        const stripe = await getStripeInstance();

        // Since the payment is already confirmed, we create a 'paid' invoice from the PaymentIntent.
        const invoice = await stripe.invoices.create({
            customer: stripeCustomerId,
            payment_intent: paymentIntent.id,
            description: `Invoice for Order #${orderId}`,
            metadata: { 
                order_id: orderId,
                payment_intent_id: paymentIntent.id 
            },
        });

        // Send the invoice to the customer
        await stripe.invoices.sendInvoice(invoice.id);
        
        console.log(`Successfully created and sent invoice ${invoice.id} for order ${orderId}`);

        // Update our order with the new invoice ID
        const { error: updateError } = await supabase
            .from('orders')
            .update({ stripe_invoice_id: invoice.id })
            .eq('id', orderId);

        if (updateError) {
            console.error(`Webhook Error: Failed to update order ${orderId} with invoice ID ${invoice.id}. Error: ${updateError.message}`);
        }
    } catch (err) {
        console.error(`Webhook Error: Failed to create or send invoice for PI ${paymentIntent.id}. Error: ${err.message}`);
    }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
    const stripeCustomerId = invoice.customer as string;
    if (!stripeCustomerId) {
        console.error(`Webhook Error: Invoice ${invoice.id} is missing a customer ID.`);
        return;
    }
    const { data: client, error: clientError } = await supabase.from('clients').select('id').eq('stripe_customer_id', stripeCustomerId).single();
    if (clientError || !client) {
        console.error(`Webhook Error: Could not find client for Stripe customer ${stripeCustomerId}. Error: ${clientError?.message}`);
        return;
    }
    const { error: invoiceUpsertError } = await supabase.from('invoices').upsert({
        stripe_invoice_id: invoice.id,
        stripe_subscription_id: invoice.subscription,
        client_id: client.id,
        amount_total: invoice.amount_paid,
        currency: invoice.currency,
        status: invoice.status,
        paid_at: invoice.status_transitions.paid_at ? new Date(invoice.status_transitions.paid_at * 1000).toISOString() : new Date().toISOString(),
        invoice_pdf: invoice.invoice_pdf,
        hosted_invoice_url: invoice.hosted_invoice_url,
    }, { onConflict: 'stripe_invoice_id' });
    if (invoiceUpsertError) console.error(`Webhook Error: Failed to upsert invoice ${invoice.id}. Error: ${invoiceUpsertError.message}`);
}

async function handleSubscriptionChange(subscription: Stripe.Subscription) {
    const { data: client, error: clientError } = await supabase.from('clients').select('id').eq('stripe_customer_id', subscription.customer).single();
    if (clientError) {
         console.error(`Webhook Error: Could not find client for Stripe customer ${subscription.customer}. Error: ${clientError.message}`);
         return;
    }
    const { error: subUpsertError } = await supabase.from('subscriptions').upsert({
        stripe_subscription_id: subscription.id,
        client_id: client.id,
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        cancel_at_period_end: subscription.cancel_at_period_end,
        canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
    }, { onConflict: 'stripe_subscription_id' });
    if (subUpsertError) console.error(`Webhook Error: Failed to upsert subscription ${subscription.id}. Error: ${subUpsertError.message}`);
}

async function handleChargeRefunded(charge: Stripe.Charge) {
    const paymentIntentId = charge.payment_intent as string;
    if (paymentIntentId) {
        const { error } = await supabase.from('orders').update({ status: 'Refunded' }).eq('stripe_payment_intent_id', paymentIntentId);
        if (error) console.error(`Webhook Error: Failed to update order status to Refunded for PI ${paymentIntentId}. Error: ${error.message}`);
    }
}

async function handleCustomerCreated(customer: Stripe.Customer) {
    console.log(`Handling customer.created for email: ${customer.email}`);
    if (customer.email) {
        const { data: client, error } = await supabase
            .from('clients')
            .select('id, stripe_customer_id')
            .eq('email', customer.email)
            .is('stripe_customer_id', null)
            .single();

        if (error && error.code !== 'PGRST116') {
            console.error(`Webhook Error: Error querying for client with email ${customer.email}.`, error.message);
        } else if (client) {
            console.log(`Found matching client profile (ID: ${client.id}) for new Stripe customer. Linking...`);
            const { error: updateError } = await supabase
                .from('clients')
                .update({ stripe_customer_id: customer.id })
                .eq('id', client.id);
            if (updateError) console.error(`Webhook Error: Failed to link Stripe customer ${customer.id} to client ${client.id}.`, updateError.message);
            else console.log(`Successfully linked Stripe customer ${customer.id} to client ${client.id}.`);
        } else {
            console.log(`No unlinked client profile found for email ${customer.email}. No action taken.`);
        }
    }
}


// Main webhook handler logic
serve(async (req) => {
    const signature = req.headers.get('Stripe-Signature');
    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET');
    
    if (!signature || !webhookSecret) {
        console.error('Webhook Error: Stripe signature or secret missing.');
        return new Response('Stripe signature or secret missing.', { status: 400 });
    }

    let event: Stripe.Event;
    try {
        const stripe = await getStripeInstance();
        const body = await req.text();
        event = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret);
    } catch (err) {
        console.error(`Webhook signature verification failed: ${err.message}`);
        return new Response(`Webhook signature verification failed: ${err.message}`, { status: 400 });
    }

    try {
        console.log(`Received webhook: ${event.type}`);
        switch (event.type) {
            case 'payment_intent.succeeded':
                await handlePaymentSuccess(event.data.object as Stripe.PaymentIntent);
                break;

            case 'invoice.payment_succeeded':
                await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
                break;

            case 'customer.subscription.updated':
            case 'customer.subscription.created':
                await handleSubscriptionChange(event.data.object as Stripe.Subscription);
                break;

            case 'charge.refunded':
                await handleChargeRefunded(event.data.object as Stripe.Charge);
                break;

            case 'customer.created':
                await handleCustomerCreated(event.data.object as Stripe.Customer);
                break;

            default:
                console.log(`Unhandled event type: ${event.type}`);
        }
    } catch (err) {
        console.error('Error in webhook handler:', err);
    }

    return new Response(JSON.stringify({ received: true }), {
        headers: { 'Content-Type': 'application/json' },
    });
});
