import { useQuery } from '@tanstack/react-query';
import { SubscriptionService } from '../lib/supabaseService';

export const useSubscriptions = () => {
  return useQuery({
    queryKey: ['subscriptions'],
    queryFn: SubscriptionService.getAllWithClientInfo,
    staleTime: 1 * 60 * 1000, // Subscriptions change frequently
  });
};

export const useSubscriptionsByClientId = (clientId: string) => {
    return useQuery({
        queryKey: ['subscriptions', 'client', clientId],
        queryFn: () => SubscriptionService.getByClientId(clientId),
        enabled: !!clientId,
        staleTime: 1 * 60 * 1000,
    });
};
