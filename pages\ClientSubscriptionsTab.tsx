import React, { useState, useMemo } from 'react';
import { useSubscriptionsByClientId } from '../hooks';
import type { Subscription } from '../types';
import { Button, ConfirmationModal } from '../components/ui';
import { supabase } from '../lib/supabaseClient';
import { useQueryClient } from '@tanstack/react-query';
import { useToastStore } from '../stores/useToastStore';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '../components/ui/Table';

const ClientSubscriptionsTab: React.FC<{ clientId: string }> = ({ clientId }) => {
    const { data: subscriptions = [], isLoading, isError } = useSubscriptionsByClientId(clientId);
    const [cancelCandidate, setCancelCandidate] = useState<Subscription | null>(null);
    const [isCanceling, setIsCanceling] = useState(false);
    const queryClient = useQueryClient();
    const { showToast } = useToastStore();
    const [filter, setFilter] = useState<'active' | 'all'>('active');

    const handleCancelSubscription = async () => {
        if (!cancelCandidate) return;
        setIsCanceling(true);
        try {
            const { error } = await supabase.functions.invoke('stripe-subscription-cancel', {
                body: { subscriptionId: cancelCandidate.stripeSubscriptionId },
            });
            if (error) throw error;

            showToast('Subscription successfully scheduled for cancellation.', 'success');
            await queryClient.invalidateQueries({ queryKey: ['subscriptions', 'client', clientId] });
        } catch (err: any) {
            console.error('Failed to cancel subscription:', err);
            showToast(`Error: ${err.message || 'Could not cancel subscription.'}`, 'error');
        } finally {
            setIsCanceling(false);
            setCancelCandidate(null);
        }
    };

    const statusColor: { [key: string]: string } = {
        active: 'bg-green-100 text-green-800',
        trialing: 'bg-blue-100 text-blue-800',
        past_due: 'bg-yellow-100 text-yellow-800',
        unpaid: 'bg-red-100 text-red-800',
        canceled: 'bg-gray-100 text-gray-800',
    };

    const filteredSubscriptions = useMemo(() => {
        if (filter === 'all') {
            return subscriptions;
        }
        // Les abonnements actifs sont ceux qui ne sont pas dans un état terminal comme 'canceled' ou 'unpaid'.
        const activeStatuses = ['active', 'trialing', 'past_due'];
        return subscriptions.filter(s => activeStatuses.includes(s.status));
    }, [subscriptions, filter]);


    if (isLoading) {
        return <div className="text-center p-10"><i className="fa-solid fa-spinner fa-spin text-2xl text-gray-400"></i></div>;
    }
    
    if (isError) {
        return <div className="text-center p-10 text-red-600">Failed to load subscriptions.</div>;
    }

    return (
        <>
            <div className="bg-white border border-gray-300">
                <div className="p-6 border-b border-gray-200 flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-black">My Subscriptions</h3>
                     <div className="flex gap-1">
                        <Button variant={filter === 'active' ? 'primary' : 'secondary'} size="sm" onClick={() => setFilter('active')}>Actifs</Button>
                        <Button variant={filter === 'all' ? 'primary' : 'secondary'} size="sm" onClick={() => setFilter('all')}>Tout l'historique</Button>
                    </div>
                </div>
                {subscriptions.length === 0 ? (
                    <div className="text-center p-10 text-gray-500">Vous n'avez pas encore d'abonnements.</div>
                ) : filteredSubscriptions.length === 0 ? (
                    <div className="text-center p-10 text-gray-500">Aucun abonnement ne correspond au filtre actuel.</div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Package</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Renouvellement / Fin le</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredSubscriptions.map((sub) => (
                                <TableRow key={sub.id}>
                                    <TableCell className="font-semibold text-black">{sub.packageName}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 text-xs font-medium capitalize ${statusColor[sub.status] || 'bg-gray-100'}`}>
                                            {sub.cancelAtPeriodEnd ? 'Annulation programmée' : sub.status.replace('_', ' ')}
                                        </span>
                                    </TableCell>
                                    <TableCell className="text-gray-600">
                                        {sub.status === 'canceled' && sub.canceledAt 
                                            ? new Date(sub.canceledAt).toLocaleDateString()
                                            : new Date(sub.currentPeriodEnd).toLocaleDateString()
                                        }
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <Button
                                            variant="danger-outline"
                                            size="sm"
                                            onClick={() => setCancelCandidate(sub)}
                                            disabled={sub.status !== 'active' || sub.cancelAtPeriodEnd}
                                            title={sub.cancelAtPeriodEnd ? "L'annulation est déjà programmée" : "Annuler l'abonnement"}
                                        >
                                            Annuler
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </div>

            <ConfirmationModal
                isOpen={!!cancelCandidate}
                onClose={() => setCancelCandidate(null)}
                onConfirm={handleCancelSubscription}
                title="Confirmer l'annulation de l'abonnement"
                message={
                    <>
                        <p>Êtes-vous sûr de vouloir annuler votre abonnement <strong>{cancelCandidate?.packageName}</strong> ?</p>
                        <p className="mt-2 text-xs bg-yellow-50 p-2 border border-yellow-200">
                            Votre abonnement restera actif jusqu'à la fin de la période de facturation en cours, le <strong>{cancelCandidate ? new Date(cancelCandidate.currentPeriodEnd).toLocaleDateString() : ''}</strong>.
                        </p>
                    </>
                }
                confirmText="Oui, annuler l'abonnement"
                isLoading={isCanceling}
            />
        </>
    );
};

export default ClientSubscriptionsTab;