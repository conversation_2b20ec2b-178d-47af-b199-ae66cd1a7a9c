import { create } from 'zustand';
import { OrderService, ClientService } from '@/lib/supabaseService';
import type { Order, CartItem, Client, DiscountCode } from '@/types';

interface OrderState {
  // Orders are stored in the client data in auth store
}

interface OrderActions {
  createOrder: (
    items: CartItem[],
    clientId: string,
    subtotal: number,
    vatAmount: number,
    total: number,
    paymentIntentId: string,
    appliedCodes: DiscountCode[]
  ) => Promise<Order | null>;
}

type OrderStore = OrderState & OrderActions;

export const useOrderStore = create<OrderStore>((set, get) => ({
  // Actions
  createOrder: async (items, clientId, subtotal, vatAmount, total, paymentIntentId, appliedCodes) => {
    
    const discountAmount = subtotal + vatAmount - total;

    // FIX: 'created_at' does not exist in type 'Order'. Use 'createdAt' and align with 'Order' type.
    const newOrder: Order = {
      id: `ORD-${Date.now()}`,
      clientId: clientId,
      createdAt: new Date().toISOString(),
      items,
      subtotal,
      vatAmount,
      total,
      status: 'Completed',
      stripePaymentIntentId: paymentIntentId,
      discountCodes: appliedCodes.map(c => ({ code: c.code, value: c.value, type: c.type })),
      discountAmount: discountAmount
    };

    const createdOrder = await OrderService.create(newOrder);
    const cartUpdated = await ClientService.updateCart(clientId, []);

    if (!createdOrder || !cartUpdated) {
      console.error('There was an error processing the order');
      return null;
    }

    return createdOrder;
  },
}));