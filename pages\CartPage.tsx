import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { loadStripe, Stripe, StripeElementsOptions } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { useCartStore } from '@/stores/useCartStore';
import { useAuthStore } from '@/stores/useAuthStore';
import { useOrderStore } from '@/stores/useOrderStore';
import { usePaymentSettings } from '@/hooks';
import CheckoutForm from '@/components/CheckoutForm';
import { Button, Input } from '@/components/ui';
import { supabase } from '@/lib/supabaseClient';
import { useToastStore } from '@/stores/useToastStore';

const CartPage: React.FC = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const { cart, getTotals, clearCart, appliedCodes, applyDiscountCode, removeDiscountCode } = useCartStore();
    const { user, isImpersonating, impersonatedClient } = useAuthStore();
    const { createOrder } = useOrderStore();
    const { showToast } = useToastStore();
    const { data: paymentSettings, isLoading: paymentSettingsLoading } = usePaymentSettings();

    const [stripePromise, setStripePromise] = useState<Promise<Stripe | null> | null>(null);
    const [clientSecret, setClientSecret] = useState<string | null>(null);
    const [isPreparingPayment, setIsPreparingPayment] = useState(true);
    const [isSubmittingOrder, setIsSubmittingOrder] = useState(false);
    const [promoCode, setPromoCode] = useState('');

    const client = isImpersonating ? impersonatedClient : user.data;
    const { subtotal, discountAmount, total } = getTotals();
    const vatAmount = (subtotal - discountAmount) * 0.20; // Assuming 20% VAT
    const finalTotal = total + vatAmount;

    useEffect(() => {
        if (paymentSettings?.stripe.publicKey) {
            setStripePromise(loadStripe(paymentSettings.stripe.publicKey));
        }
    }, [paymentSettings]);

    useEffect(() => {
        if (finalTotal > 0 && client && paymentSettings?.stripe.enabled) {
            setIsPreparingPayment(true);
            const syncAndCreateIntent = async () => {
                try {
                    const { data: customerData, error: customerError } = await supabase.functions.invoke('stripe-customer-sync', {
                        body: { clientId: client.id },
                    });
                    if (customerError) throw customerError;
                    if (customerData.error) throw new Error(customerData.error);
                    
                    const customerId = customerData.customer.id;
                    const { data, error } = await supabase.functions.invoke('stripe-payment-intent', {
                        body: {
                            amount: Math.round(finalTotal * 100),
                            customerId,
                            email: client.profile.email,
                            cartItems: cart,
                            metadata: { client_id: client.id, client_name: client.profile.companyName },
                        },
                    });

                    if (error) throw error;
                    if (data.error) throw new Error(data.error);
                    setClientSecret(data.clientSecret);
                } catch (err: any) {
                    console.error(err);
                    showToast(err.message || 'Could not initialize payment.', 'error');
                } finally {
                    setIsPreparingPayment(false);
                }
            };
            syncAndCreateIntent();
        } else if (finalTotal <= 0) {
            setIsPreparingPayment(false);
        }
    }, [finalTotal, client?.id, paymentSettings, cart]);

    const handlePaymentSuccess = async (paymentIntentId: string) => {
        if (!client) return;
        setIsSubmittingOrder(true);
        const order = await createOrder(
            cart,
            client.id,
            subtotal,
            vatAmount,
            finalTotal,
            paymentIntentId,
            appliedCodes
        );
        
        if (order) {
            await clearCart(client.id);
            navigate('/order-confirmation', { state: { order }, replace: true });
        } else {
            showToast('There was an issue creating your order. Please contact support.', 'error');
        }
        setIsSubmittingOrder(false);
    };

    useEffect(() => {
        const paymentIntentClientSecret = searchParams.get('payment_intent_client_secret');
        if (paymentIntentClientSecret && stripePromise) {
            setIsPreparingPayment(true);
            stripePromise.then(stripe => {
                if (!stripe) return;
                stripe.retrievePaymentIntent(paymentIntentClientSecret).then(async ({ paymentIntent }) => {
                    if (paymentIntent?.status === 'succeeded') {
                        await handlePaymentSuccess(paymentIntent.id);
                    } else {
                        showToast(paymentIntent?.last_payment_error?.message || 'Payment failed.', 'error');
                        setIsPreparingPayment(false);
                    }
                });
            });
        }
    }, [searchParams, stripePromise]);

    const handleApplyPromoCode = async () => {
        if (!promoCode.trim()) return;
        const result = await applyDiscountCode(promoCode);
        showToast(result.message, result.success ? 'success' : 'error');
        if (result.success) setPromoCode('');
    };
    
    const handlePaymentError = (errorMessage: string) => {
        showToast(errorMessage, 'error');
    };

    if (cart.length === 0 && !isImpersonating) {
        return (
            <div className="text-center py-20">
                <i className="fa-solid fa-cart-shopping text-5xl text-gray-300 mb-4"></i>
                <h2 className="text-2xl font-semibold text-black">Your Cart is Empty</h2>
                <p className="text-gray-600 mt-2">Looks like you haven't added any services yet.</p>
                <Button onClick={() => navigate('/')} className="mt-6">Explore Services</Button>
            </div>
        );
    }
    
    const stripeOptions: StripeElementsOptions = {
        clientSecret: clientSecret || undefined,
        appearance: { theme: 'stripe' },
    };

    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
                <h2 className="text-2xl font-bold text-black mb-6">Order Summary</h2>
                <div className="bg-white border border-gray-300">
                    <div className="p-6 space-y-4">
                        {cart.map(item => (
                            <div key={item.cartId} className="flex justify-between items-start pb-4 border-b border-gray-200 last:border-b-0">
                                <div>
                                    <p className="font-semibold text-black">{item.name}</p>
                                    <p className="text-sm text-gray-500">{item.finalBilling}</p>
                                </div>
                                <p className="font-semibold text-black">${item.finalPrice.toFixed(2)}</p>
                            </div>
                        ))}
                         {cart.length === 0 && (
                            <p className="text-sm text-gray-500 text-center py-4">The cart is currently empty for this client.</p>
                        )}
                    </div>
                    <div className="p-6 bg-gray-50 border-t border-gray-200 space-y-3">
                        <div className="flex gap-2">
                            <Input
                            type="text"
                            placeholder="Discount Code"
                            value={promoCode}
                            onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                            className="flex-grow"
                            />
                            <Button onClick={handleApplyPromoCode} variant="secondary">Apply</Button>
                        </div>
                        {appliedCodes.map(code => (
                        <div key={code.id} className="flex justify-between items-center text-xs bg-green-50 p-2 text-green-700">
                            <span>✓ Code "{code.code}" applied</span>
                            <button onClick={() => removeDiscountCode(code.id)} className="font-bold">✕</button>
                        </div>
                        ))}
                    </div>
                    <div className="p-6 border-t border-gray-200 space-y-2">
                        <div className="flex justify-between text-sm"><span className="text-gray-600">Subtotal</span><span className="font-medium text-black">${subtotal.toFixed(2)}</span></div>
                        {discountAmount > 0 && (
                            <div className="flex justify-between text-sm text-green-600">
                                <span>Discount</span>
                                <span>-${discountAmount.toFixed(2)}</span>
                            </div>
                        )}
                        <div className="flex justify-between text-sm"><span className="text-gray-600">VAT (20%)</span><span className="font-medium text-black">${vatAmount.toFixed(2)}</span></div>
                        <div className="flex justify-between font-bold text-lg pt-2 border-t border-gray-300 mt-2"><span className="text-black">Total</span><span className="text-black">${finalTotal.toFixed(2)}</span></div>
                    </div>
                </div>
            </div>

            <div>
                <h2 className="text-2xl font-bold text-black mb-6">Payment Details</h2>
                <div className="bg-white border border-gray-300 p-6 min-h-[300px] flex items-center justify-center">
                    {(isPreparingPayment || paymentSettingsLoading) ? (
                        <div className="text-center"><i className="fa-solid fa-spinner fa-spin text-2xl text-gray-400"></i><p className="text-sm text-gray-500 mt-2">Initializing secure payment...</p></div>
                    ) : (clientSecret && stripePromise && finalTotal > 0) ? (
                        <Elements stripe={stripePromise} options={stripeOptions}>
                            <CheckoutForm 
                                onPaymentSuccess={handlePaymentSuccess} 
                                onPaymentError={handlePaymentError}
                                isSubmitting={isSubmittingOrder}
                            />
                        </Elements>
                    ) : (
                        <div className="text-center p-4 bg-yellow-50 text-yellow-800 border border-yellow-200">
                            <p className="font-semibold">Payment cannot be processed.</p>
                            <p className="text-sm mt-1">{finalTotal <= 0 ? 'The total amount is $0.00. Please contact support to finalize this order.' : 'Payment processing is currently unavailable.'}</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CartPage;