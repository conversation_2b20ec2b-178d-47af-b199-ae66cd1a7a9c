import React, { useState, useEffect } from 'react';
import { useDiscountCodes } from '../../hooks';
import { useAdminActions } from '../../hooks';
import { useQueryClient } from '@tanstack/react-query';
import type { DiscountCode } from '../../types';
import FormField from '../../components/ui/FormField';
import { ToggleSwitch, Button, ConfirmationModal, SidePanel } from '../../components/ui';
import { useToastStore } from '../../stores/useToastStore';

const emptyDiscountCode: DiscountCode = {
  id: '',
  code: '',
  type: 'percentage',
  value: 10,
  validFrom: null,
  validTo: null,
  isStackable: false,
  isActive: true,
};

const formatIsoForInput = (isoString?: string | null): string => {
    if (!isoString) return '';
    try {
      const date = new Date(isoString);
      return date.toISOString().slice(0, 16);
    } catch (e) {
      return '';
    }
};
  
const formatInputForIso = (inputString?: string): string | null => {
    if (!inputString) return null;
    return new Date(inputString).toISOString();
};

const DiscountEditor: React.FC<{
    code: DiscountCode;
    onSave: (code: DiscountCode) => void;
    onClose: () => void;
    isCreating: boolean;
}> = ({ code, onSave, onClose, isCreating }) => {
    const [formData, setFormData] = useState<DiscountCode>(code);
    const { showToast } = useToastStore();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        if (type === 'datetime-local') {
            setFormData(prev => ({ ...prev, [name]: value ? formatInputForIso(value) : null }));
        } else if (name === 'code') {
            setFormData(prev => ({...prev, code: value.toUpperCase()}))
        } else {
            setFormData(prev => ({ ...prev, [name]: type === 'number' ? parseFloat(value) : value }));
        }
    };
    
    const handleToggle = (name: keyof DiscountCode) => {
        setFormData(prev => ({ ...prev, [name]: !prev[name] }));
    }

    const handleSave = () => {
        // Basic validation
        if (!formData.code.trim() || formData.value <= 0) {
            showToast('Code and a positive value are required.', 'error');
            return;
        }
        onSave(formData);
    };

    const renderFooter = () => (
        <div className="flex gap-2">
            <Button onClick={handleSave}>Save</Button>
            <Button variant="secondary" onClick={onClose}>Cancel</Button>
        </div>
    );

    return (
        <SidePanel
            isOpen={true}
            onClose={onClose}
            title={isCreating ? 'Create Discount Code' : 'Edit Discount Code'}
            size="lg"
            footer={renderFooter()}
        >
            <div className="space-y-4">
                <FormField label="Discount Code" name="code" value={formData.code} onChange={handleChange} placeholder="e.g., SUMMER20" />
                <div className="grid grid-cols-2 gap-4">
                    <FormField as="select" label="Type" name="type" value={formData.type} onChange={handleChange} options={['percentage', 'fixed']} />
                    <FormField label="Value" name="value" type="number" value={formData.value} onChange={handleChange} />
                </div>
                     <div className="grid grid-cols-2 gap-4">
                        <FormField label="Valid From (Optional)" name="validFrom" type="datetime-local" value={formatIsoForInput(formData.validFrom)} onChange={handleChange} />
                        <FormField label="Valid To (Optional)" name="validTo" type="datetime-local" value={formatIsoForInput(formData.validTo)} onChange={handleChange} />
                    </div>
                <div className="space-y-3 pt-4 border-t border-gray-200">
                    <ToggleSwitch label="Stackable" enabled={formData.isStackable} onChange={() => handleToggle('isStackable')} description="Can this code be used with other discounts?" />
                    <ToggleSwitch label="Active" enabled={formData.isActive} onChange={() => handleToggle('isActive')} description="Is this code currently usable by customers?" />
                </div>
            </div>
        </SidePanel>
    )
};


const DiscountSettingsTab = () => {
    const { data: discountCodes = [], isLoading } = useDiscountCodes();
    const adminActions = useAdminActions();
    const queryClient = useQueryClient();
    const { showToast } = useToastStore();
    const [editorState, setEditorState] = useState<{ isOpen: boolean, isCreating: boolean, code: DiscountCode | null }>({ isOpen: false, isCreating: false, code: null });
    const [deleteCandidate, setDeleteCandidate] = useState<string | null>(null);

    const handleSave = async (code: DiscountCode) => {
        try {
            if (editorState.isCreating) {
                const { id, ...createData } = code;
                await adminActions.createRecord('discount_codes', createData);
            } else if (code.id) {
                const { id, ...updateData } = code;
                await adminActions.updateRecord('discount_codes', updateData, id);
            }
            queryClient.invalidateQueries({ queryKey: ['discount-codes'] });
            setEditorState({ isOpen: false, isCreating: false, code: null });
        } catch (error) {
            showToast(`Error saving discount code: ${error instanceof Error ? error.message : String(error)}`, 'error');
        }
    };
    
    const confirmDelete = async () => {
        if (!deleteCandidate) return;
        try {
            await adminActions.deleteRecord('discount_codes', deleteCandidate);
            queryClient.invalidateQueries({ queryKey: ['discount-codes'] });
        } catch(e) {
            showToast(`Error: ${e instanceof Error ? e.message : 'Could not delete code.'}`, 'error');
        } finally {
            setDeleteCandidate(null);
        }
    };

    const codeToDelete = deleteCandidate ? discountCodes.find(c => c.id === deleteCandidate) : null;

    return (
        <div className="space-y-6">
            <div className="bg-white border border-gray-300 p-4">
                <button onClick={() => setEditorState({ isOpen: true, isCreating: true, code: emptyDiscountCode })} className="w-full px-4 py-2 bg-black text-white text-sm font-medium hover:bg-gray-800 flex items-center justify-center gap-2">
                    <i className="fa-solid fa-plus"></i> Add New Discount Code
                </button>
            </div>
            
            <div className="bg-white border border-gray-300">
                {isLoading ? (
                    <div className="text-center p-10"><i className="fa-solid fa-spinner fa-spin text-2xl text-gray-400"></i></div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                             <thead className="bg-gray-50 border-b border-gray-200">
                                <tr>
                                    <th className="px-4 py-3 font-medium text-black text-left">Code</th>
                                    <th className="px-4 py-3 font-medium text-black text-left">Type</th>
                                    <th className="px-4 py-3 font-medium text-black text-left">Value</th>
                                    <th className="px-4 py-3 font-medium text-black text-left">Validity</th>
                                    <th className="px-4 py-3 font-medium text-black text-center">Status</th>
                                    <th className="px-4 py-3 font-medium text-black text-right">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {discountCodes.map(code => (
                                    <tr key={code.id} className="border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
                                        <td className="px-4 py-3 font-semibold font-mono text-black">{code.code}</td>
                                        <td className="px-4 py-3 capitalize">{code.type}</td>
                                        <td className="px-4 py-3">{code.type === 'percentage' ? `${code.value}%` : `$${code.value.toFixed(2)}`}</td>
                                        <td className="px-4 py-3 text-xs">{code.validFrom ? new Date(code.validFrom).toLocaleDateString() : 'Always'} - {code.validTo ? new Date(code.validTo).toLocaleDateString() : 'Never'}</td>
                                        <td className="px-4 py-3 text-center">
                                            <span className={`px-2 py-1 text-xs font-medium ${code.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>{code.isActive ? 'Active' : 'Inactive'}</span>
                                        </td>
                                        <td className="px-4 py-3 text-right">
                                             <button onClick={() => setEditorState({ isOpen: true, isCreating: false, code })} className="px-3 py-1 text-xs border border-gray-300 hover:bg-gray-100">Edit</button>
                                             <button onClick={() => setDeleteCandidate(code.id)} className="ml-2 px-3 py-1 text-xs border border-red-300 text-red-600 hover:bg-red-50">Delete</button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
            
            {editorState.isOpen && editorState.code && (
                <DiscountEditor 
                    code={editorState.code}
                    onSave={handleSave}
                    onClose={() => setEditorState({ isOpen: false, isCreating: false, code: null })}
                    isCreating={editorState.isCreating}
                />
            )}
            <ConfirmationModal
                isOpen={!!deleteCandidate}
                onClose={() => setDeleteCandidate(null)}
                onConfirm={confirmDelete}
                title="Delete Discount Code"
                message={<p>Are you sure you want to delete the code <strong>{codeToDelete?.code}</strong>? This action cannot be undone.</p>}
                confirmText="Delete Code"
            />
        </div>
    );
};

export default DiscountSettingsTab;