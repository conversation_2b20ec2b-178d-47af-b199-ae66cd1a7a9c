import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui';

interface HeroProps {}

const Hero: React.FC<HeroProps> = () => {
  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center py-12 md:py-20 bg-white border border-gray-300">
        <h1 className="text-4xl md:text-5xl text-black mb-4">
          <span className="italic">Welcome to </span>
          <span className="font-bold">your</span>
          <span className="italic"> Voice-AI-Space </span>
          <span className="font-bold">portal</span>
        </h1>
        <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Your hub to pick and manage the Voice AI Space Service that are the most appropriate to your need.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Button size="lg">
            Explore Services
          </Button>
          <Link to="/contact" className="inline-flex items-center justify-center whitespace-nowrap transition-colors focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 px-6 py-3 text-base font-semibold">
            Contact Us
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Hero;