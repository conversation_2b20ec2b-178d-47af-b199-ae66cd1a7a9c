import React from 'react';
import { Button } from './Button';
import { Select } from './Select';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  onItemsPerPageChange: (items: number) => void;
  totalItems: number;
  itemType?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
  onItemsPerPageChange,
  totalItems,
  itemType = 'items'
}) => {
  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className="flex items-center justify-between p-4 border-t border-gray-200 flex-wrap gap-4">
      <div className="text-sm text-gray-600">
        Showing <span className="font-semibold text-black">{startItem}</span> to <span className="font-semibold text-black">{endItem}</span> of <span className="font-semibold text-black">{totalItems}</span> {itemType}
      </div>
      <div className="flex items-center gap-4 flex-wrap">
        <div className="flex items-center gap-2">
            <label htmlFor="itemsPerPage" className="text-sm text-gray-600">Rows per page:</label>
            <Select 
                id="itemsPerPage"
                value={itemsPerPage} 
                onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
                className="w-20"
            >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
            </Select>
        </div>
        <div className="text-sm text-gray-600">
          Page <span className="font-semibold text-black">{currentPage}</span> of <span className="font-semibold text-black">{totalPages > 0 ? totalPages : 1}</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
