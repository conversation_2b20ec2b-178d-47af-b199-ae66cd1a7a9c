import React, { useState, useMemo } from 'react';
import type { Client, Order, Invoice } from '../../types';
import { Button, SidePanel, ConfirmationModal, Pagination } from '../../components/ui';
import { Input } from '../../components/ui/Input';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '../../components/ui/Table';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../../lib/supabaseClient';
import { useToastStore } from '../../stores/useToastStore';


type OrderSortKey = 'createdAt' | 'total';

interface OrdersTabProps {
    clients: Client[];
    selectedOrder: { order: Order, client: Client } | null;
    setSelectedOrder: (order: { order: Order, client: Client } | null) => void;
}

const OrdersTab: React.FC<OrdersTabProps> = ({ clients, selectedOrder, setSelectedOrder }) => {
    const [orderSearchTerm, setOrderSearchTerm] = useState('');
    const [orderSort, setOrderSort] = useState<{ key: OrderSortKey; direction: 'asc' | 'desc' }>({ key: 'createdAt', direction: 'desc' });
    const { showToast } = useToastStore();
    const queryClient = useQueryClient();
    const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
    const [isRefunding, setIsRefunding] = useState(false);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);

    const allOrders = useMemo(() => clients.flatMap(client => (client.orders || []).map(order => ({ order, client }))), [clients]);

    const filteredAndSortedOrders = useMemo(() => {
        return allOrders
          .filter(({ order, client }) => {
            const lowercasedFilter = orderSearchTerm.toLowerCase();
            return (
              order.id.toLowerCase().includes(lowercasedFilter) ||
              client.profile.companyName.toLowerCase().includes(lowercasedFilter)
            );
          })
          .sort((a, b) => {
            // FIX: Property 'created_at' does not exist on type 'Order'. Did you mean 'createdAt'?
            const valA = orderSort.key === 'createdAt' ? new Date(a.order.createdAt).getTime() : a.order.total;
            // FIX: Property 'created_at' does not exist on type 'Order'. Did you mean 'createdAt'?
            const valB = orderSort.key === 'createdAt' ? new Date(b.order.createdAt).getTime() : b.order.total;
            return orderSort.direction === 'asc' ? valA - valB : valB - valA;
          });
    }, [allOrders, orderSearchTerm, orderSort]);

    // Pagination calculations
    const totalPages = Math.ceil(filteredAndSortedOrders.length / itemsPerPage);
    const paginatedOrders = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        return filteredAndSortedOrders.slice(startIndex, startIndex + itemsPerPage);
    }, [filteredAndSortedOrders, currentPage, itemsPerPage]);

    const handleItemsPerPageChange = (value: number) => {
        setItemsPerPage(value);
        setCurrentPage(1); // Reset to first page
    };

    const handleRefundOrder = async () => {
        if (!selectedOrder || !selectedOrder.order.stripePaymentIntentId) {
            showToast('Order cannot be refunded without a Payment Intent ID.', 'error');
            return;
        }
        setIsRefunding(true);
        try {
            const { error } = await supabase.functions.invoke('stripe-refund', {
                body: { 
                    paymentIntentId: selectedOrder.order.stripePaymentIntentId,
                    orderId: selectedOrder.order.id
                },
            });
            if (error) throw error;
    
            showToast('Refund processed successfully. Order status will update automatically.', 'success');
            
            // The webhook will update the status, so we refetch after a delay
            setTimeout(() => {
                queryClient.invalidateQueries({ queryKey: ['orders'] });
                queryClient.invalidateQueries({ queryKey: ['clients'] }); 
            }, 2500);
            
            setSelectedOrder(null);
        } catch (err: any) {
            console.error('Failed to refund order:', err);
            showToast(`Refund failed: ${err.message}`, 'error');
        } finally {
            setIsRefunding(false);
            setIsRefundModalOpen(false);
        }
    };

    const DetailItem: React.FC<{ label: string, value: React.ReactNode }> = ({ label, value }) => (
        <div>
            <p className="text-xs text-gray-500 font-medium uppercase tracking-wider">{label}</p>
            <div className="text-sm text-black mt-1">{value || 'N/A'}</div>
        </div>
    );

    const renderSidePanelFooter = () => {
        if (!selectedOrder) return null;
        const { order } = selectedOrder;
        const canRefund = order.status === 'Completed' && order.stripePaymentIntentId;
    
        return (
            <div className="flex justify-end">
                <Button
                    variant="danger-outline"
                    onClick={() => setIsRefundModalOpen(true)}
                    disabled={!canRefund || isRefunding}
                    title={!canRefund ? 'Only completed orders with a payment ID can be refunded.' : 'Issue a full refund for this order.'}
                >
                    Refund Order
                </Button>
            </div>
        );
    };

    return (
        <>
            <div className="bg-white border border-gray-300">
                <div className="p-4 border-b border-gray-200 flex items-center gap-4">
                    <h3 className="text-lg font-semibold text-black">Order Management</h3>
                    <Input 
                      type="text" 
                      placeholder="Search by Order ID or Company..." 
                      value={orderSearchTerm} 
                      // FIX: Corrected typo from setSearchTerm to setOrderSearchTerm.
                      onChange={(e) => setOrderSearchTerm(e.target.value)} 
                      className="w-64"
                    />
                </div>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Order ID</TableHead>
                            <TableHead>Company</TableHead>
                            <TableHead 
                                className="cursor-pointer" 
                                onClick={() => setOrderSort(s => ({ key: 'createdAt', direction: s.direction === 'asc' ? 'desc' : 'asc' }))}
                            >
                                Date <i className={`fa-solid fa-sort-${orderSort.key === 'createdAt' ? (orderSort.direction === 'asc' ? 'up' : 'down') : 'up-down'}`}></i>
                            </TableHead>
                            <TableHead 
                                className="text-right cursor-pointer" 
                                onClick={() => setOrderSort(s => ({ key: 'total', direction: s.direction === 'asc' ? 'desc' : 'asc' }))}
                            >
                                Total <i className={`fa-solid fa-sort-${orderSort.key === 'total' ? (orderSort.direction === 'asc' ? 'up' : 'down') : 'up-down'}`}></i>
                            </TableHead>
                            <TableHead className="text-center">Items</TableHead>
                            <TableHead className="text-center">Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {paginatedOrders.map(({ order, client }) => (
                            <TableRow key={order.id} onClick={() => setSelectedOrder({ order, client })} className="cursor-pointer">
                                <TableCell className="font-mono text-xs text-gray-800">{order.id}</TableCell>
                                <TableCell className="font-semibold text-black">{client.profile.companyName}</TableCell>
                                {/* FIX: Property 'created_at' does not exist on type 'Order'. Did you mean 'createdAt'? */}
                                <TableCell className="text-gray-600">{new Date(order.createdAt).toLocaleDateString()}</TableCell>
                                <TableCell className="text-right font-medium text-black">${order.total.toFixed(2)}</TableCell>
                                <TableCell className="text-center text-black">{order.items.length}</TableCell>
                                <TableCell className="text-center text-black">{order.status}</TableCell>
                                <TableCell className="text-right">
                                    <div className="flex items-center justify-end gap-2">
                                        {order.stripeInvoiceId && (
                                            <a
                                                href={`https://dashboard.stripe.com/invoices/${order.stripeInvoiceId}`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                onClick={(e) => e.stopPropagation()}
                                                title="View Stripe Invoice"
                                                className="inline-flex items-center justify-center whitespace-nowrap transition-colors focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 px-3 py-1.5 text-xs font-medium"
                                            >
                                                <i className="fa-brands fa-stripe mr-2"></i> Invoice
                                            </a>
                                        )}
                                        <Button onClick={(e) => { e.stopPropagation(); setSelectedOrder({ order, client }); }} variant="secondary" size="sm">
                                          Details
                                        </Button>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
                {totalPages > 0 && (
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={setCurrentPage}
                        itemsPerPage={itemsPerPage}
                        onItemsPerPageChange={handleItemsPerPageChange}
                        totalItems={filteredAndSortedOrders.length}
                        itemType="orders"
                    />
                )}
            </div>
            
            <SidePanel
                isOpen={!!selectedOrder}
                onClose={() => setSelectedOrder(null)}
                title={`Order #${selectedOrder?.order.id}`}
                size="lg"
                footer={selectedOrder ? renderSidePanelFooter() : undefined}
            >
                {selectedOrder && (() => {
                    const relatedInvoice = selectedOrder.client.invoices?.find(
                        (inv: Invoice) => inv.stripeInvoiceId === selectedOrder.order.stripeInvoiceId
                    );
                    return (
                        <div className="space-y-8">
                            <section>
                                <h4 className="text-base font-semibold text-black mb-3">Client Information</h4>
                                <div className="grid grid-cols-2 gap-y-4 gap-x-6 p-4 bg-gray-50 border border-gray-200">
                                    <DetailItem label="Company" value={selectedOrder.client.profile.companyName} />
                                    <DetailItem label="Contact" value={selectedOrder.client.profile.primaryContact} />
                                    <DetailItem label="Email" value={<a href={`mailto:${selectedOrder.client.profile.email}`} className="text-black hover:underline">{selectedOrder.client.profile.email}</a>} />
                                    <DetailItem label="Account No." value={selectedOrder.client.profile.accountNumber} />
                                </div>
                            </section>

                            <section>
                                <h4 className="text-base font-semibold text-black mb-3">Order Summary</h4>
                                 <div className="grid grid-cols-2 gap-y-4 gap-x-6">
                                    {/* FIX: Property 'created_at' does not exist on type 'Order'. Did you mean 'createdAt'? */}
                                    <DetailItem label="Order Date" value={new Date(selectedOrder.order.createdAt).toLocaleString()} />
                                    <DetailItem label="Status" value={<span className="font-medium">{selectedOrder.order.status}</span>} />
                                </div>
                            </section>

                            <section>
                                <h4 className="text-base font-semibold text-black mb-3">Items Ordered ({selectedOrder.order.items.length})</h4>
                                <div className="space-y-3 border-t border-b border-gray-200 divide-y divide-gray-200">
                                    {selectedOrder.order.items.map(item => (
                                        <div key={item.cartId} className="flex justify-between items-center py-3">
                                            <div>
                                                <p className="font-semibold text-black">{item.name}</p>
                                                <p className="text-xs text-gray-500">{item.finalBilling}</p>
                                            </div>
                                            <p className="font-semibold text-black">${item.finalPrice.toFixed(2)}</p>
                                        </div>
                                    ))}
                                </div>
                            </section>

                            <section>
                                <h4 className="text-base font-semibold text-black mb-3">Financials</h4>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between"><span className="text-gray-600">Subtotal</span><span className="font-medium text-black">${selectedOrder.order.subtotal.toFixed(2)}</span></div>
                                    {selectedOrder.order.discountAmount && selectedOrder.order.discountAmount > 0 && (
                                        <div className="flex justify-between text-green-600">
                                            <span>Discount ({selectedOrder.order.discountCodes?.map(c => c.code).join(', ')})</span>
                                            <span>-${selectedOrder.order.discountAmount.toFixed(2)}</span>
                                        </div>
                                    )}
                                    <div className="flex justify-between"><span className="text-gray-600">VAT (20%)</span><span className="font-medium text-black">${selectedOrder.order.vatAmount.toFixed(2)}</span></div>
                                    <div className="flex justify-between font-bold text-base pt-2 border-t border-gray-300 mt-2">
                                        <span className="text-black">Total Paid</span>
                                        <span className="text-black">${selectedOrder.order.total.toFixed(2)}</span>
                                    </div>
                                </div>
                            </section>

                            {(selectedOrder.order.stripePaymentIntentId || relatedInvoice) && (
                                <section>
                                    <h4 className="text-base font-semibold text-black mb-3">Invoice & Payment Actions</h4>
                                    <div className="space-y-2">
                                        <div className="flex flex-col sm:flex-row gap-2">
                                            {relatedInvoice?.hostedInvoiceUrl && (
                                                <a href={relatedInvoice.hostedInvoiceUrl} target="_blank" rel="noopener noreferrer" className="flex-1">
                                                    <Button variant="secondary" className="w-full flex items-center justify-center gap-2">
                                                        <i className="fa-solid fa-eye"></i> Preview Invoice
                                                    </Button>
                                                </a>
                                            )}
                                            {relatedInvoice?.invoicePdf && (
                                                <a href={relatedInvoice.invoicePdf} target="_blank" rel="noopener noreferrer" className="flex-1">
                                                    <Button variant="secondary" className="w-full flex items-center justify-center gap-2">
                                                        <i className="fa-solid fa-file-pdf"></i> Download PDF
                                                    </Button>
                                                </a>
                                            )}
                                        </div>
                                        {selectedOrder.order.stripeInvoiceId && (
                                            <a 
                                                href={`https://dashboard.stripe.com/invoices/${selectedOrder.order.stripeInvoiceId}`} 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                className="block w-full"
                                            >
                                                <Button className="w-full flex items-center justify-center gap-2">
                                                    <i className="fa-brands fa-stripe"></i> Manage in Stripe
                                                </Button>
                                            </a>
                                        )}
                                    </div>
                                </section>
                            )}
                        </div>
                    );
                })()}
            </SidePanel>

            <ConfirmationModal
                isOpen={isRefundModalOpen}
                onClose={() => setIsRefundModalOpen(false)}
                onConfirm={handleRefundOrder}
                title="Confirm Order Refund"
                message={
                    <p>
                        Are you sure you want to issue a full refund for order <strong>#{selectedOrder?.order.id}</strong> for <strong>${selectedOrder?.order.total.toFixed(2)}</strong>? This action is processed via Stripe and cannot be undone.
                    </p>
                }
                confirmText="Yes, Issue Full Refund"
                isLoading={isRefunding}
            />
        </>
    );
};

export default OrdersTab;
