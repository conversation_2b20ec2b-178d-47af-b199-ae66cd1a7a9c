




import React from 'react';
import { create } from 'zustand';
import type { Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabaseClient';
import { ClientService } from '@/lib/supabaseService';
import type { UserType, Client, SignupData, ProfileData, Message } from '@/types';

interface AuthState {
  session: Session | null;
  user: { type: UserType; data: Client | null };
  isLoading: boolean;
  isImpersonating: boolean;
  impersonatedClient: Client | null;
}

interface AuthActions {
  setSession: (session: Session | null) => void;
  setUser: (user: { type: UserType; data: Client | null }) => void;
  setIsLoading: (loading: boolean) => void;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  signup: (signupData: SignupData, password: string) => Promise<boolean>;
  startImpersonation: (client: Client) => void;
  endImpersonation: () => void;
  setClientData: (updater: (prevClient: Client) => Client) => Promise<void>;
  fetchUserData: (session: Session | null) => Promise<void>;
  updateClientInStore: (updatedClient: Client) => void;
  handleIncomingMessage: (newMessage: Message, conversationId: string) => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>((set, get) => ({
  // State
  session: null,
  user: { type: null, data: null },
  isLoading: true,
  isImpersonating: false,
  impersonatedClient: null,

  // Actions
  setSession: (session) => set({ session }),
  setUser: (user) => set({ user }),
  setIsLoading: (loading) => set({ isLoading: loading }),

  login: async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) {
      console.error('Login error:', error.message);
      return { success: false, error: error.message };
    }
    return { success: true };
  },

  logout: async () => {
    const { isImpersonating } = get();
    if (isImpersonating) return;
    await supabase.auth.signOut();
  },

  signup: async (signupData: SignupData, password: string) => {
    // This profile data is stored in the user's metadata and used to create their client profile upon first login after email confirmation.
    const profilePayload: ProfileData = {
      ...(signupData as ProfileData),
      accountNumber: `ACC-${Date.now()}`,
      paymentMethod: '',
      paymentMethodLast4: '',
      billingAddress: `${signupData.address || ''}, ${signupData.city || ''}`,
      accountStatus: 'Active',
      subscriptionTier: 'None',
      memberSince: new Date().toISOString(),
      autoRenewal: true,
      notifications: {
        productUpdates: true,
        eventUpdates: true,
        newsletter: true,
        accountAlerts: true,
      },
    };

    const { error } = await supabase.auth.signUp({
      email: signupData.email!,
      password: password,
      options: {
        data: {
          role: 'client',
          profile: profilePayload,
        },
      },
    });

    if (error) {
      console.error('Signup error:', error.message);
      return false;
    }

    return true;
  },

  startImpersonation: (client: Client) => {
    set({
      impersonatedClient: client,
      isImpersonating: true,
    });
  },

  endImpersonation: () => {
    set({
      isImpersonating: false,
      impersonatedClient: null,
    });
  },

  setClientData: async (updater: (prevClient: Client) => Client) => {
    const { user } = get();
    if (user.data) {
      const updatedClient = updater(user.data);
      const success = await ClientService.updateProfile(updatedClient.id, updatedClient.profile);
      if (!success) {
        console.error('Failed to update profile');
      } else {
        set({ user: { ...user, data: updatedClient } });
      }
    }
  },

  fetchUserData: async (currentSession: Session | null) => {
    set({ isLoading: true });

    if (currentSession) {
      try {
        let clientData = await ClientService.getByUserId(currentSession.user.id);

        // If a user is authenticated but has no profile, it's likely their first login after email confirmation.
        // We create their profile now using the metadata provided during signup.
        if (!clientData && currentSession.user.user_metadata?.profile) {
            console.log("No client profile found for authenticated user. Creating one from signup metadata.");
            const profilePayload = currentSession.user.user_metadata.profile;
            const newClientData: Omit<Client, 'id'> = {
                user_id: currentSession.user.id,
                role: 'client',
                email: currentSession.user.email!,
                profile: profilePayload,
                cart: [],
                cancelledPackageIds: [],
            };
            
            clientData = await ClientService.create(newClientData);

            if (clientData) {
                console.log("Successfully created new client profile:", clientData.id);
            } else {
                 console.error("Failed to create client profile from signup metadata.");
            }
        }

        if (clientData) {
          const userType = clientData.role === 'admin' ? 'admin' : 'client';
          set({ user: { type: userType, data: clientData } });
        } else {
            set({ user: { type: null, data: null } });
        }
      } catch (error) {
        console.error('Error fetching or creating client data:', error);
        set({ user: { type: null, data: null } });
      }
    } else {
      set({ user: { type: null, data: null } });
    }

    set({ isLoading: false });
  },

  updateClientInStore: (updatedClient: Client) => {
    const { isImpersonating } = get();
    if (isImpersonating) {
      set({ impersonatedClient: updatedClient });
    }
  },

  handleIncomingMessage: (newMessage: Message, conversationId: string) => {
    const { user, setUser } = get();
    if (user.data) {
        const updatedConversations = user.data.conversations?.map(convo => {
            if (convo.id === conversationId) {
                // Avoid adding duplicate messages if one was already added optimistically
                if (convo.messages.some(m => m.id === newMessage.id)) {
                    return convo;
                }
                return {
                    ...convo,
                    messages: [...convo.messages, newMessage],
                    // FIX: Property 'created_at' does not exist on type 'Message'. Did you mean 'createdAt'? Also fix related properties to match camelCase.
                    lastUpdated: newMessage.createdAt,
                    clientHasUnread: true,
                };
            }
            return convo;
        });

        const updatedClientData = { ...user.data, conversations: updatedConversations };
        setUser({ ...user, data: updatedClientData });
    }
  },
}));

// Auth initialization hook to be called from App component
export const useAuthInitialization = () => {
  const { setSession, fetchUserData } = useAuthStore();

  React.useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      fetchUserData(session);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      fetchUserData(session);
    });

    return () => subscription.unsubscribe();
  }, [setSession, fetchUserData]);
};