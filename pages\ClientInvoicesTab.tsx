import React, { useState, useMemo } from 'react';
import { useInvoicesByClient } from '../hooks';
import type { Invoice } from '../types';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '../components/ui/Table';
import { Button } from '../components/ui';

const ClientInvoicesTab: React.FC<{ clientId: string }> = ({ clientId }) => {
    const { data: invoices = [], isLoading } = useInvoicesByClient(clientId);
    const [filter, setFilter] = useState<'due' | 'all'>('due');

    const statusColor: { [key: string]: string } = {
        paid: 'bg-green-100 text-green-800',
        open: 'bg-yellow-100 text-yellow-800',
        draft: 'bg-gray-100 text-gray-800',
        uncollectible: 'bg-red-100 text-red-800',
        void: 'bg-gray-100 text-gray-800',
    };

    const filteredInvoices = useMemo(() => {
        if (filter === 'due') {
            return invoices.filter(i => i.status === 'open');
        }
        // 'all' shows everything except drafts
        return invoices.filter(i => i.status !== 'draft');
    }, [invoices, filter]);

    if (isLoading) {
        return <div className="text-center p-10"><i className="fa-solid fa-spinner fa-spin text-2xl text-gray-400"></i></div>;
    }

    return (
        <div className="bg-white border border-gray-300">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
                 <h3 className="text-lg font-semibold text-black">Historique des factures</h3>
                 <div className="flex gap-1">
                    <Button variant={filter === 'due' ? 'primary' : 'secondary'} size="sm" onClick={() => setFilter('due')}>À régler</Button>
                    <Button variant={filter === 'all' ? 'primary' : 'secondary'} size="sm" onClick={() => setFilter('all')}>Tout l'historique</Button>
                 </div>
            </div>
            {invoices.length === 0 ? (
                 <div className="text-center p-10 text-gray-500">Vous n'avez pas encore de factures.</div>
            ) : filteredInvoices.length === 0 ? (
                <div className="text-center p-10 text-gray-500">
                    {filter === 'due' ? "Vous n'avez aucune facture à régler." : "Aucune facture ne correspond à ce filtre."}
                </div>
            ) : (
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Facture ID</TableHead>
                            <TableHead>{filter === 'due' ? "Date d'échéance" : "Date"}</TableHead>
                            <TableHead>Montant</TableHead>
                            <TableHead>Statut</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredInvoices.map((invoice) => {
                            const dateLabel = invoice.status === 'paid' && invoice.paidAt 
                                ? new Date(invoice.paidAt).toLocaleDateString() 
                                : (invoice.status === 'open' && invoice.dueDate 
                                    ? new Date(invoice.dueDate).toLocaleDateString() 
                                    : 'N/A');
                            return (
                                <TableRow key={invoice.id}>
                                    <TableCell className="font-mono text-xs text-gray-800">{invoice.stripeInvoiceId}</TableCell>
                                    <TableCell className="text-gray-600">{dateLabel}</TableCell>
                                    <TableCell className="font-medium text-black">${(invoice.amountTotal / 100).toFixed(2)}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 text-xs font-medium capitalize ${statusColor[invoice.status]}`}>{invoice.status === 'open' ? 'À régler' : invoice.status}</span>
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <div className="flex justify-end items-center gap-2">
                                            {invoice.hostedInvoiceUrl && (
                                                <a href={invoice.hostedInvoiceUrl} target="_blank" rel="noopener noreferrer">
                                                    <Button variant={invoice.status === 'open' ? 'primary' : 'secondary'} size="sm">{invoice.status === 'open' ? 'Payer maintenant' : 'Voir'}</Button>
                                                </a>
                                            )}
                                            {invoice.invoicePdf && (
                                                <a href={invoice.invoicePdf} target="_blank" rel="noopener noreferrer">
                                                     <Button size="sm">Télécharger PDF</Button>
                                                </a>
                                            )}
                                        </div>
                                    </TableCell>
                                </TableRow>
                            );
                        })}
                    </TableBody>
                </Table>
            )}
        </div>
    );
};

export default ClientInvoicesTab;