import { useQuery } from '@tanstack/react-query';
import { PackageService } from '@/lib/supabaseService';
import type { PackageItem, Packages } from '@/types';

export const usePackages = () => {
  return useQuery({
    queryKey: ['packages'],
    queryFn: PackageService.getAll,
    staleTime: 10 * 60 * 1000, // Packages data is relatively static, cache for 10 minutes
  });
};

export const usePackagesByCategory = () => {
  return useQuery({
    queryKey: ['packages', 'by-category'],
    queryFn: async (): Promise<Packages> => {
      const packagesData = await PackageService.getAll();

      // Group packages by category
      const packagesByCategory: Packages = {};
      packagesData.forEach(pkg => {
        if (!packagesByCategory[pkg.categoryId]) {
          packagesByCategory[pkg.categoryId] = [];
        }
        packagesByCategory[pkg.categoryId].push(pkg);
      });

      return packagesByCategory;
    },
    staleTime: 10 * 60 * 1000,
  });
};

export const usePackage = (id: string) => {
  return useQuery({
    queryKey: ['packages', id],
    queryFn: () => PackageService.getById(id),
    enabled: !!id,
  });
};