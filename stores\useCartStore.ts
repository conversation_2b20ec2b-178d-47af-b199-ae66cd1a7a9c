import { create } from 'zustand';
import { ClientService, DiscountCodeService } from '@/lib/supabaseService';
import type { CartItem, PackageItem, Client, DiscountCode } from '@/types';

interface CartState {
  cart: CartItem[];
  isCartOpen: boolean;
  appliedCodes: DiscountCode[];
}

interface CartActions {
  setCart: (cart: CartItem[]) => void;
  setIsCartOpen: (isOpen: boolean) => void;
  addToCart: (
    packageItem: PackageItem,
    category: string,
    finalPrice: number,
    finalBilling: string,
    clientId?: string
  ) => Promise<void>;
  removeFromCart: (cartId: string, clientId?: string) => Promise<void>;
  updateCartInDb: (newCart: CartItem[], clientId: string) => Promise<void>;
  getTotals: () => { subtotal: number; discountAmount: number; total: number };
  clearCart: (clientId?: string) => Promise<void>;
  loadCartForClient: (client: Client | null) => void;
  applyDiscountCode: (code: string) => Promise<{ success: boolean; message: string }>;
  removeDiscountCode: (codeId: string) => void;
}

type CartStore = CartState & CartActions;

export const useCartStore = create<CartStore>((set, get) => ({
  // State
  cart: [],
  isCartOpen: false,
  appliedCodes: [],

  // Actions
  setCart: (cart) => set({ cart }),
  setIsCartOpen: (isOpen) => set({ isCartOpen: isOpen }),

  addToCart: async (packageItem, category, finalPrice, finalBilling, clientId) => {
    const { cart, updateCartInDb } = get();
    const newItem: CartItem = {
      ...packageItem,
      cartId: `${packageItem.id}-${Date.now()}`,
      category,
      finalPrice,
      finalBilling
    };
    const newCart = [...cart, newItem];
    set({ cart: newCart });

    if (clientId) {
      await updateCartInDb(newCart, clientId);
    }
  },

  removeFromCart: async (cartId, clientId) => {
    const { cart, updateCartInDb } = get();
    const newCart = cart.filter(item => item.cartId !== cartId);
    set({ cart: newCart });

    if (clientId) {
      await updateCartInDb(newCart, clientId);
    }
  },

  updateCartInDb: async (newCart, clientId) => {
    const success = await ClientService.updateCart(clientId, newCart);
    if (!success) {
      console.error('Could not update cart in database');
    }
  },

  getTotals: () => {
    const { cart, appliedCodes } = get();
    const subtotal = cart.reduce((total, item) => total + item.finalPrice, 0);
    
    let discountAmount = 0;
    let currentTotal = subtotal;

    // Apply percentage discounts first
    appliedCodes
      .filter(c => c.type === 'percentage')
      .sort((a, b) => b.value - a.value) // Apply larger percentages first
      .forEach(c => {
        const discountForThisCode = currentTotal * (c.value / 100);
        discountAmount += discountForThisCode;
        currentTotal -= discountForThisCode;
      });

    // Apply fixed amount discounts
    appliedCodes
      .filter(c => c.type === 'fixed')
      .sort((a, b) => b.value - a.value) // Apply larger fixed amounts first
      .forEach(c => {
        const discountForThisCode = Math.min(currentTotal, c.value);
        discountAmount += discountForThisCode;
        currentTotal -= discountForThisCode;
      });

    const total = Math.max(0, subtotal - discountAmount);
    return { subtotal, discountAmount, total };
  },

  clearCart: async (clientId) => {
    const { updateCartInDb } = get();
    set({ cart: [], appliedCodes: [] });

    if (clientId) {
      await updateCartInDb([], clientId);
    }
  },

  loadCartForClient: (client) => {
    set({ cart: client?.cart || [] });
  },

  applyDiscountCode: async (code) => {
    const { appliedCodes } = get();
    const normalizedCode = code.trim().toUpperCase();

    if (appliedCodes.some(c => c.code.toUpperCase() === normalizedCode)) {
      return { success: false, message: 'Discount code already applied.' };
    }

    const discountCode = await DiscountCodeService.getByCode(normalizedCode);
    if (!discountCode) {
      return { success: false, message: 'Invalid discount code.' };
    }
    
    const now = new Date();
    if (discountCode.validFrom && now < new Date(discountCode.validFrom)) {
        return { success: false, message: 'This code is not yet active.' };
    }
    if (discountCode.validTo && now > new Date(discountCode.validTo)) {
        return { success: false, message: 'This code has expired.' };
    }

    if (!discountCode.isStackable && appliedCodes.length > 0) {
      return { success: false, message: 'This code cannot be combined with other discounts.' };
    }
    if (appliedCodes.some(c => !c.isStackable)) {
      return { success: false, message: 'A non-stackable discount is already applied.' };
    }

    set(state => ({ appliedCodes: [...state.appliedCodes, discountCode] }));
    return { success: true, message: `Code "${discountCode.code}" applied successfully.` };
  },

  removeDiscountCode: (codeId) => {
    set(state => ({ appliedCodes: state.appliedCodes.filter(c => c.id !== codeId) }));
  },
}));