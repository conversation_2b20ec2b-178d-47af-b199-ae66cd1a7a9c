import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';

serve(async (req) => {
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const supabase = getSupabaseAdminClient();
        
        // 1. Authenticate the user from the JWT
        const { data: { user }, error: userError } = await supabase.auth.getUser(req.headers.get('Authorization')!.replace('Bearer ', ''));
        if (userError) throw userError;

        // 2. Get the client ID associated with the user
        const { data: client, error: clientError } = await supabase
            .from('clients')
            .select('id')
            .eq('user_id', user.id)
            .single();
        if (clientError) throw clientError;

        // 3. Get the Stripe subscription ID from the request body
        const { subscriptionId } = await req.json();
        if (!subscriptionId) {
            throw new Error('Subscription ID is required.');
        }

        // 4. Verify that the user owns this subscription
        const { data: subscription, error: subError } = await supabase
            .from('subscriptions')
            .select('client_id')
            .eq('stripe_subscription_id', subscriptionId)
            .single();

        if (subError) throw subError;
        
        if (subscription.client_id !== client.id) {
            throw new Error('Permission denied. You do not own this subscription.');
        }

        // 5. Cancel the subscription on Stripe (at period end)
        const stripe = await getStripeInstance();
        await stripe.subscriptions.update(subscriptionId, {
            cancel_at_period_end: true,
        });

        // The webhook `customer.subscription.updated` will handle updating our DB.
        
        return new Response(
            JSON.stringify({ success: true, message: 'Subscription scheduled for cancellation at the end of the current period.' }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error canceling subscription:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
});
