-- TUTO: This script populates your database with demo data.
-- To use it, go to the SQL Editor in your Supabase dashboard and run the entire script
-- AFTER running schema.sql.md, rls.sql.md, and init.sql.md.
-- This will set up a functional demo environment.

-- ---------------------------------------------------------------------------------
-- STEP 1: CREATE DEMO USERS MANUALLY (This is required)
-- ---------------------------------------------------------------------------------
-- Go to your Supabase Dashboard -> Authentication -> Users and click "Add user".
--
-- 1. Create the ADMIN user:
--    - Email: <EMAIL>
--    - Password: adminpass (or a stronger password if your project has a password policy)
--    - IMPORTANT: Ensure "Auto Confirm User" is enabled, or manually confirm the user's email before attempting to log in.
--
-- 2. Create the CLIENT user:
--    - Email: <EMAIL>
--    - Password: clientpass (or a stronger password)
--    - IMPORTANT: Ensure "Auto Confirm User" is enabled.
--
-- After creating the users, you can run this script directly without any changes.
-- ---------------------------------------------------------------------------------

-- Temporarily disable RLS to allow inserting data as a superuser
set session_replication_role = replica;

-- Clear existing data from public tables to ensure a clean slate
delete from billing_options;
delete from billing_types;
delete from categories_metadata;
delete from packages;
delete from service_categories;
delete from clients;
delete from orders;
delete from conversations;
delete from messages;
delete from contact_submissions;
delete from payment_settings;
delete from smtp_settings;
delete from subscriptions;
delete from notifications;
-- Also clear new tables
delete from cart_items;
delete from order_items;
delete from order_discount_codes;

-- Run init script content again to be safe
insert into billing_types (id, name, public_title, description, is_system_type) values
('standard_subscription', 'Standard Subscription', 'Choose Your Billing Cycle', 'Save more with longer commitments. Billed upfront for the chosen period.', true),
('event_bundle', 'Event Bundles', 'Bundle and Save on Events', 'Purchase multiple event packages for a discount.', true),
('none', 'None (One-Time Only)', 'One-Time Purchase', 'This is a single, one-time payment.', true);

insert into billing_options (id, billing_type_id, label, multiplier, discount, benefit_text, is_partner_tier) values
('monthly', 'standard_subscription', 'Monthly', 1, 0, 'Billed monthly', false),
('quarterly', 'standard_subscription', 'Quarterly', 3, 10, 'Billed every 3 months', false),
('half-yearly', 'standard_subscription', 'Half-Yearly', 6, 15, 'Includes 30-day pause option', false),
('yearly', 'standard_subscription', 'Yearly', 12, 20, 'Includes Partner Tier benefits', true);

insert into billing_options (id, billing_type_id, label, multiplier, discount) values
('single_event', 'event_bundle', 'Single Event', 1, 0),
('3_events', 'event_bundle', '3-Event Bundle', 3, 15),
('6_events', 'event_bundle', '6-Event Bundle', 6, 25);

-- 1. INSERT CATEGORIES METADATA
insert into categories_metadata (id, tab_title, page_title, description, billing_type_id) values
('media', 'Media', 'Media & Promotion Packages', 'Amplify your reach in the Voice AI ecosystem with our tailored media packages.', 'standard_subscription'),
('events', 'Events', 'Event Sponsorship & Hosting', 'Showcase your technology and connect with the community through our professionally managed events.', 'event_bundle'),
('onlineDemos', 'Conference', 'Voice AI Conference Sponsorship', 'Be a part of the premier Voice AI conference. Early bird pricing available until March 31, 2025!', 'none'),
('alacarte', 'À La Carte', 'À La Carte Services', 'Select individual services to meet your specific marketing and community engagement needs.', 'none'),
('annual', 'Partnerships', 'Annual Partnership Program', 'Become a strategic partner and get dedicated access to our full suite of services at an unbeatable price.', 'standard_subscription');

-- 2. INSERT PACKAGES
insert into packages (id, category_id, name, price, billing, features, cta_text, cta_link, order_index) values
('media-whisper', 'media', 'WHISPER', 300, '/month', ARRAY['Basic Directory Listing', '1 Social Media Mention per Month', 'Quarterly Analytics Report'], null, null, 0),
('media-speak', 'media', 'SPEAK', 800, '/month', ARRAY['Premium Directory Listing', '4 Social Media Mentions per Month', '1 Newsletter Feature per Quarter', 'Monthly Analytics & Strategy Call'], null, null, 1),
('media-scream', 'media', 'SCREAM', 1500, '/month', ARRAY['Top-Tier Directory Listing', 'Weekly Social Media Mentions', 'Monthly Newsletter Feature', 'Bi-weekly Strategy Calls', 'Dedicated Account Manager'], 'Contact Sales', '#contact', 2);

insert into packages (id, category_id, name, price, billing, features, is_addon, order_index) values
('event-online-demo', 'events', 'Online Demo', 1000, 'one-time', ARRAY['1-hour virtual event slot', 'Platform & moderation included', 'Promotion to our network', 'Post-event analytics'], false, 0),
('event-hackathon', 'events', 'Hackathon Sponsorship', 5000, 'one-time', ARRAY['Lead sponsor branding', 'Judging seat', 'API/Tech showcase', 'Access to participant list'], false, 1),
('event-creative-assistant', 'events', 'Creative Assistant', 400, 'one-time', ARRAY['AI-powered creative brief generation', 'Automated event description writing', 'Visual asset suggestions'], true, 2);

insert into packages (id, category_id, name, price, billing, features, order_index) values
('conf-gold', 'onlineDemos', 'Gold Sponsorship', 10000, 'one-time', ARRAY['Large booth space', '20-min speaking slot', 'Logo on all materials', '5 free passes'], 0),
('conf-silver', 'onlineDemos', 'Silver Sponsorship', 5000, 'one-time', ARRAY['Standard booth space', 'Logo on website', '2 free passes'], 1);

insert into packages (id, category_id, name, price, billing, features, order_index) values
('alacarte-newsletter', 'alacarte', 'Newsletter Sponsorship', 500, 'one-time', ARRAY['Dedicated section in one newsletter issue', 'Reach 10,000+ subscribers', 'Includes one banner ad'], 0),
('alacarte-job', 'alacarte', 'Job Board Feature', 250, 'one-time', ARRAY['Job post featured for 30 days', 'Promoted in newsletter', 'Highlighted on job board'], 1);

insert into packages (id, category_id, name, price, billing, features, cta_text, cta_link, order_index) values
('annual-partnership', 'annual', 'Annual Partnership', 20000, '/year', ARRAY['Dedicated Account Manager', 'All features from SCREAM package', 'Priority Event Sponsorship', 'Quarterly Strategy Review'], 'Contact Sales', '/contact', 0);


-- 3. INSERT SERVICE CATEGORIES (for About tab)
insert into service_categories (id, title, description, items, link_to_tab, order_index) values
('a-la-carte', '🎯 À la Carte Services', 'Pick and choose specific services that fit your exact needs and budget.', ARRAY['<strong>Directory Listings:</strong> Premium placement', '<strong>Newsletter Sponsorship:</strong> Reach our audience', '<strong>Job Board Features:</strong> Hire top talent', '<strong>Custom Packages:</strong> Mix and match options'], 'alacarte', 0),
('bundles-discounts', '💰 Bundles & Discounts', 'Save money with longer commitments and event packages.', ARRAY['<strong>Quarterly:</strong> Save on all recurring services', '<strong>Half-Yearly:</strong> Enhanced savings + pause option', '<strong>Yearly:</strong> Maximum savings + partner status', '<strong>Event Bundles:</strong> Multiple event packages with discounts'], null, 1),
('media-packages', '📢 Media Packages', 'Get your voice AI company noticed with our marketing and promotional services.', ARRAY['<strong>WHISPER:</strong> Basic visibility', '<strong>SHOUT:</strong> Enhanced promotion', '<strong>SPEAK:</strong> Premium exposure', '<strong>SCREAM:</strong> Maximum visibility'], 'media', 2),
('event-packages', '🎤 Event Packages', 'Host professional voice AI events and demos to showcase your technology.', ARRAY['<strong>In-Person Events:</strong> Physical gatherings', '<strong>Online Events:</strong> Virtual experiences', '<strong>Hybrid Events:</strong> Best of both worlds', '<strong>Hackathon:</strong> Competitive innovation events'], 'events', 3);


-- 4. INSERT SETTINGS
insert into payment_settings (id, stripe_enabled, stripe_public_key, method_credit_card, method_apple_pay, method_google_pay) values
(1, true, 'pk_test_51H5a6iF9aZ3f3jY9c7b9a5f3e2d1c0b9a8f7e6d5c4b3a2f1e0d9c8b7a6f5e4d3c2b1a0f9e8d7c6b5', true, true, true);

insert into smtp_settings (id, test_mode, server, port, username, password, encryption, from_address, from_name, test_email_recipient) values
(1, true, 'smtp.example.com', 587, 'user', 'password', 'starttls', '<EMAIL>', 'Voice AI Space', '<EMAIL>');

-- 5. INSERT CONTACT SUBMISSIONS
insert into contact_submissions (name, company_name, email, subject, message, submitted_at, is_read, is_archived) values
('Jane Doe', 'Innovate Corp', '<EMAIL>', 'Partnership', 'Hello, I would like to inquire about partnership opportunities. Our company, Innovate Corp, is very interested in the Voice AI space and we believe a collaboration could be mutually beneficial.', now() - interval '1 day', false, false),
('Peter Jones', null, '<EMAIL>', 'Support Request', 'I am having trouble with my account settings. Can someone assist me?', now() - interval '2 days', true, false);


-- ---------------------------------------------------------------------------------
-- STEP 2: ASSOCIATE DATA WITH DEMO USERS DYNAMICALLY
-- ---------------------------------------------------------------------------------
DO $$
DECLARE
  client_user_id uuid;
  admin_user_id uuid;
  client_profile_id uuid;
  admin_profile_id uuid;
  convo_id_1 text;
  order_id_1 text;
BEGIN
  -- Dynamically find user IDs from their emails
  SELECT id INTO client_user_id FROM auth.users WHERE email = '<EMAIL>';
  SELECT id INTO admin_user_id FROM auth.users WHERE email = '<EMAIL>';

  -- Error handling: check if users were found
  IF client_user_id IS NULL THEN
    RAISE EXCEPTION 'Client user ''<EMAIL>'' not found. Please create it in the Supabase Authentication dashboard before running this script.';
  END IF;
  IF admin_user_id IS NULL THEN
    RAISE EXCEPTION 'Admin user ''<EMAIL>'' not found. Please create it in the Supabase Authentication dashboard before running this script.';
  END IF;

  -- Insert CLIENT profile
  INSERT INTO clients (user_id, email, stripe_customer_id, company_name, industry, company_size, website, description, primary_contact, title, phone, address, city, state, zip_code, country, billing_email, billing_contact, billing_phone, account_number, payment_method, payment_method_last4, billing_address, account_status, subscription_tier, member_since, auto_renewal, notifications_product_updates, notifications_event_updates, notifications_newsletter, notifications_account_alerts)
  VALUES (
    client_user_id, '<EMAIL>', 'cus_placeholder_12345', 
    'Example Corp', 'Tech', '11-50 employees', 'https://example.com', 'A leading provider of innovative solutions.', 
    'John Smith', 'CEO', '************', '123 Example St', 'Anytown', 'CA', '12345', 'United States', 
    '<EMAIL>', 'Jane Doe', '************', 'ACC-001', 'Visa **** 4242', '4242', '123 Example St, Anytown, CA 12345', 
    'Active', 'SPEAK', '2023-03-15', true, true, true, true, true
  ) RETURNING id INTO client_profile_id;
  
  -- Insert ADMIN profile
  INSERT INTO clients (user_id, role, email, company_name, primary_contact, account_status)
  VALUES (
      admin_user_id, 'admin', '<EMAIL>', 'Admin', 'Admin User', 'Active'
  ) RETURNING id INTO admin_profile_id;

  -- Insert a past order for the client
  order_id_1 := 'ORD-**********';
  INSERT INTO orders (id, client_id, created_at, subtotal, vat_amount, total, status, stripe_payment_intent_id, stripe_invoice_id)
  VALUES (
    order_id_1, client_profile_id, '2023-03-15 12:00:00+00', 800, 160, 960, 'Completed', 'pi_demo_123456789', 'in_demo_123456789'
  );
  
  -- Insert order items for the past order
  INSERT INTO order_items (order_id, package_id, name, price, billing, features, final_price, final_billing, category)
  VALUES(
    order_id_1, 'media-speak', 'SPEAK', 800, '/month', ARRAY['Premium Directory Listing', '4 Social Media Mentions per Month', '1 Newsletter Feature per Quarter', 'Monthly Analytics & Strategy Call'], 800, '/month', 'media'
  );


  -- Insert conversations and messages for the client
  convo_id_1 := 'CONV-1';
  INSERT INTO conversations (id, client_id, subject, last_updated, admin_has_unread) VALUES
  (convo_id_1, client_profile_id, 'Question about my subscription', now() - interval '1 day', true);

  INSERT INTO messages (id, conversation_id, sender, content, created_at) VALUES
  ('m1', convo_id_1, 'client', 'Hi, when is my next billing date?', now() - interval '1 day');

  -- Insert Subscriptions for the client
  INSERT INTO subscriptions (client_id, stripe_subscription_id, status, current_period_start, current_period_end)
  VALUES (
      client_profile_id, 'sub_demo_active_123', 'active', now() - interval '15 days', now() + interval '15 days'
  );

  INSERT INTO subscriptions (client_id, stripe_subscription_id, status, current_period_start, current_period_end, last_payment_error_message)
  VALUES (
      client_profile_id, 'sub_demo_failed_456', 'past_due', now() - interval '10 days', now() + interval '20 days', 'Your card has expired.'
  );

  INSERT INTO subscriptions (client_id, stripe_subscription_id, status, current_period_start, current_period_end, canceled_at)
  VALUES (
      client_profile_id, 'sub_demo_canceled_789', 'canceled', now() - interval '40 days', now() - interval '10 days', now() - interval '10 days'
  );

  -- Insert Notifications for the admin
  INSERT INTO notifications (client_id, type, message, stripe_subscription_id, created_at)
  VALUES (
      client_profile_id,
      'payment_failed',
      'Payment failed for Example Corp. Reason: Your card has expired.',
      'sub_demo_failed_456',
      now() - interval '1 hour'
  );
  
  INSERT INTO notifications (client_id, type, message, stripe_subscription_id, created_at)
  VALUES (
      client_profile_id,
      'subscription_canceled',
      'Subscription for Example Corp was canceled.',
      'sub_demo_canceled_789',
      now() - interval '10 days'
  );

END $$;


-- Re-enable RLS
set session_replication_role = 'origin';

-- Final message
SELECT 'Demo data has been successfully inserted.';