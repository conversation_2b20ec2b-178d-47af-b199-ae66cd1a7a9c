import React from 'react';
import { Input } from './Input';
import { Textarea } from './Textarea';
import { Select } from './Select';

interface FormFieldProps {
  label: string;
  name: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  type?: string;
  as?: 'input' | 'textarea' | 'select';
  rows?: number;
  options?: string[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
  children?: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  value,
  onChange,
  type = 'text',
  as = 'input',
  rows,
  options,
  placeholder,
  required = false,
  disabled = false,
  error,
  className,
  children,
}) => (
  <div className={className}>
    <label className="block text-sm font-medium text-gray-700 mb-1">{label}{required && ' *'}</label>
    {as === 'textarea' ? (
      <Textarea
        name={name}
        value={value}
        onChange={onChange}
        rows={rows || 3}
        required={required}
        disabled={disabled}
        placeholder={placeholder}
      />
    ) : as === 'select' ? (
      <Select
        name={name}
        value={value}
        onChange={onChange}
        required={required}
        disabled={disabled}
      >
        {children ?? options?.map(opt => <option key={opt} value={opt}>{opt}</option>)}
      </Select>
    ) : (
      <Input
        type={type}
        name={name}
        value={value}
        onChange={onChange}
        required={required}
        disabled={disabled}
        placeholder={placeholder}
      />
    )}
    {error && <p className="text-red-600 text-xs mt-1">{error}</p>}
  </div>
);

export default FormField;