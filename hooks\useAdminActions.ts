import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ClientService,
  OrderService,
  PackageService,
  MessageService,
  ConversationService,
  ContactSubmissionService,
  CategoryMetadataService,
  BillingTypeService,
  ServiceCategoryService,
  AdminService,
  DiscountCodeService,
  EmailTemplateService
} from '@/lib/supabaseService';
import { supabase } from '@/lib/supabaseClient';

export const useAdminActions = () => {
  const queryClient = useQueryClient();

  // Generic CRUD operations
  const updateRecord = useMutation({
    mutationFn: async ({ tableName, data, id }: { tableName: string; data: any; id: string }) => {
      switch (tableName) {
        case 'clients':
          return ClientService.updateProfile(id, data.profile);
        case 'orders':
          return OrderService.update(id, data);
        case 'packages':
          return PackageService.update(id, data);
        case 'contact_submissions':
          return ContactSubmissionService.update(id, data);
        case 'categories_metadata':
          return CategoryMetadataService.update(id, data);
        case 'billing_types':
          return BillingTypeService.update(id, data);
        case 'service_categories':
          return ServiceCategoryService.update(id, data);
        case 'payment_settings':
          return AdminService.updatePaymentSettings(data);
        case 'smtp_settings':
          return AdminService.updateSmtpSettings(data);
        case 'discount_codes':
            return DiscountCodeService.update(id, data);
        case 'email_templates':
            return EmailTemplateService.update(id, data);
        default:
          throw new Error(`Unknown table: ${tableName}`);
      }
    },
    onSuccess: (_, { tableName, id }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: [tableName] });
      if (id) {
        queryClient.invalidateQueries({ queryKey: [tableName, id] });
      }
    },
  });

  const createRecord = useMutation({
    mutationFn: async ({ tableName, data }: { tableName: string; data: any }) => {
      switch (tableName) {
        case 'clients':
          return ClientService.create(data);
        case 'orders':
          return OrderService.create(data);
        case 'packages':
          return PackageService.create(data);
        case 'contact_submissions':
          return ContactSubmissionService.create(data);
        case 'categories_metadata':
          return CategoryMetadataService.create(data);
        case 'billing_types':
          return BillingTypeService.create(data);
        case 'service_categories':
            return ServiceCategoryService.create(data);
        case 'discount_codes':
            return DiscountCodeService.create(data);
        default:
          throw new Error(`Unknown table: ${tableName}`);
      }
    },
    onSuccess: (data, { tableName }) => {
      queryClient.invalidateQueries({ queryKey: [tableName] });
    },
  });

  const deleteRecord = useMutation({
    mutationFn: async ({ tableName, id }: { tableName: string; id: string }) => {
      switch (tableName) {
        case 'clients':
          return ClientService.delete(id);
        case 'orders':
          return OrderService.delete(id);
        case 'packages':
          if (supabase) {
            const { error: archiveError } = await supabase.functions.invoke('stripe-product-archive', {
              body: { packageId: id },
            });
            if (archiveError) {
              console.warn(`Could not archive stripe product for package ${id}:`, archiveError.message);
            }
          }
          return PackageService.delete(id);
        case 'contact_submissions':
          return ContactSubmissionService.delete(id);
        case 'categories_metadata':
          return CategoryMetadataService.delete(id);
        case 'billing_types':
          return BillingTypeService.delete(id);
        case 'service_categories':
            return ServiceCategoryService.delete(id);
        case 'discount_codes':
            return DiscountCodeService.delete(id);
        default:
          throw new Error(`Unknown table: ${tableName}`);
      }
    },
    onSuccess: (_, { tableName, id }) => {
      queryClient.invalidateQueries({ queryKey: [tableName] });
      queryClient.removeQueries({ queryKey: [tableName, id] });
    },
  });

  // Messaging operations
  const sendMessage = useMutation({
    mutationFn: async ({ clientId, conversationId, content }: { clientId: string; conversationId: string; content: string }) => {
      return MessageService.sendFromAdmin(conversationId, content);
    },
    onSuccess: (_, { clientId, conversationId }) => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: ['conversations', conversationId] });
      queryClient.invalidateQueries({ queryKey: ['clients', clientId] });
    },
  });

  const markConversationAsRead = useMutation({
    mutationFn: async ({ clientId, conversationId }: { clientId: string; conversationId: string }) => {
      return ConversationService.markAsReadByAdmin(conversationId);
    },
    onSuccess: (_, { clientId, conversationId }) => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: ['conversations', conversationId] });
      queryClient.invalidateQueries({ queryKey: ['clients', clientId] });
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    },
  });

  return {
    updateRecord: (tableName: string, data: any, id: string) =>
      updateRecord.mutateAsync({ tableName, data, id }),
    createRecord: (tableName: string, data: any) =>
      createRecord.mutateAsync({ tableName, data }),
    deleteRecord: (tableName: string, id: string) =>
      deleteRecord.mutateAsync({ tableName, id }),
    sendMessage: (clientId: string, conversationId: string, content: string) =>
      sendMessage.mutateAsync({ clientId, conversationId, content }),
    markConversationAsRead: (clientId: string, conversationId: string) =>
      markConversationAsRead.mutateAsync({ clientId, conversationId }),
  };
};