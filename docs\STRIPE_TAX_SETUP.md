# Configuration Stripe Tax - Guide Complet

## Problème Actuel

```
Error: You must have a valid origin address to enable automatic tax calculation in test mode.
Visit https://dashboard.stripe.com/test/settings/tax to update it.
```

## Solution Temporaire Appliquée

Le calcul automatique de taxe a été désactivé temporairement dans `stripe-payment-intent/index.ts` pour permettre les tests.

```javascript
// automatic_tax: { enabled: true }, // Disabled until origin address is configured in Stripe Dashboard
```

## Configuration Complète de Stripe Tax

### Étape 1 : Configurer l'Adresse d'Origine

1. **Aller dans le Dashboard Stripe** : https://dashboard.stripe.com/test/settings/tax
2. **Ajouter une adresse d'entreprise** :
   - Nom de l'entreprise : VoiceAI Space
   - Adresse complète avec code postal
   - Pays et état/province
   - Cette adresse sert de point de départ pour le calcul des taxes

### Étape 2 : Enregistrer les Juridictions Fiscales

1. **Ajouter les enregistrements fiscaux** dans les régions où vous vendez
2. **Exemples courants** :
   - États-Unis : Sales Tax (par État)
   - Europe : TVA (par pays)
   - Canada : GST/HST/PST (par province)

### Étape 3 : Configurer les Produits et Prix

#### A. Configurer les Tax Codes
```javascript
// Lors de la création de produits
await stripe.products.create({
  name: 'VoiceAI Service',
  tax_code: 'txcd_10103001', // Software as a Service
});
```

#### B. Configurer Tax Behavior
```javascript
// Lors de la création de prix
await stripe.prices.create({
  product: 'prod_xxx',
  unit_amount: 2000,
  currency: 'usd',
  tax_behavior: 'exclusive', // ou 'inclusive'
});
```

### Étape 4 : Réactiver le Calcul Automatique

Après configuration, réactiver dans `stripe-payment-intent/index.ts` :

```javascript
const subscription = await stripe.subscriptions.create({
  customer: customerId,
  items: subscriptionItems,
  payment_behavior: 'default_incomplete',
  expand: ['latest_invoice.payment_intent'],
  automatic_tax: { enabled: true }, // Réactivé après configuration
  metadata,
});
```

### Étape 5 : Mise à Jour du Code Client

Assurer que les clients ont des adresses valides :

```javascript
// Dans stripe-customer-create
await stripe.customers.create({
  email: email,
  name: name,
  address: {
    line1: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    postal_code: '94105',
    country: 'US',
  },
  tax: {
    validate_location: 'immediately'
  }
});
```

## Configuration des Tax Codes Recommandés

| Service | Tax Code | Description |
|---------|----------|-------------|
| SaaS Software | `txcd_10103001` | Software as a Service |
| Digital Services | `txcd_10401400` | Digital products |
| Consulting | `txcd_10401300` | Professional services |
| Setup Fee | `txcd_10401300` | One-time service fee |

## Tax Behavior

- **Exclusive** : Le prix affiché + taxes (typique aux États-Unis)
- **Inclusive** : Le prix affiché inclut les taxes (typique en Europe)

## Test avec Adresses de Test

```javascript
// Test US (Sales Tax)
address: {
  line1: '123 Main St',
  city: 'San Francisco',
  state: 'CA',
  postal_code: '94105',
  country: 'US'
}

// Test EU (TVA)
address: {
  line1: '1 Rue de la Paix',
  city: 'Paris',
  postal_code: '75001',
  country: 'FR'
}

// Test Canada (GST/HST)
address: {
  line1: '123 Queen St',
  city: 'Toronto',
  state: 'ON',
  postal_code: 'M5H 2M9',
  country: 'CA'
}
```

## Vérification du Bon Fonctionnement

1. **Tester les calculs de taxe** avec différentes adresses
2. **Vérifier les factures générées** contiennent les bonnes taxes
3. **Confirmer les rapports fiscaux** dans le Dashboard
4. **Tester les remboursements** avec ajustements de taxe

## Commandes de Déploiement

Après configuration complète :

```bash
# Redéployer avec taxe automatique activée
supabase functions deploy stripe-payment-intent

# Tester le calcul de taxe
curl -X POST "https://ljcbzpxisqegqkgjbkjk.supabase.co/functions/v1/stripe-payment-intent" \
  -H "Authorization: Bearer [ANON_KEY]" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 2000,
    "customerId": "cus_test",
    "cartItems": [{"id": "test", "billing": "/month", "finalPrice": 20}],
    "email": "<EMAIL>"
  }'
```

## Surveillance et Maintenance

- **Surveiller les taux de taxe** : Les taux changent périodiquement
- **Vérifier la conformité** : S'assurer que tous les enregistrements sont à jour
- **Examiner les rapports** : Utiliser les rapports Stripe Tax pour la déclaration

Une fois configuré, Stripe Tax gérera automatiquement :
- ✅ Calcul des taxes en temps réel
- ✅ Application des bons taux selon la localisation
- ✅ Gestion des exemptions et cas spéciaux
- ✅ Rapports pour la déclaration fiscale