import React, { useState } from 'react';
import type { Client, ProfileData } from '@/types';
import Form<PERSON>ield from '@/components/ui/FormField';
import { ToggleSwitch } from '@/components/ui';
import { Button } from '@/components/ui';

interface ProfileTabProps {
  client: Client;
  setProfileData: (updater: (prevData: ProfileData) => ProfileData) => void;
}

const ProfileTab: React.FC<ProfileTabProps> = ({ client, setProfileData }) => {
  const [isEditingProfile, setIsEditingProfile] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };

  const handleNotificationToggle = (key: keyof ProfileData['notifications']) => {
    setProfileData(prev => ({
      ...prev,
      notifications: { ...prev.notifications, [key]: !prev.notifications[key] }
    }));
  };
  
  const ProfileFieldDisplay: React.FC<{ label: string, value: string }> = ({ label, value }) => (
    <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
        <p className="text-sm text-black bg-gray-100 p-2 min-h-[38px]">{value}</p>
    </div>
  );

  return (
    <div className="max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold text-black">Company Profile</h1>
        <button
          onClick={() => setIsEditingProfile(!isEditingProfile)}
          className="px-4 py-2 bg-black text-white text-sm font-medium hover:bg-gray-800 transition-colors"
        >
          {isEditingProfile ? 'Save Changes' : 'Edit Profile'}
        </button>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white border border-gray-300 p-6">
            <div className="flex items-center gap-3 mb-4">
              <i className="fa-solid fa-building w-5 h-5 text-gray-500"></i>
              <h3 className="text-lg font-semibold text-black">Company Information</h3>
            </div>
            <div className="grid gap-4 md:grid-cols-2">
                {isEditingProfile ? (
                    <>
                        <FormField label="Company Name" name="companyName" value={client.profile.companyName} onChange={handleInputChange} />
                        <FormField label="Industry" name="industry" value={client.profile.industry} onChange={handleInputChange} />
                        <FormField label="Company Size" name="companySize" value={client.profile.companySize} onChange={handleInputChange} as="select" options={['1-10 employees', '11-50 employees', '50-100 employees', '101-500 employees', '500+ employees']} />
                        <FormField label="Website" name="website" value={client.profile.website} onChange={handleInputChange} type="url" />
                        <div className="md:col-span-2">
                            <FormField label="Company Description" name="description" value={client.profile.description} onChange={handleInputChange} as="textarea" />
                        </div>
                    </>
                ) : (
                    <>
                        <ProfileFieldDisplay label="Company Name" value={client.profile.companyName} />
                        <ProfileFieldDisplay label="Industry" value={client.profile.industry} />
                        <ProfileFieldDisplay label="Company Size" value={client.profile.companySize} />
                        <ProfileFieldDisplay label="Website" value={client.profile.website} />
                        <div className="md:col-span-2">
                            <ProfileFieldDisplay label="Company Description" value={client.profile.description} />
                        </div>
                    </>
                )}
            </div>
          </div>
          <div className="bg-white border border-gray-300 p-6">
             <div className="flex items-center gap-3 mb-4">
               <i className="fa-solid fa-user w-5 h-5 text-gray-500"></i>
               <h3 className="text-lg font-semibold text-black">Primary Contact</h3>
             </div>
             <div className="grid gap-4 md:grid-cols-2">
                {isEditingProfile ? (
                    <>
                        <FormField label="Full Name" name="primaryContact" value={client.profile.primaryContact} onChange={handleInputChange} />
                        <FormField label="Title" name="title" value={client.profile.title} onChange={handleInputChange} />
                        <FormField label="Email" name="email" value={client.profile.email} onChange={handleInputChange} type="email" />
                        <FormField label="Phone" name="phone" value={client.profile.phone} onChange={handleInputChange} type="tel" />
                    </>
                ) : (
                    <>
                        <ProfileFieldDisplay label="Full Name" value={client.profile.primaryContact} />
                        <ProfileFieldDisplay label="Title" value={client.profile.title} />
                        <ProfileFieldDisplay label="Email" value={client.profile.email} />
                        <ProfileFieldDisplay label="Phone" value={client.profile.phone} />
                    </>
                )}
             </div>
          </div>
          <div className="bg-white border border-gray-300 p-6">
              <div className="flex items-center gap-3 mb-4">
                <i className="fa-solid fa-map-pin w-5 h-5 text-gray-500"></i>
                <h3 className="text-lg font-semibold text-black">Company Address</h3>
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                {isEditingProfile ? (
                    <>
                        <div className="md:col-span-2"><FormField label="Street Address" name="address" value={client.profile.address} onChange={handleInputChange} /></div>
                        <FormField label="City" name="city" value={client.profile.city} onChange={handleInputChange} />
                        <FormField label="State/Province" name="state" value={client.profile.state} onChange={handleInputChange} />
                        <FormField label="ZIP/Postal Code" name="zipCode" value={client.profile.zipCode} onChange={handleInputChange} />
                        <FormField label="Country" name="country" value={client.profile.country} onChange={handleInputChange} as="select" options={['United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Australia', 'Other']} />
                    </>
                ) : (
                    <>
                         <div className="md:col-span-2"><ProfileFieldDisplay label="Street Address" value={client.profile.address} /></div>
                         <ProfileFieldDisplay label="City" value={client.profile.city} />
                         <ProfileFieldDisplay label="State/Province" value={client.profile.state} />
                         <ProfileFieldDisplay label="ZIP/Postal Code" value={client.profile.zipCode} />
                         <ProfileFieldDisplay label="Country" value={client.profile.country} />
                    </>
                )}
              </div>
          </div>
           <div className="bg-white border border-gray-300 p-6">
                <div className="flex items-center gap-3 mb-4">
                  <i className="fa-solid fa-credit-card w-5 h-5 text-gray-500"></i>
                  <h3 className="text-lg font-semibold text-black">Billing Information</h3>
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                    {isEditingProfile ? (
                        <>
                            <FormField label="Billing Email" name="billingEmail" value={client.profile.billingEmail} onChange={handleInputChange} type="email"/>
                            <FormField label="Billing Contact" name="billingContact" value={client.profile.billingContact} onChange={handleInputChange} />
                            <FormField label="Billing Phone" name="billingPhone" value={client.profile.billingPhone} onChange={handleInputChange} type="tel" />
                            <ProfileFieldDisplay label="Account Number" value={client.profile.accountNumber} />
                            <ProfileFieldDisplay label="Payment Method" value={client.profile.paymentMethod} />
                            <ProfileFieldDisplay label="Billing Address" value={client.profile.billingAddress} />
                        </>
                    ) : (
                        <>
                            <ProfileFieldDisplay label="Billing Email" value={client.profile.billingEmail}/>
                            <ProfileFieldDisplay label="Billing Contact" value={client.profile.billingContact} />
                            <ProfileFieldDisplay label="Billing Phone" value={client.profile.billingPhone} />
                            <ProfileFieldDisplay label="Account Number" value={client.profile.accountNumber} />
                            <ProfileFieldDisplay label="Payment Method" value={client.profile.paymentMethod} />
                            <ProfileFieldDisplay label="Billing Address" value={client.profile.billingAddress} />
                        </>
                    )}
                </div>
           </div>
        </div>
        <div className="space-y-6">
          <div className="bg-white border border-gray-300 p-6">
            <h3 className="text-lg font-semibold mb-4 text-black">Notifications</h3>
            <div className="space-y-3">
              {(Object.keys(client.profile.notifications) as Array<keyof ProfileData['notifications']>).map((key) => (
                <ToggleSwitch
                  key={key}
                  label={key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  enabled={client.profile.notifications[key]}
                  onChange={() => handleNotificationToggle(key)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileTab;