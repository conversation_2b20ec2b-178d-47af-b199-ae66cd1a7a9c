import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useCartStore } from '../stores/useCartStore';

const PromoCodeHandler = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { applyDiscountCode, cart } = useCartStore();

  useEffect(() => {
    const promoCode = searchParams.get('promo');
    // Only apply if there's something in the cart and a code is present
    if (promoCode && cart.length > 0) {
      applyDiscountCode(promoCode).then(result => {
        // In a real app, you might show a toast notification here
        console.log(`Promo code application result: ${result.message}`);
        // Clean up URL by removing the promo parameter after attempting to apply it
        searchParams.delete('promo');
        setSearchParams(searchParams, { replace: true });
      });
    }
  }, [searchParams, setSearchParams, applyDiscountCode, cart.length]);

  return null; // This component does not render anything
};

export default PromoCodeHandler;
