export interface PackageItem {
  id: string;
  categoryId: string;
  name: string;
  price: number;
  billing: '/month' | '/year' | 'one-time';
  features: string[];
  isAddon?: boolean;
  ctaText?: string;
  ctaLink?: string;
  orderIndex?: number;
  isEnabled?: boolean;
  validFrom?: string | null;
  validTo?: string | null;
}

export type PackageCategory = 'media' | 'events' | 'onlineDemos' | 'alacarte' | 'annual';

export interface Packages {
  [key: string]: PackageItem[];
}

export interface CategoryMetadata {
  id: string;
  tabTitle: string;
  pageTitle: string;
  description: string;
  billingType: string;
  isEnabled?: boolean;
  validFrom?: string | null;
  validTo?: string | null;
  orderIndex?: number;
}

export interface CategoriesMetadata {
  [key: string]: CategoryMetadata;
}

export interface ServiceCategoryItem {
  id: string;
  title: string;
  description?: string;
  items: string[];
  linkToTab?: string;
  orderIndex?: number;
  isEnabled?: boolean;
}