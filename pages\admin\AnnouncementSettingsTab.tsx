import React, { useState, useEffect } from 'react';
import type { AnnouncementSettings } from '../../types';
import { useAnnouncementSettings, useUpdateAnnouncementSettings } from '../../hooks';
import { Button, FormField, ToggleSwitch } from '../../components/ui';

const formatInputForIso = (inputString?: string): string | null => {
    if (!inputString) return null;
    return new Date(inputString).toISOString();
};

const formatIsoForInput = (isoString?: string | null): string => {
    if (!isoString) return '';
    try {
      const date = new Date(isoString);
      return date.toISOString().slice(0, 16);
    } catch (e) {
      return '';
    }
};

const AnnouncementSettingsTab = () => {
    const { data: settings, isLoading } = useAnnouncementSettings();
    const updateSettingsMutation = useUpdateAnnouncementSettings();

    const [formData, setFormData] = useState<AnnouncementSettings>({
        isEnabled: false, message: '', startDate: null, endDate: null
    });

    useEffect(() => {
        if (settings) {
            setFormData(settings);
        }
    }, [settings]);

    const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        if (type === 'datetime-local') {
            setFormData(prev => ({ ...prev, [name]: value ? formatInputForIso(value) : null }));
        } else {
            setFormData(prev => ({ ...prev, [name]: value }));
        }
    };

    const handleSave = () => {
        updateSettingsMutation.mutate(formData, {
            onSuccess: () => alert('Announcement settings saved successfully!'),
            onError: () => alert('Failed to save settings.'),
        });
    };

    if (isLoading) {
        return <div className="text-center p-10"><i className="fa-solid fa-spinner fa-spin text-2xl text-gray-400"></i></div>;
    }

    return (
        <div className="bg-white border border-gray-300 p-6 space-y-6">
            <h3 className="text-xl font-semibold text-black">Announcement Banner</h3>
            <ToggleSwitch 
                label="Enable Banner" 
                enabled={formData.isEnabled} 
                onChange={() => setFormData(p => ({ ...p, isEnabled: !p.isEnabled }))} 
                description="Display a site-wide announcement banner at the top of the page."
            />
            <FormField 
                label="Banner Message (HTML allowed)" 
                as="textarea" 
                name="message" 
                value={formData.message} 
                onChange={handleFieldChange}
                placeholder='e.g., <p>Special offer! Get 20% off on all <a href="/#media">Media Packages</a> this month.</p>'
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField 
                    label="Start Date (Optional)" 
                    name="startDate" 
                    type="datetime-local" 
                    value={formatIsoForInput(formData.startDate)} 
                    onChange={handleFieldChange} 
                />
                <FormField 
                    label="End Date (Optional)" 
                    name="endDate" 
                    type="datetime-local" 
                    value={formatIsoForInput(formData.endDate)} 
                    onChange={handleFieldChange} 
                />
            </div>
            <Button 
                onClick={handleSave} 
                isLoading={updateSettingsMutation.isPending}
                disabled={updateSettingsMutation.isPending}
            >
                {updateSettingsMutation.isPending ? 'Saving...' : 'Save Announcement Settings'}
            </Button>
        </div>
    );
};

export default AnnouncementSettingsTab;
