import React, { useState, useEffect } from 'react';
import { SidePanel, Button, FormField, ToggleSwitch } from '../../components/ui';
import type { PackageItem } from '../../types';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../../lib/supabaseClient';
import { useToastStore } from '../../stores/useToastStore';

// Helper functions
const formatIsoForInput = (isoString?: string | null): string => {
  if (!isoString) return '';
  try {
    const date = new Date(isoString);
    // Format to YYYY-MM-DDTHH:MM, which is required by datetime-local input
    return date.toISOString().slice(0, 16);
  } catch (e) {
    return ''; // Handle invalid date strings gracefully
  }
};

const formatInputForIso = (inputString?: string): string | null => {
    if (!inputString) return null;
    return new Date(inputString).toISOString();
};

interface PackageEditorDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (pkg: PackageItem & { category_id: string }, isNew: boolean) => Promise<void>;
    packageToEdit: Partial<PackageItem> | null;
    category: string;
    allPackagesInCategory: PackageItem[];
    stripeProductInfo?: { stripe_product_id: string; stripe_price_id: string };
}

const PackageEditorDrawer: React.FC<PackageEditorDrawerProps> = ({
    isOpen,
    onClose,
    onSave,
    packageToEdit,
    category,
    allPackagesInCategory,
    stripeProductInfo
}) => {
    const [editablePackage, setEditablePackage] = useState<Partial<PackageItem> | null>(null);
    const [isSyncing, setIsSyncing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const { showToast } = useToastStore();
    const queryClient = useQueryClient();

    const isNew = !packageToEdit?.id;

    useEffect(() => {
        if (packageToEdit) {
            setEditablePackage({ ...packageToEdit, features: packageToEdit.features || [''] });
        }
    }, [packageToEdit]);

    const handleSaveClick = async () => {
        if (!editablePackage || !editablePackage.name) return;
        setIsSaving(true);
        try {
            const packageToSave = { ...editablePackage, category_id: category };
            await onSave(packageToSave as PackageItem & { category_id: string }, isNew);
            setIsSaving(false);
            onClose();
        } catch (saveError: any) {
            console.error("Failed to save package", saveError);
            showToast(`Error: ${saveError.message || 'Failed to save package changes.'}`, 'error');
            setIsSaving(false);
        }
    };
    
    const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (!editablePackage) return;
        const { name, value, type } = e.target;
        let finalValue: any = value;
        if (type === 'number') finalValue = Number(value);
        if (type === 'datetime-local') finalValue = formatInputForIso(value);
        setEditablePackage({ ...editablePackage, [name]: finalValue });
    };

    const handleToggleChange = (name: 'isEnabled' | 'isAddon') => {
        if (!editablePackage) return;
        setEditablePackage(prev => prev ? { ...prev, [name]: !prev[name] } : null);
    };

    const handleFeatureChange = (featureIndex: number, value: string) => {
        if (!editablePackage || !editablePackage.features) return;
        const updatedFeatures = [...editablePackage.features];
        updatedFeatures[featureIndex] = value;
        setEditablePackage({ ...editablePackage, features: updatedFeatures });
    };
    
    const addFeature = () => {
        if (!editablePackage) return;
        setEditablePackage({ ...editablePackage, features: [...(editablePackage.features || []), ''] });
    };

    const removeFeature = (featureIndex: number) => {
        if (!editablePackage || !editablePackage.features || editablePackage.features.length <= 1) return;
        const updatedFeatures = editablePackage.features.filter((_, i) => i !== featureIndex);
        setEditablePackage({ ...editablePackage, features: updatedFeatures });
    };
    
    const handleSyncStripe = async () => {
        if (!editablePackage || !editablePackage.id) return;
        setIsSyncing(true);
        try {
            const { data, error } = await supabase.functions.invoke('stripe-product-sync', {
                body: { packageId: editablePackage.id },
            });
            if (error) throw error;
            if (data.error) throw new Error(data.error);
            showToast(data.message || 'Successfully synced with Stripe!', 'success');
            await queryClient.invalidateQueries({ queryKey: ['stripe-products'] });
        } catch (err: any) {
            console.error('Failed to sync with Stripe', err);
            showToast(`Stripe Sync Failed: ${err.message}`, 'error');
        } finally {
            setIsSyncing(false);
        }
    };

    if (!isOpen || !editablePackage) return null;

    const renderFooter = () => (
        <div className="flex justify-between items-center w-full">
            <div className="flex gap-2">
                <Button onClick={handleSaveClick} isLoading={isSaving} disabled={isSaving}>
                    <i className="fa-solid fa-check mr-2"></i> {isNew ? 'Create' : 'Save'}
                </Button>
                <Button onClick={onClose} variant="secondary" disabled={isSaving}>
                    Cancel
                </Button>
            </div>
            {!isNew && (
                 <Button onClick={handleSyncStripe} size="sm" variant="secondary" isLoading={isSyncing} disabled={isSyncing} className="text-xs px-2 py-1">
                    {isSyncing ? 'Syncing...' : (stripeProductInfo ? 'Re-sync Stripe' : 'Sync to Stripe')}
                </Button>
            )}
        </div>
    );
    
    return (
        <SidePanel 
            isOpen={isOpen} 
            onClose={onClose} 
            title={isNew ? 'Create New Package' : `Edit: ${packageToEdit?.name}`}
            size="2xl"
            footer={renderFooter()}
        >
            <div className="space-y-4">
              <FormField label="Name" name="name" value={editablePackage.name || ''} onChange={handleFieldChange} />
              <div className="grid grid-cols-2 gap-4">
                <FormField label="Price" name="price" type="number" value={editablePackage.price || 0} onChange={handleFieldChange} />
                <FormField label="Billing" name="billing" as="select" value={editablePackage.billing || 'one-time'} onChange={handleFieldChange} options={['one-time', '/month', '/year']} />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField label="CTA Button Text" name="ctaText" value={editablePackage.ctaText || ''} onChange={handleFieldChange} placeholder="e.g., Learn More"/>
                <FormField label="CTA Button Link" name="ctaLink" value={editablePackage.ctaLink || ''} onChange={handleFieldChange} placeholder="e.g., /contact"/>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField label="Visible From (Optional)" name="validFrom" type="datetime-local" value={formatIsoForInput(editablePackage.validFrom)} onChange={handleFieldChange} />
                <FormField label="Visible Until (Optional)" name="validTo" type="datetime-local" value={formatIsoForInput(editablePackage.validTo)} onChange={handleFieldChange} />
              </div>
              <div className="space-y-2 pt-2 border-t border-gray-200">
                <ToggleSwitch label="Enabled" enabled={!!editablePackage.isEnabled} onChange={() => handleToggleChange('isEnabled')} description="If disabled, this package is hidden from clients." />
                <ToggleSwitch label="Is Add-on?" enabled={!!editablePackage.isAddon} onChange={() => handleToggleChange('isAddon')} description="Add-on packages are offered alongside primary packages." />
              </div>
              <div className="pt-2 border-t border-gray-200">
                <label className="text-sm font-medium text-gray-700 mb-2 block">Features</label>
                <div className="space-y-2">
                  {(editablePackage.features || []).map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <input type="text" value={feature} onChange={(e) => handleFeatureChange(idx, e.target.value)} className="w-full px-2 py-1 border border-gray-300 text-sm bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent" />
                      <button onClick={() => removeFeature(idx)} className="text-red-500 p-1 hover:bg-red-50 disabled:text-gray-300 disabled:cursor-not-allowed" disabled={(editablePackage.features || []).length <= 1} title="Remove feature"><i className="fa-solid fa-times"></i></button>
                    </div>
                  ))}
                </div>
                <button onClick={addFeature} className="text-sm text-blue-600 hover:underline mt-2">+ Add Feature</button>
              </div>
              {!isNew && (
                <div className="pt-4 mt-4 border-t border-gray-200">
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Stripe Integration</label>
                  {stripeProductInfo ? (
                    <div className="space-y-2 text-xs p-3 bg-green-50 border border-green-200">
                      <div className="flex justify-between items-start">
                          <p className="font-semibold text-green-800 flex items-center gap-2"><i className="fa-solid fa-check-circle"></i> Synced with Stripe</p>
                      </div>
                      <p><strong>Product ID:</strong> <a href={`https://dashboard.stripe.com/products/${stripeProductInfo.stripe_product_id}`} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline break-all">{stripeProductInfo.stripe_product_id}</a></p>
                      <p><strong>Price ID:</strong> <a href={`https://dashboard.stripe.com/prices/${stripeProductInfo.stripe_price_id}`} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline break-all">{stripeProductInfo.stripe_price_id}</a></p>
                    </div>
                  ) : (
                    <div className="space-y-2 text-xs p-3 bg-yellow-50 border border-yellow-300">
                      <p className="font-semibold text-yellow-800 flex items-center gap-2"><i className="fa-solid fa-triangle-exclamation"></i> Not synced with Stripe</p>
                    </div>
                  )}
                </div>
              )}
          </div>
        </SidePanel>
    );
};

export default PackageEditorDrawer;