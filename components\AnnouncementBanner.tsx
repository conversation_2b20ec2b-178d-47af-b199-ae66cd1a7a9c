import React, { useState, useEffect } from 'react';
import { AnnouncementSettings } from '../types';

interface AnnouncementBannerProps {
  settings?: AnnouncementSettings;
}

const AnnouncementBanner: React.FC<AnnouncementBannerProps> = ({ settings }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Using a versioned key allows invalidating old dismissals if the banner logic changes.
    const isDismissed = sessionStorage.getItem('announcementDismissed_v1');
    if (isDismissed) {
        setIsVisible(false);
        return;
    }

    if (!settings || !settings.isEnabled) {
        setIsVisible(false);
        return;
    }

    const now = new Date();
    const startDate = settings.startDate ? new Date(settings.startDate) : null;
    const endDate = settings.endDate ? new Date(settings.endDate) : null;

    if (startDate && now < startDate) {
        setIsVisible(false);
        return;
    }
    if (endDate && now > endDate) {
        setIsVisible(false);
        return;
    }
    
    setIsVisible(true);

  }, [settings]);

  const handleDismiss = () => {
    sessionStorage.setItem('announcementDismissed_v1', 'true');
    setIsVisible(false);
  };

  if (!isVisible || !settings?.message) {
    return null;
  }

  // A basic sanitizer to prevent javascript: URLs in links.
  const sanitizedMessage = settings.message.replace(/href="javascript:[^"]*"/g, 'href="#"');

  return (
    <div className="bg-black text-white text-sm relative">
        <style>
        {`
            .announcement-content a {
                font-weight: bold;
                text-decoration: underline;
                color: white;
            }
            .announcement-content a:hover {
                text-decoration: none;
                opacity: 0.9;
            }
        `}
        </style>
      <div className="container mx-auto px-12 py-2.5 text-center announcement-content" dangerouslySetInnerHTML={{ __html: sanitizedMessage }} />
      <button 
        onClick={handleDismiss} 
        className="absolute top-1/2 right-4 -translate-y-1/2 p-2 leading-none text-white opacity-70 hover:opacity-100"
        aria-label="Dismiss announcement"
      >
        <i className="fa-solid fa-xmark"></i>
      </button>
    </div>
  );
};

export default AnnouncementBanner;