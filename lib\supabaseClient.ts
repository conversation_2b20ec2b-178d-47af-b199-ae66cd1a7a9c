import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database';

// --- Get credentials from either window.process.env (index.html) or configs.ts ---

// Method 1: from index.html
const envUrl = (window as any).process?.env?.SUPABASE_URL;
const envKey = (window as any).process?.env?.SUPABASE_ANON_KEY;

let supabaseUrl: string | undefined;
let supabaseAnonKey: string | undefined;

// Check if credentials from index.html are valid (not empty and not placeholders)
const isEnvConfigValid = envUrl && envUrl !== 'YOUR_SUPABASE_URL' && envKey && envKey !== 'YOUR_SUPABASE_ANON_KEY';

// Prioritize credentials from index.html if they are valid
if (isEnvConfigValid) {
    supabaseUrl = envUrl;
    supabaseAnonKey = envKey;
}

// --- Initialize Supabase client ---

let supabase: SupabaseClient<Database> | null = null;
let supabaseInitializationError: string | null = null;

if (!supabaseUrl || !supabaseAnonKey) {
    // If neither configuration is valid, set an informative error message.
    supabaseInitializationError = 'Supabase credentials are not configured. Please add your SUPABASE_URL and SUPABASE_ANON_KEY to either `index.html` or `lib/configs.ts`.';
} else {
    try {
        // Initialize the client with the valid credentials found.
        supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
    } catch (e: any) {
        // This catches other potential errors, like a malformed URL.
        supabaseInitializationError = e.message || 'Failed to initialize Supabase client.';
    }
}

export { supabase, supabaseInitializationError };