export type BillingPeriod = 'monthly' | 'quarterly' | 'half-yearly' | 'yearly';
export type EventBundle = 'single' | '3-event' | '6-event' | '12-event';

export interface BillingOption {
  id:string;
  label: string;
  multiplier: number;
  discount: number;
  isPartnerTier?: boolean;
  benefitText?: string;
  isEnabled?: boolean;
  validFrom?: string | null;
  validTo?: string | null;
}

export interface BillingType {
  id: string;
  name: string;
  publicTitle?: string;
  description?: string;
  options: BillingOption[];
  isSystemType?: boolean;
}

export interface DiscountCode {
    id: string;
    code: string;
    type: 'percentage' | 'fixed';
    value: number;
    validFrom: string | null;
    validTo: string | null;
    isStackable: boolean;
    isActive: boolean;
}
