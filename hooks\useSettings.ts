import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminService } from '../lib/supabaseService';
import type { AnnouncementSettings } from '../types';

export const usePaymentSettings = () => {
  return useQuery({
    queryKey: ['payment-settings'],
    queryFn: AdminService.getPaymentSettings,
    staleTime: 5 * 60 * 1000, // Settings change occasionally
  });
};

export const useUpdatePaymentSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: AdminService.updatePaymentSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-settings'] });
    },
  });
};

export const useSmtpSettings = () => {
  return useQuery({
    queryKey: ['smtp-settings'],
    queryFn: AdminService.getSmtpSettings,
    staleTime: 5 * 60 * 1000, // Settings change occasionally
  });
};

export const useUpdateSmtpSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: AdminService.updateSmtpSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['smtp-settings'] });
    },
  });
};

export const useAnnouncementSettings = () => {
  return useQuery({
    queryKey: ['announcement-settings'],
    queryFn: AdminService.getAnnouncementSettings,
    staleTime: 5 * 60 * 1000,
  });
};

export const useUpdateAnnouncementSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: AnnouncementSettings) => AdminService.updateAnnouncementSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcement-settings'] });
    },
  });
};
