import React, { useMemo } from 'react';
import type { PackageItem, CartItem, BillingOption } from '@/types';
import { Card } from '@/components/ui';
import { Button } from '@/components/ui';

interface PackageCardProps {
  packageItem: PackageItem;
  category: string;
  cart: CartItem[];
  addToCart: (packageItem: PackageItem, category: string, finalPrice: number, finalBilling: string) => void;
  selectedBillingOption?: BillingOption;
  isAdmin?: boolean;
  index: number;
  isDragging?: boolean;
  onDragStart: (index: number) => void;
  onDrop: (index: number) => void;
  isDropTarget?: boolean;
  onDragEnter: (index: number) => void;
  onEdit: (pkg: PackageItem) => void;
  onDelete: (id: string) => void;
  stripeProductInfo?: { stripe_product_id: string; stripe_price_id: string };
  onSyncStripe: (id: string) => void;
  isSyncing?: boolean;
}


const PackageCard: React.FC<PackageCardProps> = ({ 
  packageItem, 
  category, 
  cart, 
  addToCart, 
  selectedBillingOption,
  isAdmin,
  index,
  isDragging,
  onDragStart,
  onDrop,
  isDropTarget,
  onDragEnter,
  onEdit,
  onDelete,
  stripeProductInfo,
  onSyncStripe,
  isSyncing,
}) => {
  const isInCart = cart.some(item => item.id === packageItem.id);
  const isPartnerTier = !!(selectedBillingOption?.isPartnerTier && packageItem.billing === '/month' && !packageItem.isAddon);

  const { finalPrice, finalBilling, discountInfo } = useMemo(() => {
    let price = packageItem.price;
    let billing: string = packageItem.billing;
    let discountInfo: { percentage: string; originalPrice: number; dollarSavings: number } | null = null;
    let basePrice = packageItem.price;

    const isEarlyBird = category === 'onlineDemos' && new Date() < new Date('2025-03-31');
    if (isEarlyBird) {
        const originalPrice = basePrice;
        basePrice = Math.round(basePrice * 0.8);
        discountInfo = { 
            percentage: '20% Early Bird', 
            originalPrice: originalPrice,
            dollarSavings: originalPrice - basePrice
        };
    }
    
    if (selectedBillingOption && selectedBillingOption.multiplier > 1) {
        const isApplicable = (packageItem.billing === '/month' || (packageItem.billing === 'one-time' && category === 'events'));
        if (isApplicable && !packageItem.isAddon) {
            const originalTotal = basePrice * selectedBillingOption.multiplier;
            const discountedTotal = Math.round(originalTotal * (1 - selectedBillingOption.discount / 100));
            price = discountedTotal;
            billing = `for ${selectedBillingOption.label}`;
            if (selectedBillingOption.discount > 0) {
                 discountInfo = {
                    percentage: `${selectedBillingOption.discount}%`,
                    originalPrice: originalTotal,
                    dollarSavings: originalTotal - discountedTotal
                };
            }
        }
    } else {
       price = basePrice;
    }

    return { finalPrice: price, finalBilling: billing, discountInfo };
  }, [packageItem, selectedBillingOption, category]);

  const handleAddToCart = () => {
    addToCart(packageItem, category, finalPrice, finalBilling);
  };

  return (
    <Card 
      className={`p-6 flex flex-col h-full hover:border-black transition-all duration-300 relative group ${isDragging ? 'opacity-40 scale-105 shadow-2xl -rotate-2' : ''} ${isDropTarget ? 'ring-2 ring-black ring-offset-2' : ''}`}
      draggable={isAdmin && !packageItem.id.startsWith('new-package-placeholder')}
      onDragStart={() => onDragStart(index)}
      onDragOver={(e) => e.preventDefault()}
      onDragEnter={() => onDragEnter(index)}
      onDrop={(e) => { e.preventDefault(); onDrop(index); }}
    >
      {isAdmin && (
        <div className="absolute top-2 left-2 text-gray-400 cursor-grab p-1 group-hover:opacity-100 opacity-0 transition-opacity" title="Drag to reorder">
          <i className="fa-solid fa-grip-vertical"></i>
        </div>
      )}
      {isAdmin && (
        <div className="absolute top-2 right-2 flex gap-1">
             <button onClick={() => onDelete(packageItem.id)} className="text-gray-400 hover:text-red-600 p-2 bg-white bg-opacity-75 rounded-full z-10 group-hover:opacity-100 opacity-0 transition-opacity leading-none" title="Delete Package">
                <i className="fa-solid fa-trash-can text-xs"></i>
            </button>
            <button onClick={() => onEdit(packageItem)} className="text-gray-400 hover:text-black p-2 bg-white bg-opacity-75 rounded-full z-10 group-hover:opacity-100 opacity-0 transition-opacity leading-none" title="Edit Package">
                <i className="fa-solid fa-pencil text-xs"></i>
            </button>
        </div>
      )}

        <>
          <div className="mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-bold text-black">{packageItem.name}</h3>
                {isAdmin && (
                  <div className="relative" onClick={e => e.stopPropagation()}>
                    {stripeProductInfo ? (
                      <a
                        href={`https://dashboard.stripe.com/products/${stripeProductInfo.stripe_product_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-green-600 hover:text-green-800"
                        title="Synced with Stripe. Click to view product."
                      >
                        <i className="fa-brands fa-stripe"></i>
                      </a>
                    ) : (
                      <button
                        onClick={() => onSyncStripe(packageItem.id)}
                        disabled={isSyncing}
                        className="text-gray-400 hover:text-black disabled:text-gray-300 disabled:cursor-wait"
                        title="Sync with Stripe"
                      >
                        {isSyncing ? <i className="fa-solid fa-spinner fa-spin"></i> : <i className="fa-solid fa-arrows-rotate"></i>}
                      </button>
                    )}
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-600">{packageItem.name === 'Creative Assistant' ? 'Your Event' : 'Voice AI Space'}</p>
              {packageItem.isAddon && <span className="text-xs font-medium bg-gray-100 text-gray-800 px-2 py-1 mt-1 inline-block">ADD-ON</span>}
              {isPartnerTier && <span className="text-xs font-medium bg-yellow-100 text-yellow-800 px-2 py-1 mt-1 inline-block">⭐ Partner Status</span>}
            </div>
          </div>
          <div className="mb-4">
            <div className="flex items-baseline gap-2 mb-1">
              <span className="text-4xl font-extrabold text-black">${finalPrice}</span>
              <span className="text-gray-600 font-medium">{finalBilling}</span>
            </div>
            {discountInfo && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-green-600 font-medium">✓ Save {discountInfo.percentage} (${discountInfo.dollarSavings}){isPartnerTier && ' + Partner Benefits'}</span>
                <span className="text-sm text-gray-500 line-through">${discountInfo.originalPrice}</span>
              </div>
            )}
          </div>
          <div className="mb-6 flex-grow border-t border-gray-200 pt-4">
            <ul className="space-y-3">
              {packageItem.features.map((feature, index) => (
                <li key={index} className="flex items-start text-sm">
                  <i className="fa-solid fa-check text-green-500 mr-2 mt-0.5 shrink-0"></i>
                  <span className="text-gray-600">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
           <div className="mt-auto">
            {packageItem.ctaText && packageItem.ctaLink ? (
              <a href={packageItem.ctaLink} target={packageItem.ctaLink.startsWith('http') ? '_blank' : '_self'} rel="noopener noreferrer" className="w-full mt-auto inline-flex items-center justify-center px-6 py-3 text-sm font-medium transition-colors bg-black text-white hover:bg-gray-800">
                {packageItem.ctaText}
                <i className="fa-solid fa-arrow-right ml-2"></i>
              </a>
            ) : (
              <Button onClick={handleAddToCart} disabled={isInCart} className="w-full mt-auto">
                {isInCart ? (<><i className="fa-solid fa-check w-5 h-5 mr-2"></i>Added to Cart</>) : (<><i className="fa-solid fa-plus w-5 h-5 mr-2"></i>Add to Cart</>)}
              </Button>
            )}
          </div>
        </>
    </Card>
  );
};

export default PackageCard;