import React, { useState, useMemo, useEffect } from 'react';
import type { ContactSubmission } from '../../types';
import { useUpdateContactSubmission, useAdminActions } from '../../hooks';
import { ConfirmationModal } from '../../components/ui';
import { useToastStore } from '../../stores/useToastStore';

type InboxFilter = 'all' | 'archived';

const timeSince = (date: Date): string => {
    const seconds = Math.floor((new Date().getTime() - new Date(date).getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + " years ago";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + " months ago";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + " days ago";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + " hours ago";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + " minutes ago";
    return Math.floor(seconds) + " seconds ago";
}

interface InboxTabProps {
    contactSubmissions: ContactSubmission[];
    activeSubmission: ContactSubmission | null;
    setActiveSubmission: (submission: ContactSubmission | null) => void;
}

const InboxTab: React.FC<InboxTabProps> = ({ contactSubmissions, activeSubmission, setActiveSubmission }) => {
    const [inboxFilter, setInboxFilter] = useState<InboxFilter>('all');
    const updateContactSubmissionMutation = useUpdateContactSubmission();
    const adminActions = useAdminActions();
    const { showToast } = useToastStore();
    const [isDeleting, setIsDeleting] = useState(false);
    
    useEffect(() => {
        if (activeSubmission) {
            const updatedSubmission = contactSubmissions.find(sub => sub.id === activeSubmission.id);
            
            // Check if it's still visible in the current filter
            const isVisibleInFilter = updatedSubmission && 
                (inboxFilter === 'archived' ? updatedSubmission.isArchived : !updatedSubmission.isArchived);

            if (!isVisibleInFilter) {
                setActiveSubmission(null);
            } else if (JSON.stringify(activeSubmission) !== JSON.stringify(updatedSubmission)) {
                setActiveSubmission(updatedSubmission);
            }
        }
    }, [contactSubmissions, activeSubmission, inboxFilter, setActiveSubmission]);

    const handleSubmissionAction = async (id: string, action: 'toggleRead' | 'toggleArchive') => {
        const submission = contactSubmissions.find(sub => sub.id === id);
        if (!submission) return;
  
        const data: Partial<ContactSubmission> = {};
        if (action === 'toggleRead') data.isRead = !submission.isRead;
        if (action === 'toggleArchive') data.isArchived = !submission.isArchived;
  
        try {
          await updateContactSubmissionMutation.mutateAsync({ id, data });
          if(action === 'toggleArchive' && submission.isArchived === false) {
            setActiveSubmission(null);
          }
        } catch (error) {
          console.error("Failed to update submission", error);
          showToast("Failed to update submission status.", 'error');
        }
    };

    const handleSelectSubmission = (submission: ContactSubmission) => {
        setActiveSubmission(submission);
        // Mark as read when it's selected, if it's currently unread.
        if (!submission.isRead) {
          handleSubmissionAction(submission.id, 'toggleRead');
        }
    };

    const handleDeleteSubmission = async (id: string) => {
        setIsDeleting(true);
        try {
            await adminActions.deleteRecord('contact_submissions', id);
            setActiveSubmission(null);
            showToast('Submission deleted successfully.', 'success');
        } catch (error) {
            console.error("Failed to delete submission:", error);
            const message = error instanceof Error ? error.message : String(error);
            showToast(`Failed to delete submission: ${message}`, 'error');
        } finally {
            setIsDeleting(false);
        }
    };

    const filteredSubmissions = useMemo(() => {
        let subs = [...contactSubmissions].sort((a,b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());
        if (inboxFilter === 'archived') {
          return subs.filter(s => s.isArchived);
        }
        return subs.filter(s => !s.isArchived); // 'all' filter shows non-archived
    }, [contactSubmissions, inboxFilter]);
      
    return (
        <>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-350px)]">
              <div className="lg:col-span-1 bg-white border border-gray-300 flex flex-col">
                  <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-black">Inbox</h3>
                      <div className="flex gap-1">
                          {(['all', 'archived'] as InboxFilter[]).map(f => (
                              <button key={f} onClick={() => setInboxFilter(f)} className={`px-2 py-0.5 text-xs border ${inboxFilter === f ? 'bg-black text-white' : 'text-black hover:bg-gray-100'}`}>{f.charAt(0).toUpperCase() + f.slice(1)}</button>
                          ))}
                      </div>
                  </div>
                  <div className="flex-1 overflow-y-auto">
                      {filteredSubmissions.map(sub => (
                          <button key={sub.id} onClick={() => handleSelectSubmission(sub)} className={`w-full text-left border-b border-gray-200 p-4 transition-colors ${!sub.isRead ? 'border-l-4 border-black' : 'border-l-4 border-transparent'} ${activeSubmission?.id === sub.id ? 'bg-gray-100' : 'hover:bg-gray-50'}`}>
                              <div className="flex justify-between items-start">
                                  <h4 className={`font-semibold ${!sub.isRead ? 'text-black' : 'text-gray-800'}`}>{sub.name}</h4>
                                  <span className="text-xs text-gray-500 flex-shrink-0 ml-2">{timeSince(new Date(sub.submittedAt))}</span>
                              </div>
                              <p className="text-sm text-gray-500 truncate mt-1">{sub.subject}</p>
                          </button>
                      ))}
                  </div>
              </div>
              <div className="lg:col-span-2 bg-white border border-gray-300 flex flex-col">
                  {activeSubmission ? (
                      <>
                          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                              <div>
                                  <h3 className="text-lg font-bold text-black">{activeSubmission.subject}</h3>
                                  <p className="text-sm text-gray-600">From: {activeSubmission.name} &lt;{activeSubmission.email}&gt;</p>
                              </div>
                              <div className="flex gap-2">
                                   <button onClick={() => handleSubmissionAction(activeSubmission.id, 'toggleRead')} className="px-3 py-1 text-xs text-black border border-gray-300 hover:bg-gray-100">{activeSubmission.isRead ? 'Mark as Unread' : 'Mark as Read'}</button>
                                   <button onClick={() => handleSubmissionAction(activeSubmission.id, 'toggleArchive')} className="px-3 py-1 text-xs text-black border border-gray-300 hover:bg-gray-100">{activeSubmission.isArchived ? 'Unarchive' : 'Archive'}</button>
                                   <button onClick={() => setIsDeleting(true)} className="px-3 py-1 text-xs border border-red-300 text-red-600 hover:bg-red-50">Delete</button>
                              </div>
                          </div>
                          <div className="flex-1 p-6 overflow-y-auto text-sm whitespace-pre-wrap text-black">{activeSubmission.message}</div>
                      </>
                  ) : (
                      <div className="flex items-center justify-center h-full text-gray-500">Select a submission to read.</div>
                  )}
              </div>
            </div>
            <ConfirmationModal
                isOpen={isDeleting && !!activeSubmission}
                onClose={() => setIsDeleting(false)}
                onConfirm={() => activeSubmission && handleDeleteSubmission(activeSubmission.id)}
                title="Delete Submission"
                message={<p>Are you sure you want to permanently delete this submission from <strong>"{activeSubmission?.name}"</strong>? This action cannot be undone.</p>}
                confirmText="Delete Permanently"
                isLoading={isDeleting}
            />
        </>
    );
};

export default InboxTab;