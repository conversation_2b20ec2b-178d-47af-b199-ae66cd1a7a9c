import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../shared/cors.ts';
import { getStripeInstance, getSupabaseAdminClient } from '../shared/stripe.ts';
import Stripe from 'https://esm.sh/stripe@16.2.0';

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { email, companyName, contactName, phone, address, clientId } = await req.json();
    if (!clientId || !email || !companyName || !contactName) {
        return new Response(
            JSON.stringify({ error: "Invalid input. clientId, email, companyName, and contactName are required." }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }

    const stripe = await getStripeInstance();
    const supabase = getSupabaseAdminClient();

    const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('stripe_customer_id')
        .eq('id', clientId)
        .single();

    if (clientError && clientError.code !== 'PGRST116') throw clientError;

    let customer;
    const stripeCustomerId = clientData?.stripe_customer_id;

    const customerPayload: Stripe.CustomerUpdateParams = {
        email,
        name: companyName,
        phone: phone || undefined,
        address: address || undefined,
        description: `Primary Contact: ${contactName}`,
        metadata: {
          supabase_client_id: clientId,
        },
    };

    if (stripeCustomerId) {
      // If we have a customer ID in our DB, we trust it and update the Stripe customer.
      customer = await stripe.customers.update(stripeCustomerId, customerPayload);
    } else {
      // If we DON'T have a customer ID, first check Stripe by email to prevent duplicates.
      const existingCustomers = await stripe.customers.list({
        email,
        limit: 1,
      });

      if (existingCustomers.data.length > 0) {
        // A customer with this email already exists in Stripe.
        customer = existingCustomers.data[0];
        // Update this existing customer with potentially new details.
        customer = await stripe.customers.update(customer.id, customerPayload);
      } else {
        // No customer found in our DB or in Stripe by email. It's safe to create a new one.
        customer = await stripe.customers.create(customerPayload as Stripe.CustomerCreateParams);
      }
      
      // Sync the definitive Stripe customer ID back to our database.
      const { error: updateError } = await supabase
        .from('clients')
        .update({ stripe_customer_id: customer.id })
        .eq('id', clientId);

      if (updateError) throw updateError;
    }

    return new Response(
      JSON.stringify({ customer }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error syncing Stripe customer:', error);
    return new Response(
      JSON.stringify({ error: error.message || 'Failed to sync customer' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
