import React from 'react';

const LegalSection: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-6">
    <h2 className="text-2xl font-semibold mb-3 text-black">{title}</h2>
    <div className="space-y-4 text-gray-700">{children}</div>
  </div>
);

const PrivacyPolicyPage: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto bg-white border border-gray-300 p-8 sm:p-12">
      <h1 className="text-4xl font-bold text-center mb-8 text-black">Privacy Policy</h1>
      <p className="text-sm text-gray-500 text-center mb-10">Last updated: {new Date().toLocaleDateString()}</p>
      
      <LegalSection title="1. Introduction">
        <p>
          Voice AI Space ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our services.
        </p>
        <p>
          This is a placeholder document. Please replace this text with your official Privacy Policy.
        </p>
      </LegalSection>

      <LegalSection title="2. Information We Collect">
        <p>
          We may collect personal information that you provide to us directly, such as when you create an account, purchase our services, or contact us. This information may include:
        </p>
        <ul className="list-disc list-inside space-y-2">
          <li>Company and contact information (name, email, phone number, address)</li>
          <li>Billing and payment information</li>
          <li>Account credentials (username, password)</li>
        </ul>
      </LegalSection>

      <LegalSection title="3. How We Use Your Information">
        <p>
          We use the information we collect to:
        </p>
         <ul className="list-disc list-inside space-y-2">
          <li>Provide, operate, and maintain our Services</li>
          <li>Process your transactions and manage your orders</li>
          <li>Improve, personalize, and expand our Services</li>
          <li>Communicate with you, including for customer service and marketing purposes</li>
          <li>Prevent fraudulent activity and enhance security</li>
        </ul>
      </LegalSection>
      
      <LegalSection title="4. Data Security">
        <p>
          We implement a variety of security measures to maintain the safety of your personal information. However, no electronic transmission or storage is 100% secure, and we cannot guarantee absolute security.
        </p>
      </LegalSection>
      
      <LegalSection title="5. Third-Party Services">
        <p>
          We may use third-party services, such as payment processors, to facilitate our Services. These third parties have their own privacy policies and we recommend you review them.
        </p>
      </LegalSection>
      
      <LegalSection title="6. Your Data Rights">
        <p>
          Depending on your location, you may have certain rights regarding your personal information, such as the right to access, correct, or delete your data. Please contact us to make such a request.
        </p>
      </LegalSection>

       <LegalSection title="7. Changes to This Policy">
        <p>
          We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page. You are advised to review this Privacy Policy periodically for any changes.
        </p>
      </LegalSection>
    </div>
  );
};

export default PrivacyPolicyPage;