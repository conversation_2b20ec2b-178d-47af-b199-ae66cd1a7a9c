import { create } from 'zustand';
import { supabase } from '@/lib/supabaseClient';
import { queryClient } from '@/lib/queryClient';
import type { RealtimeChannel } from '@supabase/supabase-js';
import { ConversationService, MessageService } from '@/lib/supabaseService';
import type { Message, Conversation } from '@/types';
import type { DatabaseMessage, DatabaseNotification } from '@/types/database';
import { transformDatabaseMessageToMessage } from '@/lib/dataTransformers';
import { useAuthStore } from '@/stores/useAuthStore';
import { useToastStore } from '@/stores/useToastStore';

// Module-level variables to hold the channel subscriptions
let messageChannel: RealtimeChannel | null = null;
let conversationChannel: RealtimeChannel | null = null;
let notificationChannel: RealtimeChannel | null = null;

interface MessagingState {
  // Client messaging state is handled in auth store through client data
}

interface MessagingActions {
  sendMessage: (conversationId: string, content: string, clientId: string) => Promise<boolean>;
  startNewConversation: (
    subject: string,
    initialMessage: string,
    clientId: string
  ) => Promise<Conversation | null>;
  markConversationAsRead: (conversationId: string) => Promise<boolean>;
  subscribeToUpdates: () => void;
  unsubscribeFromUpdates: () => void;
}

type MessagingStore = MessagingState & MessagingActions;

export const useMessagingStore = create<MessagingStore>((set, get) => ({
  // Actions
  sendMessage: async (conversationId: string, content: string, clientId: string) => {
    const newMessage: Message & { conversationId: string } = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      sender: 'client',
      content,
      createdAt: new Date().toISOString(),
      conversationId: conversationId
    };

    const createdMessage = await MessageService.create(newMessage);

    const conversationUpdated = await ConversationService.update(conversationId, {
      lastUpdated: newMessage.createdAt,
      adminHasUnread: true
    });

    if (!createdMessage || !conversationUpdated) {
      console.error('Could not send message');
      return false;
    }

    // Optimistically update the state in the auth store
    const { user, setUser } = useAuthStore.getState();
    if (user.data && user.data.id === clientId) {
      const updatedConversations = user.data.conversations?.map(convo =>
        convo.id === conversationId
          ? {
              ...convo,
              messages: [...convo.messages, createdMessage],
              lastUpdated: newMessage.createdAt,
              adminHasUnread: true,
              clientHasUnread: false // Client just sent a message, so it's read for them
            }
          : convo
      );
      setUser({ ...user, data: { ...user.data, conversations: updatedConversations } });
    }

    return true; // Return success
  },

  startNewConversation: async (subject: string, initialMessage: string, clientId: string) => {
    const newConversation: Omit<Conversation, 'messages'> = {
      id: `conv-${Date.now()}`,
      subject,
      clientId: clientId,
      lastUpdated: new Date().toISOString(),
      status: 'open',
      clientHasUnread: false,
      adminHasUnread: true
    };

    const initialMessageWithId: Message & { conversationId: string } = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      sender: 'client',
      content: initialMessage,
      createdAt: newConversation.lastUpdated,
      conversationId: newConversation.id,
    };

    const createdConversation = await ConversationService.create(newConversation);
    if (!createdConversation) {
      console.error('Could not start conversation');
      return null;
    }
    
    initialMessageWithId.conversationId = createdConversation.id;

    const createdMessage = await MessageService.create(initialMessageWithId);

    if (!createdMessage) {
      console.error('Could not start conversation');
      return null;
    }

    const newConversationWithMsg = { ...createdConversation, messages: [createdMessage] };

    // Optimistically update the state in the auth store
    const { user, setUser } = useAuthStore.getState();
    if (user.data && user.data.id === clientId) {
      const updatedConversations = [...(user.data.conversations || []), newConversationWithMsg];
      setUser({ ...user, data: { ...user.data, conversations: updatedConversations } });
    }
    return newConversationWithMsg;
  },

  markConversationAsRead: async (conversationId: string) => {
    const success = await ConversationService.markAsRead(conversationId, true);
    if (!success) {
      console.error('Could not mark conversation as read');
      return false;
    }

    // Optimistically update the state in the auth store
    const { user, setUser } = useAuthStore.getState();
    if (user.data) {
      const updatedConversations = user.data.conversations?.map(convo =>
        convo.id === conversationId ? { ...convo, clientHasUnread: false } : convo
      );
      setUser({ ...user, data: { ...user.data, conversations: updatedConversations } });
    }

    return true; // Return success
  },

  subscribeToUpdates: () => {
    const { user } = useAuthStore.getState();
    const client = user.data;
    if (!supabase || !client?.id) return;
    
    // Ensure no duplicate subscriptions
    get().unsubscribeFromUpdates(); 

    const conversationIds = (client.conversations || []).map(c => c.id);

    if (conversationIds.length > 0) {
      messageChannel = supabase
        .channel(`messages-for-client-${client.id}`)
        .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=in.(${conversationIds.join(',')})`
        }, (payload) => {
            const dbMessage = payload.new as DatabaseMessage;
            if (dbMessage.sender === 'admin') {
                const newMessage = transformDatabaseMessageToMessage(dbMessage);
                useAuthStore.getState().handleIncomingMessage(newMessage, dbMessage.conversation_id);
            }
        });
    }
    
    conversationChannel = supabase
        .channel(`conversations-for-client-${client.id}`)
        .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'conversations',
            filter: `client_id=eq.${client.id}`
        }, () => {
            queryClient.invalidateQueries({ queryKey: ['clients', 'user', client.user_id] });
        });
        
    notificationChannel = supabase
        .channel(`notifications-for-client-${client.id}`)
        .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `client_id=eq.${client.id}`
        }, (payload) => {
            const newNotification = payload.new as DatabaseNotification;
            useToastStore.getState().showToast(`New notification: ${newNotification.message}`, 'success');
        });

    // Start subscriptions after defining them
    messageChannel?.subscribe();
    conversationChannel.subscribe();
    notificationChannel.subscribe();
  },

  unsubscribeFromUpdates: () => {
    if (messageChannel) {
      supabase.removeChannel(messageChannel);
      messageChannel = null;
    }
    if (conversationChannel) {
      supabase.removeChannel(conversationChannel);
      conversationChannel = null;
    }
    if (notificationChannel) {
      supabase.removeChannel(notificationChannel);
      notificationChannel = null;
    }
  },
}));